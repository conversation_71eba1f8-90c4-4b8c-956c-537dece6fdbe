package com.probim.bimenew.fragment.model;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.LoadModelViewActivity;
import com.probim.bimenew.adapter.ModelRvAdapter;
import com.probim.bimenew.adapter.ModelRvAdapter.OnRecycleItemListener;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.ModelStageBinder;
import com.probim.bimenew.dto.ModelStageDto;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.model.ShareModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.share.ShareBean;
import com.probim.bimenew.share.ShareCallBack;
import com.probim.bimenew.share.UMShareUtils;
import com.probim.bimenew.utils.Base64Utils;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.popwindow.SharePopWindow;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import com.umeng.socialize.bean.SHARE_MEDIA;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

/**
 * Description :模型分类
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/9/17:19.
 */
public class ModelStageFragment extends BaseFragment {

    private final List<ModelSatgeListModel> modelList = new ArrayList<>();
    @BindView(R.id.rv_project)
    RecyclerView rvProject;
    Unbinder unbinder;
    @BindView(R.id.lin_con)
    LinearLayout linCon;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    @BindView(R.id.iv_status)
    ImageView ivStatus;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    private ModelRvAdapter modelAdapter;
    private String projectId;
    private String modelStage;
    private ArrayList<String> stageList = new ArrayList<>();

    private List<ModelSatgeListModel> allModels = new ArrayList<>();

    private List<ModelSatgeListModel> cuurentModels = new ArrayList<>();
    private String organizeId;

    public static ModelStageFragment newInstance(List<String> stageList, List<ModelSatgeListModel> allModels) {
        Bundle args = new Bundle();
        args.putBinder(CustomParam.BigData, new ModelStageBinder(allModels));
        args.putStringArrayList(CustomParam.StageList, new ArrayList<>(stageList));
        ModelStageFragment fragment = new ModelStageFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_model_design, container, false);
        unbinder = ButterKnife.bind(this, view);
        initData();
        return view;
    }

    @Override
    protected void initData() {
        GetProjectDao();
        if (getArguments() != null) {
            ModelStageBinder modelStageBinder = (ModelStageBinder) getArguments().getBinder(CustomParam.BigData);
            allModels = modelStageBinder.getListModels();
            stageList = getArguments().getStringArrayList(CustomParam.StageList);

            for (int i = 0; i < stageList.size(); i++) {
                if (stageList.get(i).equals("allmodel_phase")) {
                    cuurentModels.addAll(allModels);

                } else {

                    for (int j = 0; j < allModels.size(); j++) {
                        if (allModels.get(j).getPhase().equals(stageList.get(i))) {
                            cuurentModels.add(allModels.get(j));
                        }
                    }
                }
            }
            if (cuurentModels.size() == 0) {
                noData();
            }
            initRecycleview();
        }
    }


    @Override
    protected void initRecycleview() {
        //为 RecyclerView 设置布局管理器
        rvProject.setLayoutManager(new LinearLayoutManager(getActivity()));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvProject.addItemDecoration(new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL));
        //动画
        rvProject.setItemAnimator(new DefaultItemAnimator());

        modelAdapter = new ModelRvAdapter(getActivity(), cuurentModels);

        modelAdapter.addRecycleItemListener(new OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
        /*ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.ModelId, modelSatgeListModel.getID());
        bundle.putString(CustomParam.ProjectId, projectId);
        bundle.putString(CustomParam.ModelName, modelSatgeListModel.getName());
        Intent intent = new Intent(getActivity(), ProjectModelActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);*/
                ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
                Bundle bundle = new Bundle();
                bundle.putString(CustomParam.ModelId, modelSatgeListModel.getFeatureID());
                bundle.putString(CustomParam.ProjectId, organizeId);
                bundle.putString(CustomParam.TvLeft, modelSatgeListModel.getFeatureName());
                Intent intent = new Intent(getActivity(), LoadModelViewActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
            }

            @Override
            public void onLoad(int pos, Object o) {

                ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
                Bundle bundle = new Bundle();
                bundle.putString(CustomParam.ModelId, modelSatgeListModel.getFeatureID());
                bundle.putString(CustomParam.ProjectId, organizeId);
                bundle.putString(CustomParam.TvLeft, "模型");
                Intent intent = new Intent(getActivity(), LoadModelViewActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
            }

            @Override
            public void OnShare(int pos, Object o) {
                ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
                SharePopWindow.getInstance().showPopWindwin(getActivity(), new SharePopWindow.onSelectListener() {
                    @Override
                    public void QQShare(int time) {

                        getShare(modelSatgeListModel.getFeatureID(), false, time + "", SHARE_MEDIA.QQ, modelSatgeListModel);
                    }

                    @Override
                    public void CopyUrl(int time) {

                        getShare(modelSatgeListModel.getFeatureID(), true, time + "", null, modelSatgeListModel);
                    }

                    @Override
                    public void WeChatShare(int time) {

                        getShare(modelSatgeListModel.getFeatureID(), false, time + "", SHARE_MEDIA.WEIXIN, modelSatgeListModel);
                    }

                    @Override
                    public void CircleShare(int time) {

                        getShare(modelSatgeListModel.getFeatureID(), false, time + "", SHARE_MEDIA.WEIXIN_CIRCLE, modelSatgeListModel);
                    }
                });
            }
        });

        rvProject.setAdapter(modelAdapter);
    }

    @Override
    protected void initRefresh() {
        smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshLayout) {
                loadData();
                refreshLayout.finishRefresh(1000);
            }
        });
    }

    @Override
    protected void loadData() {
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    /**
     * 无数据
     */
    private void noData() {
        rvStatusLayout.setVisibility(View.VISIBLE);
        ivStatus.setImageResource(R.mipmap.ic_model_empty);
        tvStatus.setText("暂无数据");
    }

    /**
     * 获取分享
     */
    private void getShare(String mID, boolean isEncrypt, String share_day, SHARE_MEDIA media, ModelSatgeListModel model_share) {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("projectId", organizeId);
        params.put("modelId", mID);
        if (isEncrypt) {
            //私密链接 加密
            params.put("hasRandomPwd", "1");
        } else {
            //第三方分享 不加密
            params.put("hasRandomPwd", "0");
        }

        params.put("daycount", share_day);
        ModelController controller = new ModelController();
        controller.GetModelShare(params, new CallBack<ShareModel>() {
            @Override
            public void onSuccess(ShareModel shareModel) {

                if (shareModel.getData().isHaspwd()) {

                    ClipboardManager clipboardManager = (ClipboardManager) getActivity().getSystemService(Context.CLIPBOARD_SERVICE);
                    // 创建ClipData对象
                    ClipData clipData = ClipData.newPlainText("链接", "链接:  " + Hawk.get(CustomParam.Web_URL) + shareModel.getData().getUrl() + "  密码:  " + shareModel.getData().getPwd());
                    // 添加ClipData对象到剪切板中
                    clipboardManager.setPrimaryClip(clipData);

                    showMsg("已经复制到剪切板");
                } else {

                    UMShareUtils.getInstance().share(getActivity(), media, new ShareBean("模型", model_share.getFeatureName(), Base64Utils.decode(model_share.getThumbnail()), Hawk.get(CustomParam.Web_URL) + shareModel.getData().getUrl()), new ShareCallBack() {
                        @Override
                        public void shareSucceed() {
                            showMsg("分享成功");
                        }

                        @Override
                        public void shareFailed(String msg) {
                            showMsg("分享失败");
                        }
                    });
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getProjectID();
        }
    }
}
