package com.probim.bimenew.fragment.project;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

import com.kcrason.dynamicpagerindicatorlibrary.DynamicPagerIndicator;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.TabAdatpter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.dto.ModelStageDto;
import com.probim.bimenew.dto.ModelStageListDto;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.fragment.model.ModelStageFragment;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.model.ModelStages;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.view.NoScrollViewPager;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

/**
 * Description :模型列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/9/15:22.
 */
public class ModelFragment extends BaseFragment {

    List<BaseFragment> fragments = new ArrayList<>();
    List<String> mTitle = new ArrayList<>();
    @BindView(R.id.tab_title)
    DynamicPagerIndicator tabTitle;
    @BindView(R.id.vp_content)
    NoScrollViewPager vpContent;
    Unbinder unbinder;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.img_right)
    ImageView imgRight;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    @BindView(R.id.tv_status)
    TextView tvStatus;

    private List<ModelSatgeListModel> allModels = new ArrayList<>();

    private List<ModelSatgeListModel> tempModels = new ArrayList<>();
    private List<ModelStageDto> allStages = new ArrayList<>();

    private List<ModelStageListDto> allFilterData = new ArrayList<>();

    public static ModelFragment newInstance(String projectId, String projectName, boolean au, List<ModelStageDto> allStages) {
        ModelFragment fragment = new ModelFragment();
        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ProjectName, projectName);
        args.putBoolean(CustomParam.ModelAuthority, au);
        args.putSerializable(CustomParam.AllStages, (Serializable) allStages);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_model, container, false);
        unbinder = ButterKnife.bind(this, view);
        initData();
        return view;
    }

    @Override
    protected void initData() {
        if (getArguments() != null) {
            allStages = (List<ModelStageDto>) getArguments().getSerializable(CustomParam.AllStages);
        }
    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void loadData() {
        if (getArguments() != null) {
            String projectId = getArguments().getString(CustomParam.ProjectId);
            String projectName = getArguments().getString(CustomParam.ProjectName);
            boolean modelAuthority = getArguments().getBoolean(CustomParam.ModelAuthority);
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(projectName);
            if (modelAuthority) {
                getAllModels(projectId);
            } else {
                rvStatusLayout.setVisibility(View.VISIBLE);
                tvStatus.setText("暂无权限");
            }
        }
    }

    /***
     * 填充fragment
     */
    private void initFragmentCount(List<String> titleList, List<BaseFragment> fragmentList) {
        TabAdatpter vpAdapter = new TabAdatpter(getChildFragmentManager(), fragmentList, titleList);
        vpContent.setAdapter(vpAdapter);
        vpContent.setOffscreenPageLimit(fragments.size());
        tabTitle.setViewPager(vpContent);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    /**
     * 获取所有模型
     *
     * @param projectId
     */
    private void getAllModels(String projectId) {
        ModelController controller = new ModelController();
        HashMap<String, String> params = new HashMap<>();
        params.put("VaultID", projectId);
        mLoading.show();
        controller.getAllList(params, new CallBack<List<ModelSatgeListModel>>() {
            @Override
            public void onSuccess(List<ModelSatgeListModel> modelSatgeListModels) {
                allModels.addAll(modelSatgeListModels);
                Hawk.put(CustomParam.AllModel,allModels);
                if (!allModels.isEmpty()) {
                    handleData();
                }

                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });
    }

    /**
     * 处理模型数据
     */
    private void handleData() {
        for (ModelStageDto stageDto : allStages) {
            mTitle.add(stageDto.getName());
            fragments.add(ModelStageFragment.newInstance(stageDto.getData(), allModels));
        }
        initFragmentCount(mTitle, fragments);
    }
       /* for (int i = 0; i < allModels.size(); i++) {
            for (int j = 0; j <allStages.size(); j++) {
                ModelStageListDto modelStageListDto = new ModelStageListDto();
                modelStageListDto.setName(allStages.get(j).getCode());
                for (int k = 0; k < allStages.get(j).getData().size(); k++) {
                    if (allModels.get(i).getPhase() == allStages.get(j).getData().get(i)){
                        modelStageListDto.getData().add(allModels.get(i));
                    }
                }
            }
        }
        Logger.t("allFilterData").e(JsonHelper.toJson(allFilterData));*/


    @OnClick({R.id.iv_back, R.id.lin_back, R.id.img_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                getActivity().finish();
                break;
            case R.id.lin_back:
                getActivity().finish();
                break;
            case R.id.img_right:
                showMsg("搜索");
                break;
        }
    }
}
