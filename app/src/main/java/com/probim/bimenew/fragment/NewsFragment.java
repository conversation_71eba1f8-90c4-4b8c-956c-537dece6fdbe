package com.probim.bimenew.fragment;

import com.probim.bimenew.R;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

/**
 * Description : Author : <PERSON> : <EMAIL> Date : 2018/6/12/17:06.
 */

public class NewsFragment extends BaseFragment {

    private static final int REQUEST_CODE_CHOOSE = 23;
    private int SELECTER_FILE = 101;
    private static NewsFragment instance = null;

    public static NewsFragment getInstance() {
        if (instance == null) {
            instance = new NewsFragment();
        }
        return instance;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
        @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_news, container, false);
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void loadData() {
        // toSelectorFile();
        toSelectorPhoto();
    }

    private void toSelectorPhoto() {
        /*
            Matisse.from(getActivity())
        .choose(MimeType.ofAll(), false).theme(R.style.Matisse_Dracula)
        .countable(true)
        .capture(true)
        .captureStrategy(
            new CaptureStrategy(true, "com.probim.BIMe.fileprovider"))
        .maxSelectable(1)
        .cropStyle(Style.RECTANGLE)
        .isCropSaveRectangle(true).cropOutPutX(1000).cropOutPutY(750)
        .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
        .gridExpectedSize(
            getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
        .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
        .thumbnailScale(0.85f)
        .imageEngine(new GlideEngine())
        .forResult(REQUEST_CODE_CHOOSE);
          }*/
    }

    /***
     * 选择文件
     */
    private void toSelectorFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        // intent.setType(“image/*”);//选择图片
        // intent.setType(“audio/*”); //选择音频
        // intent.setType(“video/*”); //选择视频 （mp4 3gp 是android支持的视频格式）
        // intent.setType(“video/*;image/*”);//同时选择视频和图片
        intent.setType("*/*");// 无类型限制
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        startActivityForResult(intent, SELECTER_FILE);
    }

    /*@Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == REQUEST_CODE_CHOOSE && resultCode == RESULT_OK) {
    
      // 原文件
      File file = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(0));
    
      // 压缩后的文件         （多个文件压缩可以循环压缩）
      File file1 = CompressHelper.getDefault(getApplicationContext()).compressToFile(file);
    
      if (file != null) {
        desPath = file.getAbsolutePath();
      }
    
      if (file1 != null) {
        compressPath = file1.getAbsolutePath();
      }
    
      switch (position) {
        case 1:
          mOnePath = compressPath;
          Glide.with(this)
              .load(desPath)
              .into(ivRegisterd);
          break;
        case 2:
          mTwoPath = compressPath;
          Glide.with(this)
              .load(desPath)
              .into(ivVerify);
          break;
        case 3:
          mThreePath = compressPath;
          Glide.with(this)
              .load(desPath)
              .into(ivLigongVerify);
          break;
        *//*case 4:
            mFourPath = compressPath;
            
            break;*//*
                      }
                      
                      desPath = "";
                      compressPath = "";
                      //      UploadAli(file1);
                      }
                      
                      if (requestCode == SELECTER_FILE && resultCode == Activity.RESULT_OK) {
                      //选择文件
                      Uri uri = data.getData();
                      *//*if ("file".equalsIgnoreCase(uri.getScheme())) {//使用第三方应用打开
                          path = uri.getPath();
                          return;
                          }*//*
                               if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT) {//4.4以后
                               path = FileUtils.getPath(this, uri);
                               } else {//4.4以下下系统调用方法
                               path = FileUtils.getRealPathFromURI(this, uri);
                               }
                               
                               if (path == null) {
                               tvFileName.setVisibility(View.GONE);
                               ivStartupVerify.setVisibility(View.VISIBLE);
                               MyToastUtil.showFast(this, "选择文件格式必须为pdf、word、ppt");
                               return;
                               }
                               
                               if (path.endsWith(".pdf") ||
                                path.endsWith(".docx") || path.endsWith(".doc") || path.endsWith(".pptx") || path.endsWith(".ppt")) {
                               File file = new File(path);
                               mFourPath = file.getName();
                               Log.i("test", "onActivityResult: " + mFourPath);
                               tvFileName.setVisibility(View.VISIBLE);
                               ivStartupVerify.setVisibility(View.GONE);
                               tvFileName.setText(mFourPath);
                               } else {
                               MyToastUtil.showFast(this, "选择文件格式必须为pdf、word、ppt");
                               tvFileName.setVisibility(View.GONE);
                               ivStartupVerify.setVisibility(View.VISIBLE);
                               return;
                               }
                               }
                               
                               }
                               */

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        /* super.onActivityResult(requestCode, resultCode, data);
         if (requestCode == REQUEST_CODE_CHOOSE && resultCode == Activity.RESULT_OK) {
        
           // 原文件
           File file = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(0));
        
           // 压缩后的文件         （多个文件压缩可以循环压缩）
           File file1 = CompressHelper.getDefault(getActivity()).compressToFile(file);
        
           Logger.t("文件路径").e(file.getAbsolutePath());
        
         }*/
    }
}
