package com.probim.bimenew.fragment.record;

import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.appcompat.app.AlertDialog;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.Chronometer;
import android.widget.ImageView;
import android.widget.Toast;

import com.probim.bimenew.R;
import com.probim.bimenew.utils.RecordingService;
import java.io.File;

/**
 * 开始录音的 DialogFragment
 *
 * Created by developerHaoz on 2017/8/12.
 */

public class RecordAudioDialogFragment extends DialogFragment {

  private int mRecordPromptCount = 0;

  private boolean mStartRecording = true;
  private boolean mPauseRecording = true;

  long timeWhenPaused = 0;

  private Button mFabRecord;
  private Chronometer mChronometerTime;
  private ImageView mIvClose;

  private OnAudioCancelListener mListener;

  public static RecordAudioDialogFragment newInstance() {
    RecordAudioDialogFragment dialogFragment = new RecordAudioDialogFragment();
    Bundle bundle = new Bundle();
    dialogFragment.setArguments(bundle);
    return dialogFragment;
  }

  @Override
  public void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
  }

  @Override
  public void onActivityCreated(Bundle savedInstanceState) {
    super.onActivityCreated(savedInstanceState);
  }

  @NonNull
  @Override
  public Dialog onCreateDialog(Bundle savedInstanceState) {
    Dialog dialog = super.onCreateDialog(savedInstanceState);
    final AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
    View view = getActivity().getLayoutInflater().inflate(R.layout.fragment_record_audio, null);
    initView(view);

    mFabRecord.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        onRecord(mStartRecording);
        mStartRecording = !mStartRecording;
      }
    });

    mIvClose.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        mListener.onCancel();
      }
    });

    builder.setCancelable(false);
    builder.setView(view);
    return builder.create();
  }

  private void initView(View view) {
    mChronometerTime = (Chronometer) view.findViewById(R.id.record_audio_chronometer_time);
    mFabRecord = view.findViewById(R.id.record_audio_fab_record);
    mIvClose = (ImageView) view.findViewById(R.id.record_audio_iv_close);
  }

  private void onRecord(boolean start) {

    Intent intent = new Intent(getActivity(), RecordingService.class);

    if (start) {
      // start recording

      //mPauseButton.setVisibility(View.VISIBLE);
      Toast.makeText(getActivity(), "开始录音...", Toast.LENGTH_SHORT).show();
      File folder = new File(Environment.getExternalStorageDirectory() + "/SoundRecorder");
      if (!folder.exists()) {
        //folder /SoundRecorder doesn't exist, create the folder
        folder.mkdir();
      }

      //start Chronometer
      mChronometerTime.setBase(SystemClock.elapsedRealtime());
      mChronometerTime.start();

      //start RecordingService
      getActivity().startService(intent);
      //keep screen on while recording
      //            getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

    } else {
      //stop recording
      //mPauseButton.setVisibility(View.GONE);
      mChronometerTime.stop();
      timeWhenPaused = 0;
      Toast.makeText(getActivity(), "录音结束...地址-------------->"+RecordingService.RecordFilePath, Toast.LENGTH_SHORT).show();

      getActivity().stopService(intent);
      //allow the screen to turn off again once recording is finished
      getActivity().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }
  }

  public void setOnCancelListener(OnAudioCancelListener listener) {
    this.mListener = listener;
  }

  @Override
  public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
      @NonNull int[] grantResults) {
    switch (requestCode) {
      case 1:
        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED
            && grantResults[1] == PackageManager.PERMISSION_GRANTED) {
          onRecord(mStartRecording);
        }
        break;
    }
  }

  public interface OnAudioCancelListener {
    void onCancel();
  }
}
