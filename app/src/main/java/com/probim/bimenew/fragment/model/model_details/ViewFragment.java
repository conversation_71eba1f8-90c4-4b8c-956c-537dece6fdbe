package com.probim.bimenew.fragment.model.model_details;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.LoadModelViewActivity;
import com.probim.bimenew.adapter.ModelViewRvAdapter;
import com.probim.bimenew.adapter.ModelViewRvAdapter.OnRecycleItemListener;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.model.ModelViewModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.popwindow.SharePopWindow;
import com.probim.bimenew.utils.view.AutoLoadRecyclerView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import com.umeng.socialize.bean.SHARE_MEDIA;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;


/**
 * Description :模型----->视图
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/16:50.
 */
public class ViewFragment extends BaseFragment {

    @BindView(R.id.rv_project)
    AutoLoadRecyclerView rvProject;
    @BindView(R.id.smartRefreshLayout)
    SmartRefreshLayout smartRefreshLayout;
    Unbinder unbinder;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    private List<ModelViewModel> list = new ArrayList<>();
    private ModelViewRvAdapter adapter;
    private String projectId;
    private String modelId;
    private String organizeId;

    public static ViewFragment newInstance(String projectId, String modelId) {

        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ModelId, modelId);
        ViewFragment fragment = new ViewFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_model_view, container, false);
        unbinder = ButterKnife.bind(this, view);
        initData();
        initRefresh();
        return view;

    }


    @Override
    protected void initData() {
        GetProjectDao();
    }

    @Override
    protected void initRecycleview() {
        //为 RecyclerView 设置布局管理器
        rvProject.setLayoutManager(new LinearLayoutManager(getActivity()));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvProject.addItemDecoration(
                new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL));
        //动画
        rvProject.setItemAnimator(new DefaultItemAnimator());

        adapter = new ModelViewRvAdapter(getActivity(), list);

        rvProject.setAdapter(adapter);

        adapter.addRecycleItemListener(new OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                ModelViewModel model = (ModelViewModel) o;
                Bundle bundle = new Bundle();
                bundle.putString(CustomParam.ProjectId, projectId);
                bundle.putString(CustomParam.ModelId, modelId);
                bundle.putString(CustomParam.ViewId, model.getID());
                bundle.putString(CustomParam.TvLeft, "视图");
                Intent intent = new Intent(getActivity(), LoadModelViewActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
            }

            @Override
            public void onSetDefault(int pos, Object o) {
                ModelViewModel model = (ModelViewModel) o;
                SetDefault(projectId, modelId, model.getID());
            }

            @Override
            public void OnShare(int pos, Object o) {
                ModelViewModel model = (ModelViewModel) o;
                SharePopWindow.getInstance().showPopWindwin(getActivity(), new SharePopWindow.onSelectListener() {
                    @Override
                    public void QQShare(int time) {

                        getShare(false, time + "", SHARE_MEDIA.QQ, model);

                    }

                    @Override
                    public void CopyUrl(int time) {

                        getShare(true, time + "", null, model);

                    }

                    @Override
                    public void WeChatShare(int time) {

                        getShare(false, time + "", SHARE_MEDIA.WEIXIN, model);
                    }

                    @Override
                    public void CircleShare(int time) {

                        getShare(false, time + "", SHARE_MEDIA.WEIXIN_CIRCLE, model);
                    }
                });
            }
        });

    }

    @Override
    protected void initRefresh() {
        smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshLayout) {
                refreshLayout.finishRefresh(1000);
                loadData();
            }
        });
    }

    @Override
    protected void loadData() {
        if (getArguments() != null) {
            projectId = getArguments().getString(CustomParam.ProjectId);
            modelId = getArguments().getString(CustomParam.ModelId);
            ModelController controller = new ModelController();
            HashMap<String, String> params = new HashMap<>();
            params.put("ProjectID", projectId);
            params.put("ModelID", modelId);
            controller.GetView(params, new CallBack<List<ModelViewModel>>() {
                @Override
                public void onSuccess(List<ModelViewModel> modelViewModels) {

                    list.clear();
                    list.addAll(modelViewModels);
                    adapter.notifyDataSetChanged();
                }


                @Override
                public void onFail(String erroMsg) {
                    rvStatusLayout.setVisibility(View.VISIBLE);
                    tvStatus.setText("暂无数据");
                }
            });

        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    private void SetDefault(String pId, String mId, String vId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", pId);
        params.put("ModelID", mId);
        params.put("ViewID", vId);
        ModelController controller = new ModelController();
        controller.SetDefaultView(params, new CallBack<String>() {
            @Override
            public void onSuccess(String s) {
                if ("\"\"".equals(s)) {
                    loadData();

                } else {
                    showMsg(s);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();

        }

    }

    /**
     * 获取分享
     *
     * @"ViewID":viewID,
     * @"ViewpointID":ViewpointID,
     */
    private void getShare(boolean isEncrypt, String share_day, SHARE_MEDIA media, ModelViewModel model_share) {
        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectId", organizeId);
        params.put("modelId", model_share.getModelID());
        params.put("ViewID", model_share.getID());
        if (isEncrypt) {
            //私密链接 加密
            params.put("hasRandomPwd", "1");
        } else {
            //第三方分享 不加密
            params.put("hasRandomPwd", "0");
        }

        params.put("daycount", share_day);
        ModelController controller = new ModelController();


    }


}
