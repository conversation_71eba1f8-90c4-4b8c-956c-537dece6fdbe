package com.probim.bimenew.fragment;

import android.graphics.BitmapFactory;
import android.graphics.Point;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.BounceInterpolator;
import android.view.animation.Interpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.amap.api.maps.AMap;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.Projection;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.LatLngBounds;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.probim.bimenew.R;

import java.util.ArrayList;
import java.util.List;


/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/7/17/15:25
 */
public class CollectionFragment extends BaseFragment implements AMap.OnMapLoadedListener, AMap.OnMarkerClickListener {

    private MapView mapView;
    private AMap aMap;
    private LatLng latLng;
    private LatLng latLng1;
    private List<Marker> markerList;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_collect, null, false);
        mapView = view.findViewById(R.id.map);
        mapView.onCreate(savedInstanceState);
        initMap();
        return view;
    }

    /**
     * 初始化map
     */
    private void initMap() {

        if (aMap == null) {
            aMap = mapView.getMap();
        }

        aMap.setOnMapLoadedListener(this);
        // 绑定marker拖拽事件
        aMap.setOnMarkerClickListener(this);
        initMarkers();
    }

    /**
     * 初始化mark
     */

    private void initMarkers() {
        ArrayList<MarkerOptions> markerOptionList = new ArrayList<MarkerOptions>();
        MarkerOptions markerOption = new MarkerOptions();
        latLng = new LatLng(39.906901, 116.397972);
        markerOption.position(latLng);
        markerOption.title("西安市").snippet("西安市：34.341568, 108.940174");
        markerOption.draggable(true);//设置Marker可拖动
        markerOption.icon(BitmapDescriptorFactory.fromBitmap(BitmapFactory
                .decodeResource(getResources(), R.mipmap.project_marker)));
        // 将Marker设置为贴地显示，可以双指下拉地图查看效果
        markerOption.setFlat(true);//设置marker平贴地图效果
        markerOptionList.add(markerOption);
        MarkerOptions markerOption1 = new MarkerOptions();
        latLng1 = new LatLng(40.906901, 120.397972);
        markerOption1.position(latLng1);
        markerOption1.title("西安市").snippet("西安市：34.341568, 108.940174");
        markerOption1.draggable(true);//设置Marker可拖动
        markerOption1.icon(BitmapDescriptorFactory.fromBitmap(BitmapFactory
                .decodeResource(getResources(), R.mipmap.project_marker)));
        // 将Marker设置为贴地显示，可以双指下拉地图查看效果
        markerOption1.setFlat(true);//设置marker平贴地图效果
        markerOptionList.add(markerOption1);
        markerList = aMap.addMarkers(markerOptionList, true);


    }

    /**
     * marker点击时跳动一下
     */
    public void jumpPoint(final Marker marker) {

        final Handler handler = new Handler();
        final long start = SystemClock.uptimeMillis();
        final LatLng originLatLng = marker.getPosition();
        Projection proj = aMap.getProjection();
        Point startPoint = proj.toScreenLocation(marker.getPosition());
        startPoint.offset(0, -100);
        final LatLng startLatLng = proj.fromScreenLocation(startPoint);
        final long duration = 1500;

        final Interpolator interpolator = new BounceInterpolator();
        handler.post(new Runnable() {
            @Override
            public void run() {
                long elapsed = SystemClock.uptimeMillis() - start;
                float t = interpolator.getInterpolation((float) elapsed
                        / duration);
                double lng = t * originLatLng.longitude + (1 - t)
                        * startLatLng.longitude;
                double lat = t * originLatLng.latitude + (1 - t)
                        * startLatLng.latitude;
                marker.setPosition(new LatLng(lat, lng));
                if (t < 1.0) {
                    handler.postDelayed(this, 16);
                }
            }
        });

    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        //在activity执行onDestroy时执行mMapView.onDestroy()，销毁地图I
        mapView.onDestroy();
    }


    @Override
    public void onResume() {
        super.onResume();
        //在activity执行onResume时执行mMapView.onResume ()，重新绘制加载地图
        mapView.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        //在activity执行onPause时执行mMapView.onPause ()，暂停地图的绘制
        mapView.onPause();
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        //在activity执行onSaveInstanceState时执行mMapView.onSaveInstanceState (outState)，保存地图当前的状态
        mapView.onSaveInstanceState(outState);
    }

    @Override
    public void onMapLoaded() {
        // 设置所有maker显示在当前可视区域地图中
        LatLngBounds bounds = new LatLngBounds.Builder().include(latLng).include(latLng1).build();
        aMap.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, 300));
    }


    @Override
    public boolean onMarkerClick(Marker marker) {
        if (aMap != null) {
            jumpPoint(marker);
        }

        return false;
    }


}
