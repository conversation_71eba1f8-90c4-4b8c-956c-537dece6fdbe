package com.probim.bimenew.fragment.position;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.LoadModelViewActivity;
import com.probim.bimenew.activity.PointViewSelectedForNewQuestionActivity;
import com.probim.bimenew.activity.TwoDimensionalForNewQuestionActivity;
import com.probim.bimenew.adapter.ModelRvAdapter;
import com.probim.bimenew.adapter.ModelRvAdapter.OnRecycleItemListener;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.popwindow.SharePopWindow;
import com.probim.bimenew.utils.view.AutoLoadRecyclerView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import com.umeng.socialize.bean.SHARE_MEDIA;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :模型分类
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/9/17:19.
 */
public class ModelStageSelectForNewQuestionFragment extends BaseFragment {

  @BindView(R.id.rv_project)
  AutoLoadRecyclerView rvProject;
  Unbinder unbinder;
  @BindView(R.id.lin_con)
  LinearLayout linCon;
  @BindView(R.id.smartRefreshLayout)
  SmartRefreshLayout smartRefreshLayout;
  @BindView(R.id.iv_status)
  ImageView ivStatus;
  @BindView(R.id.tv_status)
  TextView tvStatus;
  @BindView(R.id.rv_status_layout)
  RelativeLayout rvStatusLayout;
  private ModelRvAdapter adapter;
  private List<ModelSatgeListModel> list = new ArrayList<>();
  private String projectId;
  private String modelStage;
  private ArrayList<String> stageList = new ArrayList<>();
  private String organizeId;

  public static ModelStageSelectForNewQuestionFragment newInstance(String projectId,
      String modelStage,
      ArrayList<String> stageList) {
    Bundle args = new Bundle();
    args.putString(CustomParam.ProjectId, projectId);
    args.putString(CustomParam.ModelStage, modelStage);
    args.putStringArrayList(CustomParam.StageList, stageList);
    ModelStageSelectForNewQuestionFragment
        fragment = new ModelStageSelectForNewQuestionFragment();
    fragment.setArguments(args);
    return fragment;
  }

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
      @Nullable Bundle savedInstanceState) {
    View view = inflater.inflate(R.layout.fragment_model_design, container, false);
    unbinder = ButterKnife.bind(this, view);
    initData();
    return view;
  }

  @Override
  protected void initData() {
    GetProjectDao();
  }

  @Override
  protected void initRecycleview() {
    //为 RecyclerView 设置布局管理器
    rvProject.setLayoutManager(new LinearLayoutManager(getActivity()));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvProject.addItemDecoration(
        new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL));
    //动画
    rvProject.setItemAnimator(new DefaultItemAnimator());

    adapter = new ModelRvAdapter(getActivity(), list);

    adapter.addRecycleItemListener(new OnRecycleItemListener() {
      @Override
      public void OnRecycleItemClick(int pos, Object o) {
        ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.ModelId, modelSatgeListModel.getFeatureID());
        bundle.putString(CustomParam.ProjectId, projectId);
        Intent intent = null;

        if (ApiConstant.viewPointOrTwoDis) {

          intent = new Intent(getActivity(), PointViewSelectedForNewQuestionActivity.class);

        } else {

          intent = new Intent(getActivity(), TwoDimensionalForNewQuestionActivity.class);
        }

        intent.putExtras(bundle);
        startActivity(intent);
        getActivity().finish();
      }

      @Override
      public void onLoad(int pos, Object o) {

        ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.ModelId, modelSatgeListModel.getFeatureID());
        bundle.putString(CustomParam.ProjectId, projectId);
        bundle.putString(CustomParam.TvLeft, "模型");
        Intent intent = new Intent(getActivity(), LoadModelViewActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
      }

      @Override
      public void OnShare(int pos, Object o) {
        ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
        SharePopWindow.getInstance()
            .showPopWindwin(getActivity(), new SharePopWindow.onSelectListener() {
              @Override
              public void QQShare(int time) {

                getShare(modelSatgeListModel.getFeatureID(), false, time + "", SHARE_MEDIA.QQ,
                    modelSatgeListModel);
              }

              @Override
              public void CopyUrl(int time) {

                getShare(modelSatgeListModel.getFeatureID(), true, time + "", null, modelSatgeListModel);
              }

              @Override
              public void WeChatShare(int time) {

                getShare(modelSatgeListModel.getFeatureID(), false, time + "", SHARE_MEDIA.WEIXIN,
                    modelSatgeListModel);
              }

              @Override
              public void CircleShare(int time) {

                getShare(modelSatgeListModel.getFeatureID(), false, time + "", SHARE_MEDIA.WEIXIN_CIRCLE,
                    modelSatgeListModel);
              }
            });
      }
    });

    rvProject.setAdapter(adapter);
  }

  @Override
  protected void initRefresh() {
    smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
      @Override
      public void onRefresh(RefreshLayout refreshLayout) {
        loadData();
        refreshLayout.finishRefresh(1000);
      }
    });
  }

  @Override
  protected void loadData() {
    if (getArguments() != null) {
      list.clear();
      projectId = getArguments().getString(CustomParam.ProjectId);
      modelStage = getArguments().getString(CustomParam.ModelStage);
      stageList = getArguments().getStringArrayList(CustomParam.StageList);
      ModelController controller = new ModelController();
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      controller.GetStageList(params, new CallBack<List<ModelSatgeListModel>>() {
        @Override
        public void onSuccess(List<ModelSatgeListModel> modelSatgeListModels) {
          List<ModelSatgeListModel> moreList = new ArrayList<>();
          List<ModelSatgeListModel> firstList = new ArrayList<>();
          for (int i = 0; i < modelSatgeListModels.size(); i++) {

            if (modelSatgeListModels.get(i).getPhase().equals(modelStage) && stageList.contains(
                modelSatgeListModels.get(i).getPhase())) {
              list.add(modelSatgeListModels.get(i));

              if (modelSatgeListModels.get(i).getPhase().equals(stageList.get(0))) {

                firstList.add(modelSatgeListModels.get(i));
              }
              adapter.notifyDataSetChanged();
            } else {
              if (stageList
                  .get(0).equals(modelStage) && !stageList.contains(
                  modelSatgeListModels.get(i).getPhase())) {

                list.add(modelSatgeListModels.get(i));
              }
            }
          }
          if (list.size() == 0) {
            noData();
          }
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    unbinder.unbind();
  }

  /**
   * 无数据
   */
  private void noData() {
    rvStatusLayout.setVisibility(View.VISIBLE);
    ivStatus.setImageResource(R.mipmap.ic_model_empty);
    tvStatus.setText("暂无模型，请在WEB端添加模型");
  }

  /**
   * 获取分享
   */
  private void getShare(String mID, boolean isEncrypt, String share_day, SHARE_MEDIA media,
      ModelSatgeListModel model_share) {
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectId", organizeId);
    params.put("modelId", mID);
    if (isEncrypt) {
      //私密链接 加密
      params.put("hasRandomPwd", "1");
    } else {
      //第三方分享 不加密
      params.put("hasRandomPwd", "0");
    }

    params.put("daycount", share_day);
    ModelController controller = new ModelController();

  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      organizeId = bean.getBimProjectId();
    }
  }
}
