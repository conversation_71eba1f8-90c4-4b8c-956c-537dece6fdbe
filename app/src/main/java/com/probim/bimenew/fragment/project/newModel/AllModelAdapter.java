package com.probim.bimenew.fragment.project.newModel;

import android.graphics.Color;
import android.text.TextUtils;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.dto.ModelStageDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.model.ModelSatgeListModel;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class AllModelAdapter extends RecyclerView.Adapter<AllModelAdapter.ViewHolder> {

    private List<ModelSatgeListModel> dtoList = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;
    private List<Boolean> allClicks = new ArrayList<>();

    public AllModelAdapter(List<ModelSatgeListModel> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_model, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ModelSatgeListModel dto = dtoList.get(position);
        holder.tvName.setText(dto.getFeatureName());
        holder.tvDate.setText(dto.getCreateTime().replace("T", " ").split("\\.")[0]);
        if (TextUtils.isEmpty(dto.getThumbnail())) {

            Glide.with(holder.itemView.getContext())
                    .load(R.mipmap.img_bg_project_list)
                    .into(holder.ivPhoto);

        } else {
            byte[] imageByteArray = Base64.decode(dto.getThumbnail(), Base64.DEFAULT);
            Glide.with(holder.itemView.getContext())
                    .load(imageByteArray)
                    .into(holder.ivPhoto);

        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_name)
        TextView tvName;
        @BindView(R.id.tv_date)
        TextView tvDate;
        @BindView(R.id.iv_photo)
        ImageView ivPhoto;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
