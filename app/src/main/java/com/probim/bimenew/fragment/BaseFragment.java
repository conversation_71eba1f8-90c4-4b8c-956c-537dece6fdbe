package com.probim.bimenew.fragment;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.event.EventBusUtils;
import com.probim.bimenew.utils.BindEventBus;
import com.probim.bimenew.utils.loading.LoadingProgressDialog;

import java.util.List;

import butterknife.Unbinder;

/**
 * Description :Fragment 基类
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/5/29/16:29.
 */

public abstract class BaseFragment extends LazyLoadFragment {

    public Unbinder unbinder;
    public LoadingProgressDialog mLoading;
    //protected StateView mStateView;//用于显示加载中、网络异常，空布局、内容布局
    protected Activity mActivity;
    private View rootView;
    public String organizeId;
    public String schedualOrganizeId;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //mLoading = LoadingView.createLoading(getActivity());
        mLoading = new LoadingProgressDialog(getActivity());
        getProjectDao();
    }

/*  @Override
  public View onCreateView(LayoutInflater inflater, ViewGroup container,
      Bundle savedInstanceState) {
    if (rootView == null) {
      rootView = inflater.inflate(provideContentViewId(), container, false);
      unbinder = ButterKnife.bind(this, rootView);
      initView(rootView);

    } else {
      ViewGroup parent = (ViewGroup) rootView.getParent();
      if (parent != null) {
        parent.removeView(rootView);
      }
    }
    return rootView;
  }*/

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initListener();
        initRecycleview();
        initRefresh();

        // 若使用BindEventBus注解，则绑定EventBus
        if (this.getClass().isAnnotationPresent(BindEventBus.class)) {
            EventBusUtils.register(this);
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mActivity = (Activity) context;
    }

    /**
     * 初始化一些view
     */
    public void initView(View rootView) {
    }

    /**
     * 初始化数据
     */
    protected abstract void initData();

    /**
     * 设置listener的操作
     */
    public void initListener() {

    }

    protected abstract void initRecycleview();

    @Override
    protected void onFragmentFirstVisible() {
        //当第一次可见的时候，加载数据
        loadData();
    }

    protected abstract void initRefresh();
/*

  //得到当前界面的布局文件id(由子类实现)
  protected abstract int provideContentViewId();
*/

    //加载数据
    protected abstract void loadData();

    @Override
    public void onDestroy() {
        super.onDestroy();

        rootView = null;
        //    unbinder.unbind();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (this.getClass().isAnnotationPresent(BindEventBus.class)) {
            EventBusUtils.unregister(this);
        }
    }

    /**
     * [防止快速点击]
     */
    private boolean fastClick() {
        long lastClick = 0;
        if (System.currentTimeMillis() - lastClick <= 1000) {
            return false;
        }
        lastClick = System.currentTimeMillis();
        return true;
    }

    /**
     * [页面跳转]
     */
    public void startActivity(Class<?> clz) {
        startActivity(clz, null);
    }

    /**
     * [携带数据的页面跳转]
     */
    public void startActivity(Class<?> clz, Bundle bundle) {
        Intent intent = new Intent();
        intent.setClass(getActivity(), clz);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        startActivity(intent);
    }

    /**
     * [含有Bundle通过Class打开编辑界面]
     */
    public void startActivityForResult(Class<?> cls, Bundle bundle,
                                       int requestCode) {
        Intent intent = new Intent();
        intent.setClass(getActivity(), cls);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        startActivityForResult(intent, requestCode);
    }

    public void showMsg(String msg) {
        Toast.makeText(getActivity(), msg, Toast.LENGTH_SHORT).show();
    }
    /**
     * 获取token
     *
     * @return
     */
    public String getToken() {
        String token = Hawk.get(CustomParam.Token);
        if (!TextUtils.isEmpty(token)) {
            return token;
        }
        return "";
    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getProjectID();
            schedualOrganizeId = bean.getBimProjectId();

        }
    }

    /**
     * 通过id查找并获取控件，使用时不需要强转
     */
    public <V extends View> V findView(int id) {
        return getActivity().findViewById(id);
    }

    /**
     * 通过id查找并获取控件，并setOnClickListener
     */
    public <V extends View> V findView(int id, View.OnClickListener l) {
        V v = findView(id);
        v.setOnClickListener(l);
        return v;
    }
}
