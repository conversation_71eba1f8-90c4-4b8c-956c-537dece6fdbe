package com.probim.bimenew.fragment.project;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.NewCheckActivity;
import com.probim.bimenew.activity.SearchCheckActivity;
import com.probim.bimenew.activity.check.CheckDetailsActivity;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.adapter.CheckAdapter;
import com.probim.bimenew.adapter.CheckTypeAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.CheckListBean;
import com.probim.bimenew.result.CheckTypeResult;
import com.probim.bimenew.result.DeleteCheckDto;
import com.probim.bimenew.result.DeleteCheckResult;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.dropdownmenu.bean.DropdownItemObject;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownButton;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownListView;
import com.probim.bimenew.utils.flowlayout.FlowLayout;
import com.probim.bimenew.utils.flowlayout.TagAdapter;
import com.probim.bimenew.utils.flowlayout.TagFlowLayout;
import com.probim.bimenew.utils.view.AlertDialogUtil;
import com.probim.bimenew.utils.view.fontview.FontTextView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

/**
 * Description :质量检查
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/11/20/16:09.
 */

public class QualityFragment extends BaseFragment implements DropdownListView.Container {
    private final List<DropdownItemObject> chooseTypeData = new ArrayList<>();
    private final List<DropdownItemObject> chooseStatusData = new ArrayList<>();
    private final List<DropdownItemObject> chooseClassData = new ArrayList<>();
    private final List<CheckListBean.DataBean.ListBean> dataList = new ArrayList<>();
    private final List<CheckTypeResult.DataBean.ListBean> checkTypeResultList = new ArrayList<>();
    private final String sortFieldSelected = "";
    @BindView(R.id.drop_status)
    DropdownButton dropStatus;
    @BindView(R.id.drop_type)
    DropdownButton dropType;
    @BindView(R.id.drop_class)
    DropdownButton dropClass;
    @BindView(R.id.rv_quality)
    RecyclerView rvQuality;
    @BindView(R.id.iv_result)
    ImageView ivResult;
    @BindView(R.id.tv_result)
    TextView tvResult;
    @BindView(R.id.lin_rv_status_layout)
    LinearLayout linRvStatusLayout;
    @BindView(R.id.mask)
    View mask;
    @BindView(R.id.lis_dropdown_status)
    DropdownListView lisDropdownStatus;
    @BindView(R.id.lis_dropdown_type)
    DropdownListView lisDropdownType;
    @BindView(R.id.lis_dropdown_class)
    DropdownListView lisDropdownClass;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    @BindView(R.id.tv_title)
    TextView tvTittle;
    @BindView(R.id.drawer_layout)
    DrawerLayout drawerLayout;
    @BindView(R.id.fragment_header)
    Toolbar toolbar;
    @BindView(R.id.tag_level)
    TagFlowLayout tagLevel;
    @BindView(R.id.tag_status)
    TagFlowLayout tagStatus;
    @BindView(R.id.tag_who)
    TagFlowLayout tagWho;
    @BindView(R.id.rv_filter_check_type)
    RecyclerView rvFilterCheckType;
    @BindView(R.id.btn_check_filter_confirm)
    FontTextView btnFilter;
    @BindView(R.id.lin_title)
    LinearLayout linTitle;
    @BindView(R.id.img_right)
    ImageView ivRight;
    @BindView(R.id.iv_new_check)
    ImageView ivNewCheck;
    Unbinder unbinder;
    Animation dropdown_in, dropdown_out, dropdown_mask_out;
    private String typeSelected = "";
    private String authorSelected = "";
    private DropdownListView currentDropdownList;
    // 定义三个变量
    private String statusSelected = "";
    private String levelSelected = "";
    private String organizeId;
    private CheckAdapter checkAdapter;
    private CheckController checkController;
    private String bimId;
    private CheckTypeAdapter checkTypeAdapter;
    private SmartRefreshLayout smartRefreshLayout;

    private final String LinkType = "1";

    public static QualityFragment newInstance(String projectId, String projectName, Boolean addAu) {
        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ProjectName, projectName);
        args.putBoolean(CustomParam.CheckAuthoruty, addAu);
        QualityFragment fragment = new QualityFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.fragment_check, container, false);
        unbinder = ButterKnife.bind(this, view);
        initAuth();
        initView(view);
        initRecycleview();
        initToolBar();
        return view;
    }

    @Override
    public void initView(View rootView) {
//        tvTittle.setText(getArguments().get(CustomParam.ProjectName).toString());
        tvTittle.setText("");
        linTitle.setVisibility(View.VISIBLE);
        ivRight.setVisibility(View.VISIBLE);
        initLevelFlow();
        initStatusFlow();
        initWhoFlow();
        smartRefreshLayout = rootView.findViewById(R.id.smartRefreshLayout);
        smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshLayout) {
                initData();
                refreshLayout.finishRefresh(1000);
            }
        });
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        GetProjectDao();
        checkController = new CheckController();
        getCheckType();
    }
    //设置item数据大于多少字只显示一行  默认 超过九个字的程度只显示一列

    @Override
    protected void initData() {
        /**
         * keyword (string, optional): 关键字 ,
         * Token (string, optional): 口令 ,
         * bimcomposerId (string, optional): 模型项目ID ,
         * StateType (string, optional): 针对状态的过滤，使用英文逗号来拼接，可选值：A_ToBeCheck（待检查） B_ToBeRectified（待整改） C_ToBeRecheck（待验收） D_Qualified（已合格） ,
         * AuthorType (string, optional): 针对操作人的过滤，传空字符串或以下其中之一，可选值：AsChecker（我检查）AsRechecker（我复检）AsRectifier（我整改） AsLauncher（我发起） ,
         * Severitylevel (string, optional): 针对严重等级的过滤，使用英文逗号来拼接，可选值为：一般、严重、非常严重 ,
         * Types (string, optional): 针对检查类型的过滤，使用英文逗号来拼接，可选值为：Exam_GetExamTypes 接口返回的数据中的 aedt_guid 字段 ,
         * SortField (string, optional): 排序依赖的字段，有：创建时间 CreateDate/''， 结束时间 RectificateDate， 状态 State ,
         * SortIsAsc (string, optional): 传1为正序，其它为倒序 ,
         * Skip (string, optional): 跳过多少条数据。若转换失败则取全部 ,
         * Take (string, optional): 取多少条数据。若 Skip 无效或转换为数字失败则取剩余全部
         *
         *
         */


        //checkAdapter.clear();
        HashMap<String, String> params = new HashMap<>();
        params.put("LinkType", LinkType);
        params.put("OrganizeId", organizeId);
        params.put("KeyWord", "");
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("StateType", statusSelected);
        params.put("Types", typeSelected);
        params.put("Severitylevel", levelSelected);
        params.put("AuthorType", authorSelected);
        params.put("SortField", sortFieldSelected);
        params.put("SortIsAsc", "");
        checkController.getCheckList(params, new CallBack<CheckListBean>() {

            @Override
            public void onSuccess(CheckListBean checkListResult) {
                if (!checkListResult.getData().getList().isEmpty()) {
                    dataList.clear();
                    dataList.addAll(checkListResult.getData().getList());
                    checkAdapter.notifyDataSetChanged();
                    linRvStatusLayout.setVisibility(View.GONE);
                } else {
                    linRvStatusLayout.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void initRecycleview() {

        checkTypeAdapter = new CheckTypeAdapter(getActivity(), checkTypeResultList);
      /*  GridLayoutManager gridLayoutManager = new GridLayoutManager(getActivity(), 4);
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                return checkTypeResultList.get(position).getAedt_name().length() > 2 ? 1 : 4;
            }
        });
        rvFilterCheckType.setLayoutManager(gridLayoutManager);
        rvFilterCheckType.setAdapter(checkTypeAdapter);*/
        VerticalNoItemRecyclerView.initialize(rvFilterCheckType).setAdapter(checkTypeAdapter);
        checkTypeAdapter.setOnItemClickListener(new CheckTypeAdapter.setOnItemClickListener() {
            @Override
            public void onItemClick(int pos) {
                typeSelected = checkTypeResultList.get(pos).getAedtGuid();
            }
        });

        checkAdapter = new CheckAdapter(getActivity(), dataList);
        VerticalNoItemRecyclerView.initialize(rvQuality).setAdapter(checkAdapter);
        checkAdapter.addRecycleItemListener(new CheckAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                Intent intent = new Intent(getActivity(), CheckDetailsActivity.class);
                intent.putExtra(CustomParam.ExamineTittle,
                        checkAdapter.getData().get(pos).getExamineRemark());
                intent.putExtra(CustomParam.Examineid, checkAdapter.getData().get(pos).getExamineID());
                intent.putExtra(CustomParam.ExaminerId, checkAdapter.getData().get(pos).getExaminerID());
                intent.putExtra("LinkType", LinkType);
                startActivity(intent);
            }

            @Override
            public void OnDelete(int pos, Object o) {
                CheckListBean.DataBean.ListBean databean = (CheckListBean.DataBean.ListBean) o;
                if (databean.getExaminerID().equals(Hawk.get(CustomParam.UserId))) {
                    AlertDialogUtil alertDialogUtil = new AlertDialogUtil(getActivity());
                    alertDialogUtil.builder().setMsg("删除后不可恢复").setTitle("确认删除").setNegativeButton("确定",
                            new View.OnClickListener() {
                                @Override
                                public void onClick(View view) {
                                    deleteListItem(checkAdapter.getData().get(pos).getExamineID(), pos);
                                }
                            }).setPositiveButton("取消", new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {

                        }
                    }).show();

                } else {
                    showMsg("暂无权限删除");
                }
            }

            @Override
            public void OnClose(int pos, Object o) {
                CheckListBean.DataBean.ListBean databean = (CheckListBean.DataBean.ListBean) o;
                if (databean.getExaminerID().equals(Hawk.get(CustomParam.UserId))) {
                    closeListItem(checkAdapter.getData().get(pos).getExamineID(), pos);
                } else {
                    showMsg("暂无权限关闭");
                }

            }
        });
    }

    private void initToolBar() {

        ActionBarDrawerToggle toggle = new ActionBarDrawerToggle(getActivity(), drawerLayout, toolbar,
                R.string.app_name, R.string.app_name);
        toggle.setDrawerIndicatorEnabled(false);
        //toggle.setHomeAsUpIndicator(R.drawable.ic_menu_black_24dp);
        //设置toolbar结合drawerLayout
        toggle.syncState();
        drawerLayout.addDrawerListener(toggle);
        //drawerLayout.openDrawer(Gravity.RIGHT, true);
    }

    private void initAuth() {
        if (getArguments() != null) {
            String projectId = getArguments().getString(CustomParam.ProjectId);
            String projectName = getArguments().getString(CustomParam.ProjectName);
            boolean checkAu = getArguments().getBoolean(CustomParam.CheckAuthoruty);
            //imgRight.setVisibility(View.VISIBLE);
            //imgRight.setImageResource(R.mipmap.btn_common_add);
            if (!checkAu) {
                //无权限
                rvStatusLayout.setVisibility(View.VISIBLE);
                tvStatus.setText("暂无权限");
                ivNewCheck.setVisibility(View.GONE);
            }
        }
    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    void init() {
        // 加载动画
        dropdown_in = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_in);
        dropdown_out = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_out);
        dropdown_mask_out = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_mask_out);
        //加载数据

        //StateType (string, optional): 针对状态的过滤
        //A_ToBeCheck（待检查） B_ToBeRectified（待整改） C_ToBeRecheck（待验收） D_Qualified（已合格） ,
        chooseStatusData.add(new DropdownItemObject("状态", 0, "状态"));
        chooseStatusData.add(new DropdownItemObject("待检查", 1, "A_ToBeCheck"));
        chooseStatusData.add(new DropdownItemObject("待整改", 2, "B_ToBeRectified"));
        chooseStatusData.add(new DropdownItemObject("待验收", 2, "C_ToBeRecheck"));
        chooseStatusData.add(new DropdownItemObject("已合格", 2, "D_Qualified"));
        chooseStatusData.add(new DropdownItemObject("已关闭", 2, "E_Closed"));
        // Severitylevel
        chooseClassData.add(new DropdownItemObject("等级", 0, "等级"));
        chooseClassData.add(new DropdownItemObject("一般", 1, "一般"));
        chooseClassData.add(new DropdownItemObject("严重", 2, "严重"));
        chooseClassData.add(new DropdownItemObject("非常严重", 3, "非常严重"));

        mask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hide();
            }
        });
        reset();
        lisDropdownStatus.bind(chooseStatusData, dropStatus, this, 0);

        lisDropdownType.bind(chooseTypeData, dropType, this, 0);

        lisDropdownClass.bind(chooseClassData, dropClass, this, 0);

        dropdown_mask_out.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (currentDropdownList == null) {
                    reset();
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
    }

    void reset() {
        dropStatus.setChecked(false);
        dropType.setChecked(false);
        dropClass.setChecked(false);

        mask.setVisibility(View.GONE);
        lisDropdownType.setVisibility(View.GONE);
        lisDropdownStatus.setVisibility(View.GONE);
        lisDropdownClass.setVisibility(View.GONE);

        lisDropdownType.clearAnimation();
        lisDropdownStatus.clearAnimation();
        lisDropdownClass.clearAnimation();
        mask.clearAnimation();
    }

    @Override
    public void show(DropdownListView listView) {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            currentDropdownList.startAnimation(dropdown_out);
            currentDropdownList.setVisibility(View.GONE);
            currentDropdownList.button.setChecked(false);
        }
        currentDropdownList = listView;
        mask.clearAnimation();
        mask.setVisibility(View.VISIBLE);
        currentDropdownList.clearAnimation();
        currentDropdownList.startAnimation(dropdown_in);
        currentDropdownList.setVisibility(View.VISIBLE);
        currentDropdownList.button.setChecked(true);
    }

    @Override
    public void hide() {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            currentDropdownList.startAnimation(dropdown_out);
            currentDropdownList.button.setChecked(false);
            mask.clearAnimation();
            mask.startAnimation(dropdown_mask_out);
        }
        currentDropdownList = null;
    }

    @Override
    public void onSelectionChanged(DropdownListView view, String itemId) {
        // 状态筛选
        if (lisDropdownStatus == view) {

            if (itemId.equals("状态")) {
                statusSelected = "";
            } else {
                statusSelected = itemId;
            }


        }
        // 严重等级
        if (lisDropdownClass == view) {

            if (itemId.equals("等级")) {
                levelSelected = "";
            } else {
                levelSelected = itemId;
            }


        }
        // 类型筛选
        if (lisDropdownType == view) {

            if (itemId.equals("类别")) {
                typeSelected = "";
            } else {
                typeSelected = itemId;
            }


        }

        checkAdapter.clear();
        initData();

    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getProjectID();
            bimId = bean.getBimProjectId();
        }
    }

    /**
     * 删除检查
     */
    private void deleteListItem(String id, int position) {
        List<String> idList = new ArrayList<>();
        idList.add(id);
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("Ids", JsonHelper.toJson(idList));
        DeleteCheckDto deleteCheckDto = new DeleteCheckDto();
        deleteCheckDto.setToken(Hawk.get(CustomParam.Token));
        deleteCheckDto.setIds(idList);
        checkController.deleteCheckItem(new Gson().toJson(deleteCheckDto), new CallBack<DeleteCheckResult>() {
            @Override
            public void onSuccess(DeleteCheckResult deleteCheckResult) {
                if (1 == deleteCheckResult.getRet()) {
                    showMsg(deleteCheckResult.getMsg());
                    checkAdapter.notifyItemRemoved(position);
                    checkAdapter.getData().remove(position);
                }else {
                    showMsg(deleteCheckResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }


    /**
     * 归档检查
     */
    private void closeListItem(String id, int position) {
        HashMap<String, String> params = new HashMap<>();
        params.put("Ids", id);
        params.put("Token", Hawk.get(CustomParam.Token));
        checkController.closeCheckItem(params, new CallBack<BaseResult>() {
            @Override
            public void onSuccess(BaseResult baseDto) {
                if (1 == baseDto.getRet()) {
                    checkAdapter.notifyItemRemoved(position);
                    checkAdapter.getData().remove(position);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @OnClick({
            R.id.lin_back, R.id.lin_title,
            R.id.tv_title, R.id.iv_new_check, R.id.img_right, R.id.btn_check_filter_confirm
    })
    public void onViewClicked(View view) {
        switch (view.getId()) {

            case R.id.lin_back:
                getActivity().finish();
                break;
            case R.id.lin_title:
                startActivity(SearchCheckActivity.class);
                break;
            case R.id.iv_new_check:
                Intent intent = new Intent(getActivity(), NewCheckActivity.class);
                intent.putExtra("LinkType", LinkType);
                startActivity(intent);
                break;
            case R.id.img_right:
                drawerLayout.openDrawer(GravityCompat.END, true);
                break;
            case R.id.btn_check_filter_confirm:
                //刷新数据
                drawerLayout.closeDrawers();
                checkAdapter.clear();
                initData();
                break;
            default:
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        initData();
    }

    /**
     * 获检查分类
     */
    private void getCheckType() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", organizeId);
        params.put("examType", "quanlity");
        checkController.getCheckType(params, new CallBack<CheckTypeResult>() {
            @Override
            public void onSuccess(CheckTypeResult checkTypeResult) {
                chooseTypeData.add(new DropdownItemObject("类别", 0, "类别"));
                for (CheckTypeResult.DataBean.ListBean dataBean : checkTypeResult.getData().getList()) {
                    chooseTypeData.add(
                            new DropdownItemObject(dataBean.getAedtName(), 1, dataBean.getAedtGuid()));
                }

                init();
                CheckTypeResult.DataBean.ListBean dataBean = new CheckTypeResult.DataBean.ListBean();
                dataBean.setAedtGuid("");
                dataBean.setAedtName("全部");
                checkTypeResultList.add(dataBean);
                checkTypeResultList.addAll(checkTypeResult.getData().getList());
                checkTypeAdapter.notifyDataSetChanged();
                // 填充 点击数据
                setTapList(checkTypeResultList);

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取 点击数据
     *
     * @param typeResultList
     */
    private void setTapList(List<CheckTypeResult.DataBean.ListBean> typeResultList) {
        List<Boolean> booleanList = new ArrayList<>();
        for (int i = 0; i < typeResultList.size(); i++) {
            booleanList.add(false);
        }

        checkTypeAdapter.setTapListData(booleanList);


    }

    /**
     * 初始化相关数据
     */
    private void initWhoFlow() {

        List<String> stringList = new ArrayList<>();
        stringList.add("全部");
        stringList.add("我发起");
        stringList.add("我检查");
        stringList.add("我整改");
        stringList.add("我验收");
        TagAdapter<String> tagAdapter =
                new TagAdapter(stringList) {
                    @Override
                    public View getView(FlowLayout parent, int position, Object o) {
                        TextView tv =
                                (TextView) LayoutInflater.from(getActivity()).inflate(R.layout.item_check_filter_flowlayout,
                                        parent, false);
                        tv.setText(stringList.get(position));
                        return tv;
                    }
                };

        tagWho.setAdapter(tagAdapter);

        tagWho.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                switch (stringList.get(position)) {

                    case "全部":
                        authorSelected = "";
                        break;
                    case "我发起":
                        authorSelected = "AsLauncher";
                        break;
                    case "我检查":
                        authorSelected = "AsChecker";
                        break;
                    case "我整改":
                        authorSelected = "AsRectifier";
                        break;
                    case "我验收":
                        authorSelected = "AsRechecker";
                        break;
                    default:
                        authorSelected = "";
                        break;
                }
                return true;
            }
        });
    }

    /**
     * 初始化严重等级数据
     */
    private void initLevelFlow() {

        List<String> stringList = new ArrayList<>();
        stringList.add("全部");
        stringList.add("一般");
        stringList.add("严重");
        stringList.add("非常严重");
        TagAdapter<String> tagAdapter =
                new TagAdapter(stringList) {
                    @Override
                    public View getView(FlowLayout parent, int position, Object o) {
                        TextView tv =
                                (TextView) LayoutInflater.from(getActivity()).inflate(R.layout.item_check_filter_flowlayout,
                                        parent, false);
                        tv.setText(stringList.get(position));
                        return tv;
                    }
                };

        tagLevel.setAdapter(tagAdapter);

        tagLevel.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {
                levelSelected = stringList.get(position);
                return true;
            }
        });
    }

    /**
     * 初始化状态数据
     */
    private void initStatusFlow() {

        List<String> stringList = new ArrayList<>();
        stringList.add("全部");
        stringList.add("待检查");
        stringList.add("需整改");
        stringList.add("验收中");
        stringList.add("已合格");
        stringList.add("已关闭");

        TagAdapter<String> tagAdapter =
                new TagAdapter(stringList) {
                    @Override
                    public View getView(FlowLayout parent, int position, Object o) {
                        TextView tv =
                                (TextView) LayoutInflater.from(getActivity()).inflate(R.layout.item_check_filter_flowlayout,
                                        parent, false);
                        tv.setText(stringList.get(position));
                        return tv;
                    }
                };

        tagStatus.setAdapter(tagAdapter);

        tagStatus.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {

                switch (stringList.get(position)) {

                    case "全部":
                        statusSelected = "";
                        break;
                    case "待检查":
                        statusSelected = "A_ToBeCheck";
                        break;
                    case "需整改":
                        statusSelected = "B_ToBeRectified";
                        break;
                    case "验收中":
                        statusSelected = "C_ToBeRecheck";
                        break;
                    case "已合格":
                        statusSelected = "D_Qualified";
                        break;
                    case "已归档":
                        statusSelected = "E_Close";
                        break;
                    case "已关闭":
                        statusSelected = "E_Closed";
                        break;
                    default:
                        statusSelected = "";
                        break;
                }

                return true;
            }
        });
    }


}

