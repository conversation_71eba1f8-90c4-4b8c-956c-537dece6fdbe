package com.probim.bimenew.fragment.position;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.TwoDimenAdapter;
import com.probim.bimenew.adapter.TwoDimenAdapter.OnRecycleItemListener;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.model.ModelTwoDimensModel;
import com.probim.bimenew.model.ModelTwoDimensModel.SheetsBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.view.AutoLoadRecyclerView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :模型----->二维视图
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/16:50.
 */
public class DrawingsPositionFragment extends BaseFragment {

  @BindView(R.id.rv_project)
  AutoLoadRecyclerView rvView;
  Unbinder unbinder;
  @BindView(R.id.smartRefreshLayout)
  SmartRefreshLayout smartRefreshLayout;
  @BindView(R.id.tv_status)
  TextView tvStatus;
  @BindView(R.id.rv_status_layout)
  RelativeLayout rvStatusLayout;
  private List<SheetsBean> list = new ArrayList<>();
  private TwoDimenAdapter adapter;
  private String projectId;
  private String modelId;


  public static DrawingsPositionFragment newInstance(String projectId, String modelId) {

    Bundle args = new Bundle();
    args.putString(CustomParam.ProjectId, projectId);
    args.putString(CustomParam.ModelId, modelId);
    DrawingsPositionFragment fragment = new DrawingsPositionFragment();
    fragment.setArguments(args);
    return fragment;
  }

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
      @Nullable Bundle savedInstanceState) {
    View view = inflater.inflate(R.layout.fragment_two_dimensional_view, container, false);
    unbinder = ButterKnife.bind(this, view);
    initData();
    return view;

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {
    //为 RecyclerView 设置布局管理器
    rvView.setLayoutManager(new LinearLayoutManager(getActivity()));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvView.addItemDecoration(
        new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL));
    //动画
    rvView.setItemAnimator(new DefaultItemAnimator());

    adapter = new TwoDimenAdapter(getActivity(), list);

    rvView.setAdapter(adapter);

    adapter.addRecycleItemListener(new OnRecycleItemListener() {
      @Override
      public void OnRecycleItemClick(View v, Object o) {
        SheetsBean data = (SheetsBean) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.TwoDimensName, data.getName());
        bundle.putString(CustomParam.TwoDimensId, data.getId());
        bundle.putString(CustomParam.ProjectId, projectId);
        bundle.putString(CustomParam.ModelId, modelId);
//        NewCheckActivityBack.isFromWhere =false;
//        Intent intent = new Intent(getActivity(), SelectDrawingForNewCheck2Activity.class);
//        intent.putExtras(bundle);
//        startActivity(intent);
        getActivity().finish();

      }
    });
  }

  @Override
  protected void initRefresh() {
    smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
      @Override
      public void onRefresh(RefreshLayout refreshLayout) {
        refreshLayout.finishRefresh(1000);
        loadData();
      }
    });
  }

  @Override
  protected void loadData() {
    if (getArguments() != null) {
      projectId = getArguments().getString(CustomParam.ProjectId);
      modelId = getArguments().getString(CustomParam.ModelId);
      ModelController controller = new ModelController();
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ModelID", modelId);
      params.put("VersionNO", "");
      params.put("FileType", "PlanView");
      params.put("FileName", "sheets");
      controller.GetTwoDimensional(params, new CallBack<ModelTwoDimensModel>() {
        @Override
        public void onSuccess(ModelTwoDimensModel modelTwoDimensModel) {

          list.clear();
          list.addAll(modelTwoDimensModel.getSheets());
          adapter.notifyDataSetChanged();


        }

        @Override
        public void onFail(String erroMsg) {
          rvStatusLayout.setVisibility(View.VISIBLE);
          tvStatus.setText("暂无数据");
        }
      });
    }

  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    unbinder.unbind();
  }
}
