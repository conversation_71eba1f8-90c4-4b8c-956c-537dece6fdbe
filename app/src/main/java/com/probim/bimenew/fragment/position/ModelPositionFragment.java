package com.probim.bimenew.fragment.position;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.SelectModelViewActivity;
import com.probim.bimenew.adapter.ModelPointView2RvAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.model.ModelViewPointModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.view.AutoLoadRecyclerView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :模型----->视点
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/16:50.
 */
public class ModelPositionFragment extends BaseFragment {

  @BindView(R.id.rv_project)
  AutoLoadRecyclerView rvProject;
  @BindView(R.id.smartRefreshLayout)
  SmartRefreshLayout smartRefreshLayout;
  Unbinder unbinder;
  @BindView(R.id.tv_status)
  TextView tvStatus;
  @BindView(R.id.rv_status_layout)
  RelativeLayout rvStatusLayout;
  private List<ModelViewPointModel> list = new ArrayList<>();
  private ModelPointView2RvAdapter adapter;
  private String projectId;
  private String organizeId;

  public static ModelPositionFragment newInstance(String projectId, String modelId) {

    Bundle args = new Bundle();
    args.putString(CustomParam.ProjectId, projectId);
    args.putString(CustomParam.ModelId, modelId);
    ModelPositionFragment fragment = new ModelPositionFragment();
    fragment.setArguments(args);
    return fragment;
  }

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
      @Nullable Bundle savedInstanceState) {
    View view = inflater.inflate(R.layout.fragment_model_view, container, false);
    unbinder = ButterKnife.bind(this, view);
    return view;
  }

  @Override
  protected void initRefresh() {

    smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
      @Override
      public void onRefresh(RefreshLayout refreshLayout) {
        refreshLayout.finishRefresh(1000);
        initData();
      }
    });
  }

  @Override
  protected void initData() {
    GetProjectDao();

    if (getArguments() != null) {
      projectId = getArguments().getString(CustomParam.ProjectId);
      String modelId = getArguments().getString(CustomParam.ModelId);
      ModelController controller = new ModelController();
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ModelID", modelId);
      controller.GetViewPoint(params, new CallBack<List<ModelViewPointModel>>() {
        @Override
        public void onSuccess(List<ModelViewPointModel> modelViewPointModels) {
          list.clear();
          //遍历list
          for (int i = 0; i < modelViewPointModels.size(); i++) {
            //type==0 视点, 1是批注
            if (modelViewPointModels.get(i).getType() == 0
                || modelViewPointModels.get(i).getType() == 1) {
              list.add(modelViewPointModels.get(i));
            }
          }

          if (list.isEmpty()) {
            rvStatusLayout.setVisibility(View.VISIBLE);
            tvStatus.setText("暂无数据");
          }
          adapter.notifyDataSetChanged();
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }
  }

  @Override
  protected void initRecycleview() {
    //为 RecyclerView 设置布局管理器
    rvProject.setLayoutManager(new LinearLayoutManager(getActivity()));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvProject.addItemDecoration(
        new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL));
    //动画
    rvProject.setItemAnimator(new DefaultItemAnimator());

    adapter = new ModelPointView2RvAdapter(getActivity(), list);

    rvProject.setAdapter(adapter);

    adapter.addRecycleItemListener(new ModelPointView2RvAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        ModelViewPointModel model = (ModelViewPointModel) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.WhoType, model.getType() + "");
        bundle.putString(CustomParam.ViewId, model.getID());
        bundle.putString(CustomParam.ProjectId, projectId);
        bundle.putString(CustomParam.ModelId, model.getModelID());
        bundle.putString(CustomParam.ViewPointName, model.getName());
        Intent intent = new Intent(getActivity(), SelectModelViewActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        getActivity().finish();
      }
    });
  }

  @Override
  protected void loadData() {

  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    unbinder.unbind();
  }

  @Override
  public void onResume() {
    super.onResume();
    initData();
  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      organizeId = bean.getBimProjectId();
    }
  }
}