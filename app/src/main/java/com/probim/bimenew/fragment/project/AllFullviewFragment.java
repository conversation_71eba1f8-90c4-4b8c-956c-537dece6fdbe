package com.probim.bimenew.fragment.project;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.fullview.adapter.AllFullviewAdapter;
import com.probim.bimenew.activity.fullview.controller.FullviewController;
import com.probim.bimenew.activity.fullview.dto.AllFullviewDTO2;
import com.probim.bimenew.activity.fullview.dto.FullviewLableDTO;
import com.probim.bimenew.activity.fullview.view.AllFullviewItemActivity;
import com.probim.bimenew.activity.fullview.view.NewFullviewActivity;
import com.probim.bimenew.activity.pano.LoadPanoActivity;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.dropdownmenu.bean.DropdownItemObject;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownButton;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownListView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.ButterKnife;

/**
 * 全景图列表
 */
public class AllFullviewFragment extends BaseFragment implements View.OnClickListener, DropdownListView.Container, OnRefreshListener {
    List<AllFullviewDTO2.DataDTO.PanoramasDTO> allList = new ArrayList<>();
    private RecyclerView rvAllFullview;
    private AllFullviewAdapter allFullviewAdapter;
    private Animation dropdown_in, dropdown_out, dropdown_mask_out;
    private List<DropdownItemObject> chooseTypeData = new ArrayList<>();
    private View mask;
    private DropdownButton dropType;
    private DropdownListView lisDropdownType;
    private DropdownListView currentDropdownList;
    private String labelId = "";
    private String pbName = "";
    private FullviewController fullviewController = new FullviewController();
    private EditText edtSearch;
    private String projectName;
    private String projectId;

    public static AllFullviewFragment newInstance(String projectId, String projectName, Boolean addAu, Boolean deleteAu) {
        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ProjectName, projectName);
        args.putBoolean(CustomParam.CheckAuthoruty, addAu);
        AllFullviewFragment fragment = new AllFullviewFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.activity_all_fullview, container, false);
        initView(view);
        initRecycleview();
        initData();
        return view;

    }

    @Override
    protected void loadData() {

    }

    @Override
    public void initView(View rootView) {
        if (getArguments() != null) {
            projectName = getArguments().getString(CustomParam.ProjectName);
            projectId = getArguments().getString(CustomParam.ProjectId);
        }
        SmartRefreshLayout smartRefreshLayout = rootView.findViewById(R.id.smartRefreshLayout);
        smartRefreshLayout.setOnRefreshListener(this);
        LinearLayout linBack = rootView.findViewById(R.id.lin_back);
        linBack.setOnClickListener(this);
        TextView tvTitle = rootView.findViewById(R.id.tv_title);
        tvTitle.setText(projectName);
        rvAllFullview = rootView.findViewById(R.id.rv_all_fullview);
        mask = rootView.findViewById(R.id.mask);
        dropType = rootView.findViewById(R.id.drop_type);
        lisDropdownType = rootView.findViewById(R.id.lis_dropdown_type);
        edtSearch = rootView.findViewById(R.id.edt_search);
        edtSearch.addTextChangedListener(new MyTextWatcher());
        ImageView ivAddFullview = rootView.findViewById(R.id.iv_add_fullview);
        ivAddFullview.setOnClickListener(this);
    }

    @Override
    protected void initData() {

    }

    @Override
    public void onResume() {
        super.onResume();
        getLable();
        getFullviewList();
    }

    @Override
    protected void initRecycleview() {
        allFullviewAdapter = new AllFullviewAdapter(allList);
        allFullviewAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                AllFullviewDTO2.DataDTO.PanoramasDTO dto = (AllFullviewDTO2.DataDTO.PanoramasDTO) o;
                Intent intent = new Intent(getActivity(), LoadPanoActivity.class);
                intent.putExtra(CustomParam.TvLeft, dto.getPbName());
                intent.putExtra(CustomParam.LoadPano, dto.getPbUrl());
                intent.putExtra(CustomParam.ProjectId, dto.getPbOrganizeId());
                intent.putExtra("isAll", true);
                startActivity(intent);

            }

            @Override
            public void OnDelete(int pos, Object o) {
                //  删除单挑数据
                AllFullviewDTO2.DataDTO.PanoramasDTO dto = (AllFullviewDTO2.DataDTO.PanoramasDTO) o;
                deleteFullview(dto.getPbGuid(), pos);
            }

            @Override
            public void OnClose(int pos, Object o) {
                AllFullviewDTO2.DataDTO.PanoramasDTO dto = (AllFullviewDTO2.DataDTO.PanoramasDTO) o;
                BaseApp.getInstance().insertInfo("lableId", dto.getLabelId());
                Intent intent = new Intent(getActivity(), AllFullviewItemActivity.class);
                intent.putExtra("pb_name", dto.getPbName());
                intent.putExtra("pb_guid", dto.getPbGuid());
                intent.putExtra("pb_url", dto.getPbUrl());
                intent.putExtra("pb_organizeId",dto.getPbOrganizeId());
                startActivity(intent);
            }
        });
        VerticalNoItemRecyclerView.initialize(rvAllFullview).setAdapter(allFullviewAdapter);


    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.lin_back:
                getActivity().finish();
                break;
            case R.id.iv_add_fullview:
                startActivity(new Intent(getActivity(), NewFullviewActivity.class).putExtra("isAll", true));
                break;
            default:
                break;
        }
    }

    /**
     * 获取全部标签
     */
    private void getLable() {

        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("OrganizeId", projectId);
        parmas.put("Token", Hawk.get(CustomParam.Token));
        fullviewController.getLabelList(parmas, new CallBack<FullviewLableDTO>() {
            @Override
            public void onSuccess(FullviewLableDTO fullviewLableDTO) {
                // 检查 Fragment 是否仍然附加到 Activity
                if (!isAdded() || getActivity() == null) {
                    return;
                }

                if (fullviewLableDTO.getData() != null) {
                    if (!chooseTypeData.isEmpty()){
                        chooseTypeData.clear();
                    }
                    chooseTypeData.add(new DropdownItemObject("全部标签", 0, ""));
                    for (FullviewLableDTO.DataDTO dto : fullviewLableDTO.getData()) {
                        chooseTypeData.add(new DropdownItemObject(dto.getLabelName(), 1, dto.getLabelId()));
                    }

                }
                initDrop();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }


    /**
     * 删除全景图
     */
    private void deleteFullview(String id, int pos) {

        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", Hawk.get(CustomParam.Token));
        parmas.put("PbGuid", id);
        fullviewController.deleteFullview(parmas, new CallBack<SchedualBaseResult>() {
            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                // 检查 Fragment 是否仍然附加到 Activity
                if (!isAdded() || getActivity() == null) {
                    return;
                }

                if (schedualBaseResult.getRet() == 1) {
                    allList.remove(pos);
                    if (allFullviewAdapter != null) {
                        allFullviewAdapter.notifyItemRemoved(pos);
                    }
                } else {
                    showMsg(schedualBaseResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取全部标签 查询
     */
    private void getFullviewList() {

        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", Hawk.get(CustomParam.Token));
        parmas.put("organizeId", projectId);
        parmas.put("labelId", labelId);
        parmas.put("pbName", pbName);
        fullviewController.getFullviewList(parmas, new CallBack<AllFullviewDTO2>() {
            @Override
            public void onSuccess(AllFullviewDTO2 allFullviewDTO2) {
                // 检查 Fragment 是否仍然附加到 Activity
                if (!isAdded() || getActivity() == null) {
                    return;
                }

                if (!allList.isEmpty()) {
                    allList.clear();
                }
                if (allFullviewDTO2.getRet() == 1) {
                    for (int i = 0; i < allFullviewDTO2.getData().size(); i++) {
                        allList.addAll(allFullviewDTO2.getData().get(i).getPanoramas());

                    }

                    if (allFullviewAdapter != null) {
                        allFullviewAdapter.notifyDataSetChanged();
                    }
                }


            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 舒适化下拉
     */
    private void initDrop() {
        // 检查 Fragment 是否仍然附加到 Activity
        if (!isAdded() || getActivity() == null) {
            return;
        }

        // 加载动画
        dropdown_in = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_in);
        dropdown_out = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_out);
        dropdown_mask_out = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_mask_out);


        // 添加空指针检查
        if (mask != null) {
            mask.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    hide();
                }
            });
        }

        reset();

        if (lisDropdownType != null && dropType != null) {
            lisDropdownType.bind(chooseTypeData, dropType, this, 0);
        }


        if (dropdown_mask_out != null) {
            dropdown_mask_out.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    if (currentDropdownList == null) {
                        reset();
                    }
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
        }
    }

    void reset() {
        if (dropType != null) {
            dropType.setChecked(false);
        }
        if (mask != null) {
            mask.setVisibility(View.GONE);
            mask.clearAnimation();
        }
        if (lisDropdownType != null) {
            lisDropdownType.setVisibility(View.GONE);
            lisDropdownType.clearAnimation();
        }
    }

    @Override
    public void show(DropdownListView listView) {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            if (dropdown_out != null) {
                currentDropdownList.startAnimation(dropdown_out);
            }
            currentDropdownList.setVisibility(View.GONE);
            if (currentDropdownList.button != null) {
                currentDropdownList.button.setChecked(false);
            }
        }
        currentDropdownList = listView;
        if (mask != null) {
            mask.clearAnimation();
            mask.setVisibility(View.VISIBLE);
        }
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            if (dropdown_in != null) {
                currentDropdownList.startAnimation(dropdown_in);
            }
            currentDropdownList.setVisibility(View.VISIBLE);
            if (currentDropdownList.button != null) {
                currentDropdownList.button.setChecked(true);
            }
        }
    }

    @Override
    public void hide() {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            if (dropdown_out != null) {
                currentDropdownList.startAnimation(dropdown_out);
            }
            if (currentDropdownList.button != null) {
                currentDropdownList.button.setChecked(false);
            }
            if (mask != null) {
                mask.clearAnimation();
                if (dropdown_mask_out != null) {
                    mask.startAnimation(dropdown_mask_out);
                }
            }
        }
        currentDropdownList = null;
    }

    @Override
    public void onSelectionChanged(DropdownListView view, String itemId) {

        // 类型筛选
        if (lisDropdownType == view) {

            if (itemId.equals("全部标签")) {
                labelId = "";
            } else {
                labelId = itemId;
            }
            //  查询数据
            getFullviewList();

        }


    }


    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getFullviewList();
        refreshLayout.finishRefresh();

    }

    private class MyTextWatcher implements TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            pbName = s.toString();
            getFullviewList();

        }
    }
}