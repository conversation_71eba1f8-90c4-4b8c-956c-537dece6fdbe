package com.probim.bimenew.fragment.project;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;


import com.probim.bimenew.R;
import com.probim.bimenew.activity.business.QualityListActivity;
import com.probim.bimenew.activity.business.SafeListActivity;
import com.probim.bimenew.activity.flow.AllFlowListActivity;
import com.probim.bimenew.activity.schedule.AllSchedualListActivity;
import com.probim.bimenew.activity.schedule.LoadScheduleActivity;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.fragment.BaseFragment;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class BusinessFragment extends BaseFragment {
    @BindView(R.id.card_quality)
    CardView cardCheck;
    @BindView(R.id.card_shedual)
    CardView cardShedual;
    @BindView(R.id.card_safe)
    CardView cacrdFullview;
    @BindView(R.id.card_flow)
    CardView cardFlow;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.lin_title)
    LinearLayout linTitle;
    @BindView(R.id.iv_search)
    ImageView ivSearch;

    public static BusinessFragment newInstance(String projectId, String projectName, Boolean addAu) {
        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ProjectName, projectName);
        args.putBoolean(CustomParam.CheckAuthoruty, addAu);
        BusinessFragment fragment = new BusinessFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_business, container, false);
        unbinder = ButterKnife.bind(this, view);
        initView(container);
        initData();
        return view;
    }

    @Override
    protected void initData() {
        if (getArguments() != null) {
            ivSearch.setVisibility(View.GONE);
            String projectId = getArguments().getString(CustomParam.ProjectId);
            String projectName = getArguments().getString(CustomParam.ProjectName);
            boolean checkAu = getArguments().getBoolean(CustomParam.CheckAuthoruty);
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(projectName);
            tvLeft.setText("");

        }

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void loadData() {

    }

    @OnClick({R.id.lin_back, R.id.card_quality, R.id.card_shedual, R.id.card_safe, R.id.card_flow})
    public void onViewClicked(View view) {
        Intent intent = null;
        switch (view.getId()) {

            case R.id.lin_back:
                getActivity().finish();
                break;
            case R.id.card_quality:
                intent = new Intent(getActivity(), LoadScheduleActivity.class);
                intent.putExtra("load-type", "quality");
                startActivity(intent);
                break;
            case R.id.card_safe:
                intent = new Intent(getActivity(), LoadScheduleActivity.class);
                intent.putExtra("load-type", "safe");
                startActivity(intent);
                break;
            case R.id.card_shedual:
                intent = new Intent(getActivity(), LoadScheduleActivity.class);
                intent.putExtra("load-type", "schedule");
                startActivity(intent);
                break;
            case R.id.card_flow:
                startActivity(AllFlowListActivity.class);
                break;
            default:
                break;
        }
    }
}
