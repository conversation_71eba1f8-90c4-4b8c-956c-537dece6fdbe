package com.probim.bimenew.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import butterknife.BindView;
import com.probim.bimenew.R;
import com.probim.bimenew.utils.web.X5WebView;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/6/12/16:55.
 */
public class HomeFragment extends BaseFragment {

  @BindView(R.id.mWebView)
  X5WebView mWebView;

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
      @Nullable Bundle savedInstanceState) {
    return inflater.inflate(R.layout.fragment_home, container, false);
  }

  @Override
  protected void loadData() {
   // mWebView.loadUrl("http://soft.imtt.qq.com/browser/tes/feedback.html");
  }

  @Override
  public void onAttach(Context context) {
    super.onAttach(context);
    //初始化Adapter
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }
}
