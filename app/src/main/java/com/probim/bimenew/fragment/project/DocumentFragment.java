package com.probim.bimenew.fragment.project;

import android.content.ActivityNotFoundException;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnKeyListener;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.TextView.OnEditorActionListener;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.BuildConfig;
import com.probim.bimenew.NewLinearLayout.MyLinearLayoutManager;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.DwgPreviewActivity;
import com.probim.bimenew.adapter.DocRvAdapter;
import com.probim.bimenew.adapter.DocRvAdapter.OnRecycleItemListener;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.DocController;
import com.probim.bimenew.file.DocViewActivity;
import com.probim.bimenew.file.MyMimeMap;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.interfaces.AuthInterface;
import com.probim.bimenew.model.BaseDocDto;
import com.probim.bimenew.model.BaseModel;
import com.probim.bimenew.model.DeleteDocDto;
import com.probim.bimenew.model.DocFileDto;
import com.probim.bimenew.model.DocTreeDto;
import com.probim.bimenew.model.DocFirstFloor;
import com.probim.bimenew.model.DocShareResult;
import com.probim.bimenew.model.DocUserPermissions;
import com.probim.bimenew.model.DocVersionDto;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.DocAuResult;
import com.probim.bimenew.share.DocShareBean;
import com.probim.bimenew.share.ShareCallBack;
import com.probim.bimenew.share.UMDocShareUtils;
import com.probim.bimenew.utils.DownloadUtil;
import com.probim.bimenew.utils.FileUtils;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.popwindow.SharePopWindow;
import com.probim.bimenew.utils.thread.ThreadUtils;
import com.probim.bimenew.utils.web.X5WebView;
import com.umeng.socialize.bean.SHARE_MEDIA;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

/**
 * Description :文档界面
 * Author : Gary
 * Email : <EMAIL>
 * Date : 2018/7/9/16:58.
 */
public class DocumentFragment extends BaseFragment {

    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.img_right)
    ImageView imgRight;
    @BindView(R.id.rv_project)
    RecyclerView rvProject;
    Unbinder unbinder;
    @BindView(R.id.serach_layout)
    RelativeLayout serachLayout;
    @BindView(R.id.fragment_header)
    Toolbar fragmentHeader;
    @BindView(R.id.edt_search)
    EditText edtSearch;
    @BindView(R.id.tv_cancle)
    TextView tvCancle;
    @BindView(R.id.tv_search)
    TextView tvSearch;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    @BindView(R.id.iv_status)
    ImageView ivStatus;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    List<List<DocFileDto.DataDTO>> docAllList = new ArrayList<>();
    private List<DocFileDto.DataDTO> list = new ArrayList<>();
    private DocRvAdapter adapter;
    private String organizeId;
    private DocController controller;
    private String projectName;
    private List<String> backList = new ArrayList<>();
    private boolean isRoot = true;
    private String projectId;

    public static DocumentFragment newInstance(String projectId, String projectName,
                                               String organizeId,
                                               boolean authority) {
        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ProjectName, projectName);
        args.putString(CustomParam.OrganizeId, organizeId);
        args.putBoolean(CustomParam.DocAuthority, authority);
        DocumentFragment fragment = new DocumentFragment();
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * 拼接逗号字符串
     */
    private static String dataToString(List<String> stringList) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < stringList.size(); i++) {
            if (sb.length() > 0) {// 该步即不会第一位有逗号，也防止最后一位拼接逗号！
                sb.append(",");
            }
            sb.append(stringList.get(i));
        }
        return sb.toString();
    }

    /**
     * 使用自定义方法获得文件的MIME类型
     */
    private static String getMimeTypeFromFile(File file) {
        String type = "*/*";
        String fName = file.getName();
        //获取后缀名前的分隔符"."在fName中的位置。
        int dotIndex = fName.lastIndexOf(".");
        if (dotIndex > 0) {
            //获取文件的后缀名
            String end = fName.substring(dotIndex, fName.length()).toLowerCase(Locale.getDefault());
            //在MIME和文件类型的匹配表中找到对应的MIME类型。
            HashMap<String, String> map = MyMimeMap.getMimeMap();
            if (!TextUtils.isEmpty(end) && map.keySet().contains(end)) {
                type = map.get(end);
            }
        }
        return type;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_document, container, false);
        unbinder = ButterKnife.bind(this, view);
        initData();
        setListener();
        return view;
    }

    @Override
    protected void initData() {
        if (getArguments() != null) {
            controller = new DocController();
            organizeId = getArguments().getString(CustomParam.OrganizeId);
            projectName = getArguments().getString(CustomParam.ProjectName);
            projectId = getArguments().getString(CustomParam.ProjectId);
//            authority = getArguments().getBoolean(CustomParam.DocAuthority);
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(projectName);
        }
        edtSearch.setHint("查找文档");
        tvLeft.setText("");
        imgRight.setVisibility(View.GONE);
        imgRight.setImageResource(R.mipmap.btn_common_search);
        getDocTree("0");
    }

    @Override
    protected void initRecycleview() {
        // 为 RecyclerView 设置布局管理器
        rvProject.setLayoutManager(new MyLinearLayoutManager(getActivity()));
        // 为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvProject.addItemDecoration(
                new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL));
        // 动画
//    rvProject.setItemAnimator(new DefaultItemAnimator());

        adapter = new DocRvAdapter(getActivity(), list);

        adapter.addRecycleItemListener(new OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {

                DocFileDto.DataDTO dataDTO = (DocFileDto.DataDTO) o;
                if (dataDTO.getIsFolder()) {
                    // 文件夹
                    isRoot = false;
                    getDocFile(dataDTO.getFileId(), false);
                } else {
                    switch (dataDTO.getFileExtension().toLowerCase()) {
                        case ".dwg":
                            openDwg(dataDTO);
                            break;
                        case ".zip":
                        case ".rar":
                        case ".7z":
                        case ".rvt":
                        case ".mv":
                            showMsg("此文件格式暂不支持预览！");
                            break;
                        default:
                            openFile(dataDTO);
                            break;
                    }
                }
            }

            @Override
            public void onShare(int pos, Object o) {
            }

            @Override
            public void OnDelete(int pos, Object o) {
                DocFileDto.DataDTO dataDTO = (DocFileDto.DataDTO) o;
                deleteFiles(dataDTO, pos);
            }
        });
        rvProject.setAdapter(adapter);
    }

    @Override
    protected void initRefresh() {
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    @OnClick({R.id.iv_back, R.id.lin_back, R.id.img_right, R.id.tv_cancle, R.id.tv_search})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
            case R.id.lin_back:
                backToList();
                break;
            case R.id.img_right:
                //fragmentHeader.setVisibility(View.GONE);
                //serachLayout.setVisibility(View.VISIBLE);
                break;
            case R.id.tv_cancle:
                // 关闭软键盘
                /*InputMethodManager imm = (InputMethodManager) getActivity()
                    .getSystemService(Activity.INPUT_METHOD_SERVICE);
                imm.toggleSoftInput(InputMethodManager.HIDE_IMPLICIT_ONLY, 0);*/

                View tt = getActivity().getCurrentFocus();
                if (tt != null) {
                    InputMethodManager imm =
                            (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
                }

                //fragmentHeader.setVisibility(View.VISIBLE);
                //serachLayout.setVisibility(View.GONE);
                CancleSearch();
                // docAllList.clear();
                // GetAllFiles(role);
                break;
            case R.id.tv_search:
                searchDoc(edtSearch.getText().toString().trim());
                break;
        }
    }


    /**
     * 返回物理按键处理文档层级关系数据
     */

    private void backToList() {
        if (isRoot) {
            // 初始进入 没有点击事件
            getActivity().finish();
        } else {
            if (backList.size() == 1) {
                getActivity().finish();
                return;
            }
            backList.remove(backList.size() - 1);
            getDocFile(backList.get(backList.size() - 1), true);
        }

    }

    /**
     * 删除文件
     */
    private void deleteFiles(DocFileDto.DataDTO dataDTO, int position) {
        List<DeleteDocDto> ids = new ArrayList<>();
        DeleteDocDto deleteDocDto = new DeleteDocDto();
        deleteDocDto.setId(dataDTO.getFileId());
        deleteDocDto.setFolder(dataDTO.getIsFolder());
        ids.add(deleteDocDto);
        controller.deleteFiles(Hawk.get(CustomParam.UserId), JsonHelper.toJson(ids), new CallBack<BaseDocDto>() {
            @Override
            public void onSuccess(BaseDocDto baseDocDto) {
                // 检查 Fragment 是否仍然附加到 Activity
                if (!isAdded() || getActivity() == null) {
                    return;
                }

                if (baseDocDto.getRet() == 1) {
                    list.remove(position);
                    if (adapter != null) {
                        adapter.notifyItemRemoved(position);
                    }
                } else {
                    showMsg(baseDocDto.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 文档搜索
     */
    private void searchDoc(String serachStr) {
        HashMap<String, String> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("fileName", serachStr);
        params.put("userId", Hawk.get(CustomParam.UserId));
        params.put("Token",Hawk.get(CustomParam.Token));
        controller.searchDoc(params, new CallBack<DocFileDto>() {

            @Override
            public void onSuccess(DocFileDto docFileDto) {
                // 检查 Fragment 是否仍然附加到 Activity
                if (!isAdded() || getActivity() == null) {
                    return;
                }

                if (docFileDto.getRet() == 200) {
                    if (docFileDto.getData().isEmpty()) {
                        noSearchResult();
                        return;
                    }
                    list.clear();
                    list.addAll(docFileDto.getData());
                    if (adapter != null) {
                        adapter.notifyDataSetChanged();
                    }
                } else {
                    showMsg(docFileDto.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    // 添加输入框监听以及键盘搜索按键监听
    private void setListener() {

        //用户输入完毕进行搜索
        edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    tvSearch.setVisibility(View.VISIBLE);
                }
            }
        });

        // 键盘搜索键
        edtSearch.setOnEditorActionListener(new OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
                if (i == EditorInfo.IME_ACTION_SEARCH) {

                    if (!TextUtils.isEmpty(edtSearch.getText().toString().trim())) {
                        searchDoc(edtSearch.getText().toString().trim());
                    }

                    /*//搜索
                    if (TextUtils.isEmpty(edtSearch.getText().toString().trim())) {
                      docAllList.clear();
                      GetAllFiles(role);
                    } else {

                      DocSearch(edtSearch.getText().toString().trim());
                    }*/
                    return true;
                }
                return false;
            }
        });
    }

    /**
     * 取消搜索文件层次处理
     */
    private void CancleSearch() {
        if (edtSearch != null) {
            edtSearch.setText(null);
        }
        if (tvSearch != null) {
            tvSearch.setVisibility(View.GONE);
        }
        if (rvProject != null) {
            rvProject.setVisibility(View.VISIBLE);
        }
        if (rvStatusLayout != null) {
            rvStatusLayout.setVisibility(View.GONE);
        }
        list.clear();
        getDocTree("0");
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    /**
     * 暂无搜索结果
     */
    private void noSearchResult() {
        if (tvStatus != null) {
            tvStatus.setText("无搜索结果");
        }
        if (ivStatus != null) {
            ivStatus.setVisibility(View.INVISIBLE);
        }
        if (rvProject != null) {
            rvProject.setVisibility(View.GONE);
        }
        if (rvStatusLayout != null) {
            rvStatusLayout.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 文件夹下无文档
     */
    private void noFile() {
        if (tvStatus != null) {
            tvStatus.setText("暂无数据");
        }
        if (ivStatus != null) {
            ivStatus.setImageResource(R.mipmap.ic_document_empty);
        }
        if (rvProject != null) {
            rvProject.setVisibility(View.GONE);
        }
        if (rvStatusLayout != null) {
            rvStatusLayout.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 显示文档列表
     */
    private void showFile() {
        // 添加空指针检查
        if (rvProject != null) {
            rvProject.setVisibility(View.VISIBLE);
        }
        if (rvStatusLayout != null) {
            rvStatusLayout.setVisibility(View.GONE);
        }
    }

    /**
     * 判断文档打开权限
     *
     * @param isHas
     */
    private void handleAuth(boolean isHas) {
        if (isHas) {
            if (rvProject != null) {
                rvProject.setVisibility(View.VISIBLE);
            }
            if (rvStatusLayout != null) {
                rvStatusLayout.setVisibility(View.GONE);
            }
        } else {
            if (tvStatus != null) {
                tvStatus.setText("暂无权限，请在WEB端添加权限");
            }
            if (ivStatus != null) {
                ivStatus.setImageResource(R.mipmap.ic_document_empty);
            }
            if (rvProject != null) {
                rvProject.setVisibility(View.GONE);
            }
            if (rvStatusLayout != null) {
                rvStatusLayout.setVisibility(View.VISIBLE);
            }
        }

    }

    //根据图片名称取得对应图片
    public int getDrawable(String name) {

        ApplicationInfo appInfo = getActivity().getApplicationInfo();
        int resID = getActivity().getResources().getIdentifier(name, "mipmap", appInfo.packageName);
        //解析资源文件夹下，id为resID的图片
        return resID;
    }

    /**
     * dwg文件打开
     *
     * @param bean
     */
    private void openDwg(DocFileDto.DataDTO bean) {

        try {
            String firstUrl = Hawk.get(CustomParam.Doc_URL) + "/api/v1/file/preview?FileId=" + bean.getFileId() + "&Version=1" + "&UserId=" + Hawk.get(CustomParam.UserId)+ "&Token=" + Hawk.get(CustomParam.Token);
            String docUrl = "https://www.probim.cn:9003" + "/Home/Index2?dwgurlcfg=" + URLEncoder.encode(firstUrl, "UTF-8") + "&name" + bean.getFileName();
            Intent intent = new Intent(getActivity(), DocViewActivity.class);
            intent.putExtra(CustomParam.DocUrl, docUrl);
            intent.putExtra(CustomParam.DocName, bean.getFileName());
            startActivity(intent);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }


    }

    /**
     * 在线预览文件
     *
     * @param dataDTO
     */
    private void openFile(DocFileDto.DataDTO dataDTO) {
        //: http://8.140.54.245:5000/api/v1/file/preview?FileId=d94c2702-68db-49ab-b6b8-d966b6187df4&Version=1&UserId=837f46a0-6ad8-4a07-aa4f-edf9705f5388&Token=3161CBE4B6FB907632E260D33969D626290A42EDCFF9B7C1B9804DE02C8F6A9482B7F2BD543E2AD36D83963BE008CB79A5D9635820E253A6E5B06E2A0A7DF20E78E260977C55335BF36A7C9B83DC6399|.png
        try {
            String firstUrl = Hawk.get(CustomParam.Doc_URL) + "/api/v1/file/preview?FileId=" + dataDTO.getFileId() + "&Version=1" + "&UserId=" + Hawk.get(CustomParam.UserId)+ "&Token=" + Hawk.get(CustomParam.Token);
            String docUrl = BaseApp.getDocUrl() + "/Content/PDFJS/web/viewer.html?file=" + URLEncoder.encode(firstUrl, "UTF-8") + "|" + dataDTO.getFileExtension();
            Intent intent = new Intent(getActivity(), DocViewActivity.class);
            intent.putExtra(CustomParam.DocUrl, docUrl);
            intent.putExtra(CustomParam.DocName, dataDTO.getFileName());
            startActivity(intent);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取文档树结构
     *
     * @param parentId
     */
    private void getDocTree(String parentId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", Hawk.get(CustomParam.UserId));
        params.put("projectId", projectId);
        params.put("parentId", parentId);
        params.put("Token",Hawk.get(CustomParam.Token));
        controller.getDocTree(params, new CallBack<DocTreeDto>() {

            @Override
            public void onSuccess(DocTreeDto docTreeDto) {
                // 检查 Fragment 是否仍然附加到 Activity
                if (!isAdded() || getActivity() == null) {
                    return;
                }

                if (docTreeDto.getRet() == 1) {
                    if (!docTreeDto.getData().isEmpty() && parentId.equals("0")) {
                        isRoot = true;
                        if (docTreeDto.getData().get(0).getAuth().getOpen()) {
                            // 有权限打开
                            getDocFile(docTreeDto.getData().get(0).getId(), false);
                        } else {
                            handleAuth(false);
                        }
                    }
                } else {
                    noFile();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取文档
     *
     * @param folderId
     */
    private void getDocFile(String folderId, boolean isBack) {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", Hawk.get(CustomParam.UserId));
        params.put("projectId", projectId);
        params.put("folderId", folderId);
        params.put("Token",Hawk.get(CustomParam.Token));
        controller.getDocFile(params, new CallBack<DocFileDto>() {

            @Override
            public void onSuccess(DocFileDto docFileDto) {
                // 检查 Fragment 是否仍然附加到 Activity
                if (!isAdded() || getActivity() == null) {
                    return;
                }

                if (docFileDto.getRet() == 1) {
                    if (!isBack) {
                        backList.add(folderId);
                    }
                    list.clear();
                    list.addAll(docFileDto.getData());
                    if (adapter != null) {
                        adapter.notifyDataSetChanged();
                    }
                    if (docFileDto.getData().isEmpty()) {
                        noFile();
                    } else {
                        showFile();
                    }

                }else {
                    showMsg(docFileDto.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
        mLoading.dismiss();
    }
}
