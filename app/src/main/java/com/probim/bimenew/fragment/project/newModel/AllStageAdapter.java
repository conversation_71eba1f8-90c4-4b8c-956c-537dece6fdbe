package com.probim.bimenew.fragment.project.newModel;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualTypeDto;
import com.probim.bimenew.dto.ModelStageDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class AllStageAdapter extends RecyclerView.Adapter<AllStageAdapter.ViewHolder> {

    private List<ModelStageDto> dtoList = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;
    private List<Boolean> allClicks = new ArrayList<>();

    public AllStageAdapter(List<ModelStageDto> dtoList) {
        this.dtoList = dtoList;
        for (int i = 0; i < dtoList.size(); i++) {
            if (i == 0) {
                allClicks.add(true);
            } else {
                allClicks.add(false);
            }
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_stage, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ModelStageDto dto = dtoList.get(position);
        holder.tvName.setText(dto.getName());
        if (!allClicks.isEmpty()) {
            if (allClicks.get(position)) {
                holder.tvName.setTextColor(Color.parseColor("#283A4F"));
                holder.blueLine.setVisibility(View.VISIBLE);
                holder.bcItem.setBackgroundColor(Color.parseColor("#f3f5f7"));
            } else {
                holder.blueLine.setVisibility(View.GONE);
                holder.tvName.setTextColor(Color.parseColor("#616F7D"));
                holder.bcItem.setBackgroundColor(Color.parseColor("#ffffff"));
            }
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    for (int i = 0; i < allClicks.size(); i++) {
                        allClicks.set(i, false);
                    }
                    allClicks.set(position, true);
                    notifyDataSetChanged();
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_name)
        TextView tvName;
        @BindView(R.id.blue_line)
        View blueLine;
        @BindView(R.id.bc_item)
        LinearLayout bcItem;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
