package com.probim.bimenew.screenadaptation;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import com.orhanobut.logger.Logger;

/**
 * Description :是根据一个参照分辨率进行布局，
 * 然后再各个机器上提取当前机器分辨率换算出系数之后，
 * 然后再通过重新测量的方式来达到适配的效果，
 * 这一套方案基本能适用于95以上的机型，
 * 那么今天到时候再加上刘海屏的适配就OK了
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/4/23/13:46
 */

public class ScreenAdaptationRelaLayout extends RelativeLayout {

  public ScreenAdaptationRelaLayout(Context context) {
    super(context);
  }

  public ScreenAdaptationRelaLayout(Context context, AttributeSet attrs) {
    super(context, attrs);
  }

  public ScreenAdaptationRelaLayout(Context context, AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
  }

  static boolean isFlag = true;

  @Override
  protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {

    if (isFlag) {
      int count = this.getChildCount();
      float scaleX = UIUtils.getInstance(this.getContext()).getHorizontalScaleValue();
      float scaleY = UIUtils.getInstance(this.getContext()).getVerticalScaleValue();

      Logger.e("x系数:" + scaleX);
      Logger.i("y系数:" + scaleY);

      for (int i = 0; i < count; i++) {
        View child = this.getChildAt(i);
        //代表的是当前空间的所有属性列表
        LayoutParams layoutParams = (LayoutParams) child.getLayoutParams();
        layoutParams.width = (int) (layoutParams.width * scaleX);
        layoutParams.height = (int) (layoutParams.height * scaleY);
        layoutParams.rightMargin = (int) (layoutParams.rightMargin * scaleX);
        layoutParams.leftMargin = (int) (layoutParams.leftMargin * scaleX);
        layoutParams.topMargin = (int) (layoutParams.topMargin * scaleY);
        layoutParams.bottomMargin = (int) (layoutParams.bottomMargin * scaleY);
      }
      isFlag = false;
    }

    super.onMeasure(widthMeasureSpec, heightMeasureSpec);
  }

  @Override
  protected void onDraw(Canvas canvas) {
    super.onDraw(canvas);
  }
}