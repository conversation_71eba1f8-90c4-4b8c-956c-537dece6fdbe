package com.probim.bimenew.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :问题详情页面照片展示
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/13/15:42.
 */
public class ProblemPopWindowHeadAdapter extends BaseAdapter {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private List<String> mList = new ArrayList<>();

  public ProblemPopWindowHeadAdapter(List<String> list, Context mContext) {
    this.mList = list;
    this.mContext = mContext;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @Override
  public int getCount() {
    return mList == null ? 0 : mList.size();
  }

  @Override
  public Object getItem(int i) {
    return mList.get(i);
  }

  @Override
  public long getItemId(int i) {
    return i;
  }

  @Override
  public View getView(int i, View view, ViewGroup viewGroup) {
    ViewHolder holder = null;
    if (view == null) {
      view = mLayoutInflater.inflate(R.layout.item_status_grid_pop, null);
      holder = new ViewHolder(view);
      view.setTag(holder);
    } else {
      holder = (ViewHolder) view.getTag();
    }
    holder.tvName.setText(mList.get(i));
    return view;
  }

  static class ViewHolder {

    @BindView(R.id.tv_name)
    TextView tvName;

    ViewHolder(View view) {
      ButterKnife.bind(this, view);
    }
  }


}
