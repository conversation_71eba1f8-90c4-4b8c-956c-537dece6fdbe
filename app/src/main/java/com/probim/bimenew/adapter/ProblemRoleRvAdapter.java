package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.TextView;

import com.probim.bimenew.R;
import com.probim.bimenew.adapter.ProblemRoleRvAdapter.ViewHolder;
import com.probim.bimenew.model.ProjectUser;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;


/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/21/16:40.
 */
public class ProblemRoleRvAdapter extends Adapter<ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private OnRecycleItemListener mRecycleItemListener;
    private List<ProjectUser> mData;
    private int myposition = -1;

    public ProblemRoleRvAdapter(Context mContext,
                                List<ProjectUser> mData) {
        this.mContext = mContext;
        this.mData = mData;
        this.mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_role, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ProjectUser projectUser = mData.get(position);
        if (position == myposition) {
            holder.tvRoleName.setTextColor(mContext.getResources().getColor(R.color.text_yellow));
        } else {
            holder.tvRoleName.setTextColor(mContext.getResources().getColor(R.color.sp_16_filter));
        }
        holder.tvRoleName.setText(projectUser.getRoleName());
        holder.tvRoleSize.setText(projectUser.getUsers().size() + "");
        holder.itemView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {

                    mRecycleItemListener.OnRecycleItemClick(position, projectUser.getUsers());

                }
            }
        });

    }

    @Override
    public int getItemCount() {
        return mData.size();
    }


    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);

    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.tv_role_name)
        TextView tvRoleName;
        @BindView(R.id.tv_role_size)
        TextView tvRoleSize;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    public void getIndex(int myposition) {
        this.myposition = myposition;
    }

}
