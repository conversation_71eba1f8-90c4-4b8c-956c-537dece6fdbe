package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.model.NewCheckConfig;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class FenXiangProjectAdapter extends RecyclerView.Adapter<FenXiangProjectAdapter.ViewHolder> {

  private Context mContext;
  private List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX.ItemsBeanXX.ChildrenBeanXX.ItemsBeanX>
      mDatas;
  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;

  public FenXiangProjectAdapter(Context mContext,
      List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX.ItemsBeanXX.ChildrenBeanXX.ItemsBeanX> mDatas) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_step_project, parent, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX.ItemsBeanXX.ChildrenBeanXX.ItemsBeanX
        bean = mDatas.get(position);

    holder.tvProjectStep.setText(bean.getName());

    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {

          mRecycleItemListener.OnRecycleItemClick(position, bean.getChildren());
        }
      }
    });
  }

  @Override public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_project_step) TextView tvProjectStep;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
