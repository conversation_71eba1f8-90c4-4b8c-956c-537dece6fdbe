package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.ProblemMemberRvAdapter.ViewHolder;
import com.probim.bimenew.model.ProjectUser;
import java.util.List;


/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/21/16:40.
 */
public class ProblemMemberRvAdapter extends Adapter<ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  private List<ProjectUser.UsersBean> mData;

  public ProblemMemberRvAdapter(Context mContext,
      List<ProjectUser.UsersBean> mData) {
    this.mContext = mContext;
    this.mData = mData;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_member, parent, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    ProjectUser.UsersBean usersBean = mData.get(position);

    holder.tvMemberName.setText(usersBean.getRealName());
    holder.itemView.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          //
          mRecycleItemListener.OnRecycleItemClick(position, usersBean);
        }
      }
    });

  }

  @Override
  public int getItemCount() {
    return mData.size();
  }


  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);

  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.tv_member_name)
    TextView tvMemberName;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }
}
