package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.result.RoleUsersDto;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2021/1/6/13:58.
 */

public class RoleUserInnerAdapter
        extends RecyclerView.Adapter<RoleUserInnerAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private List<RoleUsersDto.DataDTO.ListDTO.UsersDTO> mDatas = new ArrayList<>();
    private List<Boolean> isClicks;
    private OnItemClickListener listener;

    public RoleUserInnerAdapter(Context mContext, List<RoleUsersDto.DataDTO.ListDTO.UsersDTO> datas) {
        this.mDatas = datas;
        this.mContext = mContext;
        this.mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_all_peoplel, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        RoleUsersDto.DataDTO.ListDTO.UsersDTO
                bean = mDatas.get(position);
        holder.tvLetterName.setText(bean.getRealName()
                .substring(bean.getRealName().length() - 1));
        holder.tvName.setText(bean.getRealName());
        holder.ivSelected.setVisibility(View.VISIBLE);
        //holder.tvCode.setText(bean.getBm_materialcode());
        if (bean.isSelected()) {
            Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
        } else {
            Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null) {

                    if (bean.isSelected()) {
                        bean.setSelected(false);
                        Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
                    } else {
                        bean.setSelected(true);
                        Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
                    }


                    listener.OnItemClick(position, bean);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void setOnItemClickListener(OnItemClickListener itemClickListener) {
        this.listener = itemClickListener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnItemClickListener<T> {

        void OnItemClick(int pos, T o);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_name)
        TextView tvName;
        @BindView(R.id.tv_type)
        TextView tvType;
        @BindView(R.id.ivSelected)
        ImageView ivSelected;
        @BindView(R.id.tv_letter_name)
        TextView tvLetterName;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
