package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.PhotoViewActivity;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.Comments2;
import com.probim.bimenew.utils.view.MyGridView;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/4/17:20.
 */
public class CommentsInnerRvAdapter extends Adapter<CommentsInnerRvAdapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  @BindView(R.id.tv_position) TextView tvPosition;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<Comments2.DataBean.InnerListBean> mDatas;
  private String organizeId;

  public CommentsInnerRvAdapter(Context mContext, List<Comments2.DataBean.InnerListBean> mDatas,
      String organizeId) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
    this.organizeId = organizeId;
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_comments_inner, null, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int i) {

    Comments2.DataBean.InnerListBean dto = mDatas.get(i);
    holder.tvName.setText(dto.getBU_RealName());
    holder.tvDate.setText(dto.getCreateDate().replace("T", " "));
    holder.tvIntro.setText(dto.getContent());
    holder.tvPosition.setText(i + 1 + " 楼");
    List<PhotoDto> photoDtoList = new ArrayList<>();
    //照片url拼接
    for (Comments2.DataBean.InnerListBean.TalkDocsBeanX innerListBean : dto.getTalkDocs()) {
      //请求文档和照片数据
      String photoUrl = BaseApp.getBimUrl() + ApiConstant.LOAD_HIDE + "?ProjectID=" + organizeId
          + "&FileId=" + innerListBean.getTargetID()
          + "&FileType=Issue";
      photoDtoList.add(new PhotoDto(photoUrl));
    }
    //照片  内部列表 数据传输
    new Thread(new Runnable() {
      @Override public void run() {
        loadPhotoData(holder.gridPhoto, photoDtoList);
      }
    }).start();

    holder.tvReplyClick.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnInnerItemReply(i, dto.getIssue_TalkId());
        }
      }
    });

    holder.ivDelete.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnInnerItemReply(i, dto.getIssue_TalkId());
        }
      }
    });
  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  static
  class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_name) TextView tvName;
    @BindView(R.id.tv_intro) TextView tvIntro;
    @BindView(R.id.grid_photo) MyGridView gridPhoto;
    @BindView(R.id.tv_date) TextView tvDate;
    @BindView(R.id.tv_reply_click) TextView tvReplyClick;
    @BindView(R.id.tv_position) TextView tvPosition;
    @BindView(R.id.iv_delete) ImageView ivDelete;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnInnerItemReply(int pos, T o);

    void OnInnerItemDelete(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  /**
   * 照片适配器
   */
  private void loadPhotoData(MyGridView gridPhoto, List<PhotoDto> list) {

    CommentsPhotoAdapter commentsPhotoAdapter = new CommentsPhotoAdapter(list,
        mContext);

    gridPhoto.setOnItemClickListener(new AdapterView.OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        if (list.size() != 0) {
          Bundle bundle = new Bundle();
          bundle.putSerializable(CustomParam.StartPhoto, (Serializable) list);
          bundle.putInt(CustomParam.PhotoPosition, i);
          Intent intent = new Intent(mContext, PhotoViewActivity.class);
          intent.putExtras(bundle);
          mContext.startActivity(intent);
        }
      }
    });

    gridPhoto.setAdapter(commentsPhotoAdapter);
  }
}

