package com.probim.bimenew.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.utils.JsonHelper;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :工程结构适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class AllProgtessItemAdapter
        extends RecyclerView.Adapter<AllProgtessItemAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private Context mContext;
    private List<SchedualListDto.DataDTO.ChildrenDTO> mDatas = new ArrayList<>();
    private OnRecycleItemListener mRecycleItemListener;
    private boolean isFromTask;

    public AllProgtessItemAdapter(Context mContext, boolean isFromTask, List<SchedualListDto.DataDTO.ChildrenDTO> datas) {
        this.mDatas = datas;
        this.mContext = mContext;
        this.isFromTask = isFromTask;
        this.mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_item_task, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SchedualListDto.DataDTO.ChildrenDTO
                bean = mDatas.get(position);

        holder.tvName.setText(bean.getName());
        //holder.tvCode.setText(bean.getBm_materialcode());
        if (bean.isSelected()) {
            Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
        } else {
            Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(JsonHelper.toJson(bean));
            // 检查属性是否存在
            if (jsonObject.has("children")) {
                // 文件夹
                holder.ivSelected.setVisibility(View.INVISIBLE);
                holder.ivNext.setVisibility(View.VISIBLE);
            } else {
                // 单选
                holder.ivSelected.setVisibility(View.VISIBLE);
                holder.ivNext.setVisibility(View.INVISIBLE);
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }


        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {

                    if (bean.isSelected()) {
                        bean.setSelected(false);
                        Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
                    } else {
                        if (isFromTask) {
                            for (int i = 0; i < mDatas.size(); i++) {
                                mDatas.get(i).setSelected(false);
                                notifyDataSetChanged();
                            }
                        }
                        bean.setSelected(true);
                        Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
                    }
                    mRecycleItemListener.OnRecycleItemClick(position, bean);
                }
            }
        });


    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_material)
        TextView tvName;
        @BindView(R.id.tv_code)
        TextView tvCode;
        @BindView(R.id.ivSelected)
        ImageView ivSelected;
        @BindView(R.id.iv_next)
        ImageView ivNext;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
