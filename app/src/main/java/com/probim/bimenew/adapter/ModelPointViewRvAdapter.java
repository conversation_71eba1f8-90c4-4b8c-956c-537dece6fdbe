package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.ModelPointViewRvAdapter.ViewHolder;
import com.probim.bimenew.model.ModelViewPointModel;
import com.probim.bimenew.model.PointViewDescModel;
import com.probim.bimenew.utils.Base64Utils;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;


/**
 * Description :模型-->视点  适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/4/11:04.
 */
public class ModelPointViewRvAdapter extends Adapter<ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private OnRecycleItemListener mRecycleItemListener;
    // 通过构造器传进来的数据
    private final List<ModelViewPointModel> mDatas;


    public ModelPointViewRvAdapter(Context mContext, List<ModelViewPointModel> mDatas) {
        this.mContext = mContext;
        this.mDatas = mDatas;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        //指定 viewHolder 的布局样式，并返回该 viewHolder
        return new ViewHolder(
                mLayoutInflater.inflate(R.layout.item_rv_model_point_view, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        // 绑定数据，就是给控件设置值
        ModelViewPointModel dto = mDatas.get(position);
        holder.tvViewName.setText(dto.getName());
        PointViewDescModel model = new Gson().fromJson(dto.getTag(), PointViewDescModel.class);
        if (TextUtils.isEmpty(model.getDescription())) {
            holder.tvViewIntro.setText("<无描述>");
        } else {
            holder.tvViewIntro.setText(model.getDescription());
        }

        if (dto.isIsDefault()) {
            holder.ivDefault.setVisibility(View.VISIBLE);
        } else {
            holder.ivDefault.setVisibility(View.INVISIBLE);
        }

        if (!TextUtils.isEmpty(dto.getSnapshot()) && dto.getSnapshot()
                .contains("base64")) {

            Glide.with(mContext)
                    .load(Base64Utils.decode(dto.getSnapshot().split("base64,")[1]))
                    .into(holder.iv_icon);

        } else {

            Glide.with(mContext)
                    .load(R.mipmap.img_bg_project_list)
                    .into(holder.iv_icon);
        }

        //item点击事件
        holder.linearLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {

                    mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), dto);

                }
            }
        });

        //打开更多
        holder.btnMore.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.onMore(holder.getAdapterPosition(), dto);
                }
            }
        });
        //分享视点
        holder.btnShare.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.OnShare(holder.getAdapterPosition(), dto);
                }
            }
        });
        //加载视点
        holder.btnLoading.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.OnLoading(holder.getAdapterPosition(), dto);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);

        void onMore(int pos, T o);

        void OnShare(int pos, T o);

        void OnLoading(int pos, T o);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.tv_point_name)
        TextView tvViewName;
        @BindView(R.id.tv_point_intro)
        TextView tvViewIntro;
        @BindView(R.id.iv_isDefault)
        TextView ivDefault;
        @BindView(R.id.btnLoading)
        Button btnLoading;
        @BindView(R.id.btnShare)
        Button btnShare;
        @BindView(R.id.btnMore)
        Button btnMore;
        @BindView(R.id.rl_content)
        RelativeLayout linearLayout;
        @BindView(R.id.iv_icon)
        ImageView iv_icon;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
