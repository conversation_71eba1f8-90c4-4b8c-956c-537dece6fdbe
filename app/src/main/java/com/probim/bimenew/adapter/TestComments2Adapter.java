package com.probim.bimenew.adapter;

import android.content.Context;
import android.os.AsyncTask;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.Comments;
import com.probim.bimenew.model.DocRelevance;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.StringUtils;
import com.probim.bimenew.utils.view.MyGridView;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/4/17:20.
 */
public class TestComments2Adapter extends Adapter<TestComments2Adapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<Comments.DataBean> mDatas;
  private String organizeId;
  List<PhotoDto> photoDtoList = new ArrayList<>();
  private List<DocRelevance> docRelevanceList = new ArrayList<>();
  private CommentsPhotoAdapter commentsPhotoAdapter;
  private CommentsDocAdapter commentsDocAdapter;
  private List<DocRelevance> DatasList = new ArrayList<>();

  public TestComments2Adapter(Context mContext, List<Comments.DataBean> mDatas, String organizeId) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
    this.organizeId = organizeId;
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_comments, null, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int i) {
    holder.setIsRecyclable(false);
    commentsPhotoAdapter = new CommentsPhotoAdapter(photoDtoList,
        mContext);
    commentsDocAdapter = new CommentsDocAdapter(mContext,
        docRelevanceList);
    Comments.DataBean dto = mDatas.get(i);
    holder.tvName.setText(dto.getBU_RealName());
    holder.tvDate.setText(dto.getCreateDate().replace("T", " "));
    holder.tvIntro.setText(dto.getContent());
    holder.ivDelete.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnItemDelete(i, dto.getBU_UserId());
        }
      }
    });

    holder.rvDoc.setAdapter(commentsDocAdapter);
    holder.gridPhoto.setAdapter(commentsPhotoAdapter);
    holder.gridPhoto.requestLayout();

    //请求文档和照片数据
    List<String> docIdList = new ArrayList<>();

    for (Comments.DataBean.TalkDocsBean talkDocsBean : dto.getTalkDocs()) {
      docIdList.add(talkDocsBean.getTargetID());
    }

    if (!TextUtils.isEmpty(StringUtils.dataToString(docIdList))) {

      new MyTask(StringUtils.dataToString(docIdList)).execute();
      Logger.t("IDS------------------>").e(docIdList.size() + "");

    }


  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_date)
    TextView tvDate;
    @BindView(R.id.tv_intro)
    TextView tvIntro;
    @BindView(R.id.grid_photo)
    MyGridView gridPhoto;
    @BindView(R.id.rv_doc)
    RecyclerView rvDoc;
    @BindView(R.id.iv_delete)
    ImageView ivDelete;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
      RecyclerView.LayoutManager manager = new LinearLayoutManager(view.getContext());
      rvDoc.setLayoutManager(manager);
      rvDoc.setNestedScrollingEnabled(false);

    }
  }


  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {


    void OnItemDelete(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  class MyTask extends AsyncTask<Void, Void, List<DocRelevance>> {

    private String fileIds;

    public MyTask(String fileIds) {
      this.fileIds = fileIds;
    }


    @Override
    protected List<DocRelevance> doInBackground(Void... voids) {
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", organizeId);
      params.put("FileIDs", fileIds);
      IssueController issueController = new IssueController();
      Logger.t("参数------------------>").e(params.toString());
      issueController.GetRelevance(params, new CallBack<List<DocRelevance>>() {
        private String url;

        @Override
        public void onSuccess(List<DocRelevance> docRelevances) {
          DatasList = docRelevances;
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });

      return DatasList;
    }

    @Override
    protected void onPostExecute(List<DocRelevance> docRelevances) {
      super.onPostExecute(docRelevances);
      photoDtoList.clear();
      docRelevanceList.clear();
      Logger.t("异步数据------------------>").e(docRelevances.size() + "   ");
      for (DocRelevance docRelevance : docRelevances) {

        String url = null;
        if (".jpeg".equals(docRelevance.getFileExtensions()) || ".png"
            .equals(docRelevance.getFileExtensions()) || ".jpg"
            .equals(docRelevance.getFileExtensions()) || ".PNG"
            .equals(docRelevance.getFileExtensions())) {
          //照片格式

          if (TextUtils.isEmpty(docRelevance.getFilePath())) {
            //隐藏文件
            url = BaseApp.getBimUrl() + ApiConstant.LOAD_HIDE + "?ProjectID=" + organizeId
                + "&FileId=" + docRelevance
                .getFileId() + "&FileType=Issue";

          } else {
            //不是隐藏文件
            url = BaseApp.getBimUrl() + ApiConstant.LOAD_DOC + "?ProjectID=" + organizeId
                + "&keyValue=" + docRelevance
                .getFileId() + "&FileKind=File";
          }

          photoDtoList
              .add(new PhotoDto(url));


        } else {
          //其余文档
          docRelevanceList.add(docRelevance);

        }

      }
      commentsPhotoAdapter.notifyDataSetChanged();
      commentsDocAdapter.notifyDataSetChanged();
    }
  }

}
