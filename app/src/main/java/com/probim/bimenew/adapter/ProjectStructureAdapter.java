package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.result.ProjectStrcutreResult;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :工程结构适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class ProjectStructureAdapter
    extends RecyclerView.Adapter<ProjectStructureAdapter.ViewHolder> {

  private Context mContext;
  private List<ProjectStrcutreResult.DataBean> mDatas = new ArrayList<>();
  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;
  private List<Boolean> isClicks;

  public ProjectStructureAdapter(Context mContext) {
    this.mContext = mContext;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_step_project, parent, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    ProjectStrcutreResult.DataBean
        bean = mDatas.get(position);

    holder.tvProjectStep.setText(bean.getEc_name());

    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {

          for (int i = 0; i < isClicks.size(); i++) {
            isClicks.set(i, false);
          }
          isClicks.set(position, true);
          notifyDataSetChanged();
          mRecycleItemListener.OnRecycleItemClick(position, bean);
        }
      }
    });

    if (isClicks.get(position)) {
      holder.tvProjectStep.setTextColor(mContext.getColor(R.color.red));
    } else {
      holder.tvProjectStep.setTextColor(mContext.getColor(R.color.black));
    }
  }

  @Override public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_project_step) TextView tvProjectStep;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  public void setData(List<ProjectStrcutreResult.DataBean> listBeans) {
    mDatas.clear();
    mDatas.addAll(listBeans);
    isClicks = new ArrayList<>();
    for (int i = 0; i < mDatas.size(); i++) {
      isClicks.add(false);
    }
    notifyDataSetChanged();
  }
}
