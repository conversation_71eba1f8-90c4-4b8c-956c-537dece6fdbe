package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.media.MediaPlayer;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.CheckRecordListActivity;
import com.probim.bimenew.activity.PhotoViewActivity;
import com.probim.bimenew.activity.check.FullScreenActivity;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.result.CheckDetailsDto;
import com.probim.bimenew.utils.view.MyGridView;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/4/17:20.
 */
public class CheckRecordRvAdapterBack extends Adapter<CheckRecordRvAdapterBack.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private MediaPlayer mediaPlayer;
  private OnRecycleItemListener mRecycleItemListener;
  private final int TYPE_TEXT = 0;
  private final int TYPE_IMG = 1;
  private final int TYPE_MORE_IMG = 2;
  private final int TYPE_VIDEO = 5;

  // 通过构造器传进来的数据
  private List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean> mDatas;
  private String organizeId;

  public CheckRecordRvAdapterBack(Context mContext,
      List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean> mDatas,
      String organizeId) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
    this.organizeId = organizeId;
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

    View view;
    if (i == TYPE_IMG) {
      // 1-3 张图片
      view = mLayoutInflater.inflate(R.layout.item_check_record_video, viewGroup, false);
      return new ImageViewHolder(view);
    } else if (i == TYPE_MORE_IMG) {
      //多张图片
      view = mLayoutInflater.inflate(R.layout.item_check_record_more_img, viewGroup, false);
      return new ImageViewMoreHolder(view);
    } else {
      // 文本
      view = mLayoutInflater.inflate(R.layout.item_check_record_text, viewGroup, false);
      return new ViewHolder(view);
    }
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int i) {

    CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean dto = mDatas.get(i);
    holder.tvName.setText(dto.getRectificationOperator());
    holder.tvDate.setText(dto.getCreateDate().replace("T", " "));
    holder.tvIntro.setText(dto.getRectificationRemark());
    if (dto.getRectificationOperator().length() == 1) {
      holder.tvImgHead.setText(dto.getRectificationOperator());
    } else {
      holder.tvImgHead.setText(dto.getRectificationOperator()
          .substring(dto.getRectificationOperator().length() - 1));
    }

    if (dto.getAer_counterpart() != null) {

      switch (dto.getAer_counterpart()) {
        case "需整改":

          if (i == mDatas.size() - 1) {
            holder.tvCheckResult.setBackgroundResource(R.drawable.bg_tag_record_rectification);
            holder.tvCheckResult.setText(dto.getAer_counterpart());
            holder.tvRole.setText("检查人");
          } else {
            holder.tvCheckResult.setBackgroundResource(R.drawable.bg_tag_record_rectification);
            holder.tvCheckResult.setText(dto.getAer_counterpart());
            holder.tvRole.setText("验收人");
          }
          break;
        case "验收中":
          holder.tvCheckResult.setBackgroundResource(R.drawable.bg_tag_record_applying);
          holder.tvCheckResult.setText(dto.getAer_counterpart());
          holder.tvRole.setText("整改人");
          break;
        case "已合格":
          holder.tvCheckResult.setBackgroundResource(R.drawable.bg_tag_record_ok);
          holder.tvCheckResult.setText(dto.getAer_counterpart());
          holder.tvRole.setText("验收人");
          break;
      }
    }
    /*if (2 == dto.getRectificationOperateFlag()) {
      holder.tvCheckResult.setTextColor(Color.parseColor("#417fcd"));
      holder.tvCheckResult.setBackgroundResource(R.drawable.shape_check_agian);
      holder.tvCheckResult.setText("复检");
      holder.tvRole.setText("整改人");
    } else {

      holder.tvCheckResult.setTextColor(Color.parseColor("#417fcd"));
      holder.tvCheckResult.setBackgroundResource(R.drawable.shape_check_agian);
      holder.tvCheckResult.setText(dto.getRectificationCheckResult());
      holder.tvRole.setText("检查人");
    }*/
    //初始化 文件数据


    lodaData(holder.gridPhoto, dto.getAttachments(), holder.rlVideo, holder.ivVideo);
  }

  @Override public int getItemViewType(int position) {
    CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean dto = mDatas.get(position);
    int size = dto.getAttachments().size();

    if (size == 0) {
      return TYPE_TEXT;

    } else if (size >= 1 && size <= 3) {

      return TYPE_IMG;

    } else if (size > 3) {

      return TYPE_MORE_IMG;
    }

    return super.getItemViewType(position);
  }

  private void lodaData(GridView gridView,
      List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean.AttachmentsBean> attachmentsBeanLista,
      RelativeLayout rl, ImageView ivVideo) {

    //拼接photoUrl
    List<PhotoDto> photoDtoList = new ArrayList<>();

    for (CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean.AttachmentsBean bean : attachmentsBeanLista) {

      if (isVideoType(bean.getAttachmentType())) {
        rl.setVisibility(View.VISIBLE);
        // Glide.with(mContext).load(R.drawable.ic_launcher).into(ivVideo);
        String url = Hawk.get(CustomParam.Base_URL) + bean.getAttachmentUrl();
        ivVideo.setOnClickListener(new OnClickListener() {
          @Override public void onClick(View v) {
            Intent intent = new Intent(mContext, FullScreenActivity.class);
            intent.putExtra(CustomParam.VIDEOURL, url);
            mContext.startActivity(intent);
          }
        });
      } else {
        rl.setVisibility(View.GONE);
        String url = Hawk.get(CustomParam.Base_URL) + bean.getAttachmentUrl();
        photoDtoList.add(new PhotoDto(url));
      }
    }

    //适配器
    CommentsPhotoAdapter commentsPhotoAdapter = new CommentsPhotoAdapter(photoDtoList,
        mContext);
    ((CheckRecordListActivity) mContext).runOnUiThread(new Runnable() {
      @Override public void run() {

        gridView.setAdapter(commentsPhotoAdapter);
      }
    });

    gridView.setOnItemClickListener(new OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        if (photoDtoList.size() != 0) {
          Bundle bundle = new Bundle();
          bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
          bundle.putInt(CustomParam.PhotoPosition, i);
          Intent intent = new Intent(mContext, PhotoViewActivity.class);
          intent.putExtras(bundle);
          mContext.startActivity(intent);
        }
      }
    });
  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.tv_img_head) TextView tvImgHead;
    @BindView(R.id.tv_name) TextView tvName;
    @BindView(R.id.tv_intro) TextView tvIntro;
    @BindView(R.id.grid_photo) MyGridView gridPhoto;
    @BindView(R.id.lin_record) LinearLayout linRecord;
    @BindView(R.id.tv_role) TextView tvRole;
    @BindView(R.id.tv_date) TextView tvDate;
    @BindView(R.id.tv_check_result) TextView tvCheckResult;
    @BindView(R.id.iv_record) ImageView ivRecord;
    @BindView(R.id.iv_video) ImageView ivVideo;
    @BindView(R.id.rl_video) RelativeLayout rlVideo;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnPlay(String url);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  //根据图片名称取得对应图片
  public int getDrawable(String name) {

    ApplicationInfo appInfo = mContext.getApplicationInfo();
    int resID = mContext.getResources().getIdentifier(name, "drawable", appInfo.packageName);
    //解析资源文件夹下，id为resID的图片
    return resID;
  }

  /**
   * 播放音频
   */
  private void playMedia(String filePath, ImageView iv) {

    ((CheckRecordListActivity) mContext).runOnUiThread(new Runnable() {
      @Override public void run() {

        Glide.with(mContext).load(R.drawable.recordinganimation).into(iv);
      }
    });

    mediaPlayer = new MediaPlayer();
    try {
      mediaPlayer.setDataSource(filePath);

      mediaPlayer.prepare();

      mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
        @Override
        public void onPrepared(MediaPlayer mp) {
          mediaPlayer.start();
        }
      });
      mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
        @Override public void onCompletion(MediaPlayer mediaPlayer) {
          stopPlayMedia(iv);
        }
      });
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 停止播放r
   */
  private void stopPlayMedia(ImageView iv) {

    mediaPlayer.stop();
    mediaPlayer.reset();
    mediaPlayer.release();
    mediaPlayer = null;
    ((CheckRecordListActivity) mContext).runOnUiThread(new Runnable() {
      @Override public void run() {

        Glide.with(mContext).load(R.mipmap.ic_recoarding_play).into(iv);
      }
    });
  }

  /**
   * 获取文件类型
   */
  private boolean isVideoType(String fileType) {

    if (fileType.equalsIgnoreCase(".mp4") || fileType.equalsIgnoreCase(".avi")) {
      //选取视频
      return true;
    }
    return false;
  }

  public int dpToPx(float dp) {
    float px = mContext.getResources().getDisplayMetrics().density;
    return (int) (dp * px + 0.5f);
  }

  private class ImageViewHolder extends ViewHolder {
    public ImageViewHolder(View view) {
      super(view);
    }
  }

  private class ImageViewMoreHolder extends ViewHolder {
    public ImageViewMoreHolder(View view) {
      super(view);
    }
  }
}
