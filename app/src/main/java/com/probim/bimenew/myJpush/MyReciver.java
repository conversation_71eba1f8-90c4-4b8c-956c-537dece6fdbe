package com.probim.bimenew.myJpush;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;

import com.orhanobut.logger.Logger;
import com.probim.bimenew.activity.SplashActivity;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.event.EventBean;
import com.probim.bimenew.event.EventBusUtils;

import java.util.List;

import cn.jpush.android.api.CustomMessage;
import cn.jpush.android.api.NotificationMessage;
import cn.jpush.android.service.JPushMessageReceiver;

public class MyReciver extends JPushMessageReceiver {
    @Override
    public void onMessage(Context context, CustomMessage customMessage) {
        super.onMessage(context, customMessage);
    }

    @Override
    public void onNotifyMessageArrived(Context context, NotificationMessage notificationMessage) {
        super.onNotifyMessageArrived(context, notificationMessage);
        Logger.e(notificationMessage.toString());
        EventBusUtils.sendEvent(new EventBean(ApiConstant.CODE_TYPE_MSG,new String()));

    }

    @Override
    public void onNotifyMessageOpened(Context context, NotificationMessage notificationMessage) {

        String pName = "com.probim.bimenew";
        int uid = getPackageUid(context, pName);
        if(uid !=  0){
            boolean rstA = isAppRunning(context, pName);
            boolean rstB = isProcessRunning(context, uid);
            if(rstA||rstB){
                //指定包名的程序正在运行中
                Logger.e("aaaaa"+rstA+"---"+rstB);

            }else{
                //指定包名的程序未在运行中
                Logger.e("bbbbb");
                Intent intent = new Intent(context, SplashActivity.class);
                context.startActivity(intent);
            }
        }else{
            //应用未安装
        }

    }
    /**
     * 方法描述：判断某一应用是否正在运行
     * @param context   上下文
     * @param packageName 应用的包名
     * @return true 表示正在运行，false 表示没有运行
     */
    public static boolean isAppRunning(Context context, String packageName) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> list = am.getRunningTasks(100);
        if (list.size() <= 0) {
            return false;
        }
        for (ActivityManager.RunningTaskInfo info : list) {
            if (info.baseActivity.getPackageName().equals(packageName)) {
                return true;
            }
        }
        return false;
    }
    //获取已安装应用的 uid，-1 表示未安装此应用或程序异常
    public static int getPackageUid(Context context, String packageName) {
        try {
            ApplicationInfo applicationInfo = context.getPackageManager().getApplicationInfo(packageName, 0);
            if (applicationInfo != null) {
                Logger.d(applicationInfo.uid);
                return applicationInfo.uid;
            }
        } catch (Exception e) {
            return -1;
        }
        return -1;
    }
    /**
     * 判断某一 uid 的程序是否有正在运行的进程，即是否存活
     * @param context   上下文
     * @param uid 已安装应用的 uid
     * @return true 表示正在运行，false 表示没有运行
     */
    public static boolean isProcessRunning(Context context, int uid) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningServiceInfo>  runningServiceInfos = am.getRunningServices(200);
        if (runningServiceInfos.size()  > 0) {
            for (ActivityManager.RunningServiceInfo appProcess : runningServiceInfos){
                if (uid == appProcess.uid) {
                    return true;
                }
            }
        }
        return false;
    }

}