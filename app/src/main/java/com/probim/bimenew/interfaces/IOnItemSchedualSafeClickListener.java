package com.probim.bimenew.interfaces;

/**
 * Description :RecyclerView Item点击事件
 * Author : <PERSON>
 * <PERSON><PERSON>  : <EMAIL>
 * Date   : 2018/6/14/10:11.
 */

public interface IOnItemSchedualSafeClickListener<T> {

    /**
     * RecyclerView Item点击事件
     */
    void onClick(int pos, T o);

    void onDelete(int pos, T o);

    void onClose(int pos, T o);

    void showNameChange(int pos, String s);

    void showMeasureChange(int pos, String s);

}
