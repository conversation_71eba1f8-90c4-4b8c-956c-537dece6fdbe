package com.probim.bimenew.interfaces;

/**
 * Description :RecyclerView Item点击事件
 * Author : <PERSON>
 * <PERSON><PERSON>  : <EMAIL>
 * Date   : 2018/6/14/10:11.
 */

public interface IOnItemSchedualMaterailClickListener<T> {

  /**
   * RecyclerView Item点击事件
   */
  void onClick(int pos, T o);

  void onDelete(int pos, T o);

  void onClose(int pos, T o);

  void onMaterailClick(int pos, T o);

  void showSizeChange(int pos,String s);

  void showUnitChange(int pos,String s);

  void showRemarkChange(int pos,String s);

  void showProductChange(int pos,String s);

  void showApproChange(int pos,String s);

  void onPartsClick(int pos, T o);

  void onEstimeClick(int pos, T o);
}
