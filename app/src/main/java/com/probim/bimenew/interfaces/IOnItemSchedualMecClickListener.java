package com.probim.bimenew.interfaces;

/**
 * Description :RecyclerView Item点击事件
 * Author : <PERSON>
 * <PERSON><PERSON>  : <EMAIL>
 * Date   : 2018/6/14/10:11.
 */

public interface IOnItemSchedualMecClickListener<T> {

  /**
   * RecyclerView Item点击事件
   */
  void onClick(int pos, T o);

  void onDelete(int pos, T o);

  void onClose(int pos, T o);

  void onMecClick(int pos, T o);

  void showAddChange(int pos,String s);

  void showLiveChange(int pos,String s);

  void showLeijiChange(int pos,String s);

  void showEdt(int pos, String s);
}
