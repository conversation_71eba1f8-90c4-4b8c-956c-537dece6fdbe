package com.probim.bimenew.interfaces;

/**
 * Description :RecyclerView Item点击事件
 * Author : <PERSON>
 * Email  : <EMAIL>
 * Date   : 2018/6/14/10:11.
 */

public interface IOnItemSchedualTaskPlanClickListener<T> {

    /**
     * RecyclerView Item点击事件
     */
    void onClick(int pos, T o);

    void onDelete(int pos, T o);

    void onClose(int pos, T o);

    void onTaskClick(int pos, T o);

    void showEdtWhyChange(int pos, String s);

    void showEdtPlantratioChange(int pos, String s);

    void showEdtActualratioChange(int pos, String s);

    void showEdtAddratioChange(int pos, String s);

    void showEdtTomorrowratioChange(int pos, String s);

    void showStateChange(int pos, String s);


}
