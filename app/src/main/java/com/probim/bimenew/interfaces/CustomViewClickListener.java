package com.probim.bimenew.interfaces;

import android.view.View;

/**
 * Description :
 * Author : <PERSON>
 * Email  : <EMAIL>
 * Date   : 2020/7/16/16:29
 */
public abstract class CustomViewClickListener implements View.OnClickListener {

    private long lastTime;
    public CustomViewClickListener() {
    }

    @Override
    public void onClick(View v) {
        long nowTime = System.currentTimeMillis();
        long timeInterval = 2000L;
        if (nowTime - lastTime > timeInterval) {
            //单次点击事件
            onSingleClick();
            lastTime = nowTime;
        } else {
            //快速点击事件
            onFastClick();
        }
    }

    protected abstract void onSingleClick();

    protected abstract void onFastClick();
}
