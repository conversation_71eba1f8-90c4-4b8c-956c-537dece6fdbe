package com.probim.bimenew.application;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.os.StrictMode;
import android.text.TextUtils;
import android.util.Log;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.AndroidLogAdapter;
import com.orhanobut.logger.FormatStrategy;
import com.orhanobut.logger.Logger;
import com.orhanobut.logger.PrettyFormatStrategy;
import com.probim.bimenew.BuildConfig;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.crash.CrashHandler;
import com.probim.bimenew.db.DaoMaster;
import com.probim.bimenew.db.DaoMaster.OpenHelper;
import com.probim.bimenew.db.DaoSession;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.utils.X5NetService;
import com.tencent.smtt.sdk.QbSdk;
import com.tencent.smtt.sdk.QbSdk.PreInitCallback;
import com.tencent.smtt.sdk.TbsListener;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.socialize.PlatformConfig;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;

import cn.jpush.android.api.JPushInterface;

/**
 * Description :app Author : Gary Email : <EMAIL> Date : 2018/6/12/14:10.
 */
public class BaseApp extends Application {

    private static final String TAG = "BIMe-H5";
    public static BaseApp app;
    // 以下属性应用于整个应用程序，合理利用资源，减少资源浪费
    private static Context mContext;// 上下文
    private static Thread mMainThread;// 主线程
    private static long mMainThreadId;// 主线程id
    private static Looper mMainLooper;// 循环队列
    private static Handler mHandler;// 主线程Handler
    private DaoSession daoSession;
    private DaoMaster daoMaster;
    private ProjectBeanDao projectBeanDao;
    private int isBackCount;
    private int isHaveCount;
    private Activity currentActivity;
    private HashMap<String, String> infoMap = new HashMap<>();

    public static BaseApp getInstance() {
        return app;
    }

    /**
     * 重启当前应用
     */
    public static void restart() {
        Intent intent =
                mContext.getPackageManager().getLaunchIntentForPackage(mContext.getPackageName());
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        mContext.startActivity(intent);
    }

    public static String getBimUrl() {

        String code = Hawk.get(CustomParam.CompanyCode);

        if ("000000".equals(code)) {

            return "https://bimcomposer.bim365.com.cn";

        } else if ("000002".equals(code)) {

            return "https://bimcomposer.probim.cn";
        } else {
            if (Hawk.contains(CustomParam.Bim_URL)) {

                String bimUrl = Hawk.get(CustomParam.Bim_URL);

                return bimUrl;
            }
        }
        return "";
    }

    public static String getDocUrl() {
        if (Hawk.contains(CustomParam.Base_URL)) {

            String docUrl = Hawk.get(CustomParam.Base_URL);

            return docUrl;
        }
        return "";
    }

    /**
     * 判断当前应用是否是debug状态
     */
    public static boolean isApkInDebug(Context context) {
        try {
            ApplicationInfo info = context.getApplicationInfo();
            return (info.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {
            return false;
        }
    }

    public static Context getContext() {
        return mContext;
    }

    public static void setContext(Context mContext) {
        BaseApp.mContext = mContext;
    }

    public static Thread getMainThread() {
        return mMainThread;
    }

    public static void setMainThread(Thread mMainThread) {
        BaseApp.mMainThread = mMainThread;
    }

    public static long getMainThreadId() {
        return mMainThreadId;
    }

    public static void setMainThreadId(long mMainThreadId) {
        BaseApp.mMainThreadId = mMainThreadId;
    }

    public static Looper getMainThreadLooper() {
        return mMainLooper;
    }

    public static void setMainThreadLooper(Looper mMainLooper) {
        BaseApp.mMainLooper = mMainLooper;
    }

    public static Handler getMainHandler() {
        return mHandler;
    }

    public static void setMainHandler(Handler mHandler) {
        BaseApp.mHandler = mHandler;
    }

    /**
     * @return
     */
    public static String getPackageNameStr() {

        String pcName = mContext.getPackageName();
        if (!TextUtils.isEmpty(pcName)) {
            return pcName;
        }
        return "";
    }

    public void insertInfo(String key, String value) {
        infoMap.put(key, value);
    }

    public String selectInfo(String key) {
        return infoMap.get(key);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        app = this;
        JPushInterface.setDebugMode(true);
        JPushInterface.init(this);
        //友盟配置
        initUM();
        //MMKV
        // 初始化Logger
        if (BuildConfig.DEBUG) {
            //testIO();

            initLog();
            // BlockCanary.install(this, new AppBlockCanaryContext()).start();
        }
        // 初始化SP工具类
        Hawk.init(this).build();
        // 初始化Bugly
        // 建议在测试阶段建议设置成true，发布时设置为false
        //CrashReport.initCrashReport(getApplicationContext(), "eb3bdcde46", false);
        // 初始化X5浏览器
        initX5();
        preInitX5Core();
        // Normal app init code...
        // 对全局属性赋值
        mContext = getApplicationContext();
        mMainThread = Thread.currentThread();
        mMainThreadId = android.os.Process.myTid();
        mHandler = new Handler();


        JPushInterface.init(this);

        registerActivityLife();

        getStack();

        CrashHandler.getInstance().init(this);


        //Logger.t("进程名字----->").e(getProcess());
    }

    /**
     * logger初始化
     */
    private void initLog() {
        FormatStrategy formatStrategy = PrettyFormatStrategy.newBuilder()
                .showThreadInfo(false)  // (Optional) Whether to show thread info or not. Default true
                .methodCount(0)         // (Optional) How many method line to show. Default 2
                .methodOffset(7)        // (Optional) Hides internal method calls up to offset. Default 5
                //.logStrategy(customLog) // (Optional) Changes the log strategy to print out. Default LogCat
                .tag("My custom tag")   // (Optional) Global tag for every log. Default PRETTY_LOGGER
                .build();

        Logger.addLogAdapter(new AndroidLogAdapter());

    }

    /**
     * 获取app内存大小
     */
    private void getStack() {
        ActivityManager manager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo info = new ActivityManager.MemoryInfo();
        manager.getMemoryInfo(info);
        Runtime rt = Runtime.getRuntime();
        long maxMemory = rt.maxMemory();
        //getMemoryClass()和getLargeMemoryClass()方法最终读取的仍然是dalvik.vm.heapgrowthlimit和dalvik.vm.heapsize的值。
        // 而且，dalvik.vm.heapsize默认值为16M，这也是解释了google的原生OS默认值是16M了。
        // 而dalvik.vm.heapgrowthlimit和dalvik.vm.heapsize的值各个手机厂家的OS会对这个值进行修改，所以存在差异
        // 设置 android:largeHeap="true"时  Runtime.maxMemory 会变为  dalvik.vm.heapsize
        Logger.t("Memory").e("Runtime.maxMemory:" + maxMemory / (1024 * 1024));
        Logger.t("Memory").e("dalvik.vm.heapgrowthlimit:" + Long.toString(manager.getMemoryClass()));
        Logger.t("Memory").e("dalvik.vm.heapsize:" + Long.toString(manager.getLargeMemoryClass()));
        Logger.t("Memory").e("系统总内存:" + (info.totalMem / (1024 * 1024)) + "M");
        Logger.t("Memory").e("系统剩余内存:" + (info.availMem / (1024 * 1024)) + "M");
        Logger.t("Memory").e("系统是否处于低内存运行：" + info.lowMemory);
        Logger.t("Memory").e("系统剩余内存低于:" + (info.threshold / (1024 * 1024)) + "M时为低内存运行");
    }

    private void initUM() {
        UMConfigure.init(this, "5b6953eea40fa33fd7000282"
                , "umeng", UMConfigure.DEVICE_TYPE_PHONE, "");

        PlatformConfig.setWeixin("wx0553fe2b259ca859", "ce5eb5a089f1c446fbfaaf835f8b9b5b");

        PlatformConfig.setQQZone("101511727", "257c162bf4d39bba91f5d8626c8041cb");
    }

    /**
     * 获取版本号
     */
    public String getVersionName() {
        String versionName = "";
        PackageManager packageManager = getPackageManager();
        try {
            PackageInfo packageInfo = packageManager.getPackageInfo(getPackageName(), 0);
            versionName = packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return versionName;
    }

    // 获取SSl秘钥
    public InputStream[] GetProbimtKey() throws IOException {
        InputStream stream = getAssets().open("STAR_probim_cn.cer");
        InputStream[] is = new InputStream[]{stream};
        return is;
    }

    // 获取SSl秘钥
    public InputStream[] GetBimt365Key() throws IOException {
        InputStream stream = getAssets().open("STAR_bim365_com_cn.cer");
        InputStream[] is = new InputStream[]{stream};
        return is;
    }

    private void preInitX5Core() {
        //预加载x5内核
        Intent intent = new Intent(this, X5NetService.class);
        startService(intent);
    }

    /**
     * 初始化X5
     */
    private void initX5() {
        /* 设置允许移动网络下进行内核下载。默认不下载，会导致部分一直用移动网络的用户无法使用x5内核 */
        QbSdk.setDownloadWithoutWifi(true);

        QbSdk.setCoreMinVersion(QbSdk.CORE_VER_ENABLE_202112);
        /* SDK内核初始化周期回调，包括 下载、安装、加载 */

        QbSdk.setTbsListener(new TbsListener() {

            /**
             * @param stateCode 用户可处理错误码请参考{@link com.tencent.smtt.sdk.TbsCommonCode}
             */
            @Override
            public void onDownloadFinish(int stateCode) {
                Log.i(TAG, "onDownloadFinished: " + stateCode);
            }

            /**
             * @param stateCode 用户可处理错误码请参考{@link com.tencent.smtt.sdk.TbsCommonCode}
             */
            @Override
            public void onInstallFinish(int stateCode) {
                Log.i(TAG, "onInstallFinished: " + stateCode);
            }

            /**
             * 首次安装应用，会触发内核下载，此时会有内核下载的进度回调。
             * @param progress 0 - 100
             */
            @Override
            public void onDownloadProgress(int progress) {
                Log.i(TAG, "Core Downloading: " + progress);
            }
        });

        /* 此过程包括X5内核的下载、预初始化，接入方不需要接管处理x5的初始化流程，希望无感接入 */
        QbSdk.initX5Environment(this, new PreInitCallback() {
            @Override
            public void onCoreInitFinished() {
                // 内核初始化完成，可能为系统内核，也可能为系统内核
            }

            /**
             * 预初始化结束
             * 由于X5内核体积较大，需要依赖wifi网络下发，所以当内核不存在的时候，默认会回调false，此时将会使用系统内核代替
             * 内核下发请求发起有24小时间隔，卸载重装、调整系统时间24小时后都可重置
             * 调试阶段建议通过 WebView 访问 debugtbs.qq.com -> 安装线上内核 解决
             * @param isX5 是否使用X5内核
             */
            @Override
            public void onViewInitFinished(boolean isX5) {
                Log.i(TAG, "onViewInitFinished: " + isX5);
                // hint: you can use QbSdk.getX5CoreLoadHelp(context) anytime to get help.
            }
        });
    }

    /**
     * 取得DaoMaster * * @param context * @return
     */
    public DaoMaster getDaoMaster(Context context) {
        if (daoMaster == null) {
            OpenHelper helper = new DaoMaster.DevOpenHelper(context, "Project_params.db", null);
            daoMaster = new DaoMaster(helper.getWritableDatabase());
        }
        return daoMaster;
    }

    /**
     * 取得DaoSession * * @param context * @return
     */
    public DaoSession getDaoSession(Context context) {
        if (daoSession == null) {
            if (daoMaster == null) {
                daoMaster = getDaoMaster(context);
            }
            daoSession = daoMaster.newSession();
        }
        return daoSession;
    }

    public ProjectBeanDao getDao() {
        if (projectBeanDao == null) {
            // **原博主代码为noteDao=daoSession.getNoteDao(); * daoSession这个对象会空啊，所以直接改为了getDaoSession(mInstance) *
            // daoSession空了间接导致getInstance方法返回空。 **//*
            projectBeanDao = getDaoSession(mContext).getProjectBeanDao();
        }
        return projectBeanDao;
    }

    //取得进程名
    private String getProcess() {
        ActivityManager am = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningApps = am.getRunningAppProcesses();
        if (runningApps == null) {
            return null;
        }
        for (ActivityManager.RunningAppProcessInfo procInfo : runningApps) {
            if (procInfo.pid == Process.myPid()) {
                return procInfo.processName;
            }
        }
        return null;
    }

    public DaoSession getDaoSession() {
        return daoSession;
    }

    /***
     *
     * OnTrimMemory的参数是一个int数值，代表不同的内存状态：
     *
     * TRIM_MEMORY_COMPLETE：内存不足，并且该进程在后台进程列表最后一个，马上就要被清理
     *
     * TRIM_MEMORY_MODERATE：内存不足，并且该进程在后台进程列表的中部。
     *
     * TRIM_MEMORY_BACKGROUND：内存不足，并且该进程是后台进程。
     *
     * TRIM_MEMORY_UI_HIDDEN：内存不足，并且该进程的UI已经不可见了。
     *
     * TRIM_MEMORY_RUNNING_CRITICAL：内存不足(后台进程不足3个)，并且该进程优先级比较高，需要清理内存
     *
     * TRIM_MEMORY_RUNNING_LOW：内存不足(后台进程不足5个)，并且该进程优先级比较高，需要清理内存
     *
     * TRIM_MEMORY_RUNNING_MODERATE：内存不足(后台进程超过5个)，并且该进程优先级比较高，需要清理内存.
     *
     * @param level
     */
    @Override
    public void onTrimMemory(int level) {
        Logger.e("内存----------->" + level);
        switch (level) {
            case TRIM_MEMORY_RUNNING_LOW:

                //Toast.makeText(mContext, "aaa", Toast.LENGTH_SHORT).show();
                // 低内存
                if (!JPushInterface.isPushStopped(getApplicationContext())) {
                    //Toast.makeText(mContext, "bbb", Toast.LENGTH_SHORT).show();
                    Logger.e("内存杀死----------->");
                    JPushInterface.stopPush(getApplicationContext());
                } else {
                    // Toast.makeText(mContext, "ccc", Toast.LENGTH_SHORT).show();

                }
                break;
            default:
                break;
        }
        super.onTrimMemory(level);


    }

    private void registerActivityLife() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle bundle) {
            }

            @Override
            public void onActivityStarted(Activity activity) {
                isHaveCount++;
            }

            @Override
            public void onActivityResumed(Activity activity) {
                currentActivity = activity;
                isBackCount++;

            }

            @Override
            public void onActivityPaused(Activity activity) {
            }

            @Override
            public void onActivityStopped(Activity activity) {
                isHaveCount--;

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
            }

            @Override
            public void onActivityDestroyed(Activity activity) {
            }
        });
    }

    //推送的位置可以拿到当前显示的页面
    public Activity getCurrentActivity() {
        return currentActivity;
    }

    public int getIsBackCount() {
        return isBackCount;
    }

    public int getIsHaveCount() {
        return isHaveCount;
    }

    /**
     * IO 测试工具
     */
    private void testIO() {

        if (BuildConfig.DEBUG) {
            StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder()
                    .detectDiskReads()
                    .detectDiskWrites()
                    .detectNetwork()   // or .detectAll() for all detectable problems
                    .penaltyLog()
                    .build());
            StrictMode.setVmPolicy(new StrictMode.VmPolicy.Builder()
                    .detectLeakedSqlLiteObjects()
                    .detectLeakedClosableObjects()
                    .penaltyLog()
                    .penaltyDeath()
                    .build());
        }

    }

}
