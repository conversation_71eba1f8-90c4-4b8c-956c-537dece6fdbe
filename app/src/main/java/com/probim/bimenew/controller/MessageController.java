package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.model.BaseModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.MessageResult;
import java.io.IOException;
import java.util.HashMap;
import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :消息适配器 Author : Gary Email  : <EMAIL> Date   :
 * 2018/9/20/11:24.
 */
public class MessageController {

  private Context mContext = BaseApp.getContext();
  private String BASE_URL = Hawk.get(CustomParam.Base_URL);

  private String BIME_URL = Hawk.get(CustomParam.Bim_URL);

  private String WEB_URL = Hawk.get(CustomParam.Web_URL);

  /**
   * 获取消息列表
   */
  public void GetMessageList(HashMap<String, String> params, final CallBack<MessageResult> callBack) {
    OkHttpHelper.getInstance(mContext).getAsync(BASE_URL + ApiConstant.MESSAGELIST, params,
        new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) throws IOException {
            Logger.t("消息列表---------").e(str);
            if (TextUtils.isEmpty(str)) {
              return;
            }
            callBack.onSuccess(new Gson().fromJson(str, MessageResult.class));
          }

          @Override
          public void onFailure(Call call, IOException e) {

          }

          @Override
          public void onResponse(Call call, Response response) throws IOException {

          }
        });
  }


  /**
   * 全部消息设为已读
   */
  public void SetMessageAllRead(HashMap<String, String> params,
      final CallBack<BaseModel> callBack) {
    OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.SET_MESSAGE_READ, params,
        new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) throws IOException {
            Logger.t("全部消息设为已读---------").e(str);
            if (TextUtils.isEmpty(str)) {
              return;
            }
            callBack.onSuccess(new Gson().fromJson(str, BaseModel.class));
          }

          @Override
          public void onFailure(Call call, IOException e) {

          }

          @Override
          public void onResponse(Call call, Response response) throws IOException {

          }
        });
  }


  /**
   * 单个消息设为已读
   */
  public void SetMessageItemRead(HashMap<String, String> params,
      final CallBack<BaseModel> callBack) {
    OkHttpHelper.getInstance(mContext)
        .postJsonAsync(BASE_URL + ApiConstant.SET_MESSAGE_ITEM_READ, params,
            new IHttpRequestHandler1() {
              @Override
              public void onResponse(Call call, Response response, String str) throws IOException {
                Logger.t("单个消息设为已读---------").e(str);
                if (TextUtils.isEmpty(str)) {
                  return;
                }
                callBack.onSuccess(new Gson().fromJson(str, BaseModel.class));
              }

              @Override
              public void onFailure(Call call, IOException e) {

              }

              @Override
              public void onResponse(Call call, Response response) throws IOException {

              }
            });
  }


  /**
   * 删除单条消息
   */
  public void DeleteMessageItem(HashMap<String, String> params,
      final CallBack<BaseModel> callBack) {
    OkHttpHelper.getInstance(mContext).postFormBody(BASE_URL + ApiConstant.DELETE_MESSAGE_ITEM,
        params, new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) throws IOException {
            Logger.t("删除单条消息").e(str);
            if (TextUtils.isEmpty(str)) {
              return;
            }
            callBack.onSuccess(new Gson().fromJson(str, BaseModel.class));

          }

          @Override
          public void onFailure(Call call, IOException e) {

          }

          @Override
          public void onResponse(Call call, Response response) throws IOException {

          }
        });

  }
}
