package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.BaseResult;
import java.io.IOException;
import java.util.HashMap;
import okhttp3.Call;
import okhttp3.Response;

/**
 * Description : 获取sessionId 接口
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/30/16:51.
 */
public class CustomController {

  private Context mContext = BaseApp.getContext();
  private String BASE_URL = Hawk.get(CustomParam.Base_URL);

  private String BIME_URL = Hawk.get(CustomParam.Bim_URL);

  private String WEB_URL = Hawk.get(CustomParam.Web_URL);
  /**
   * 获取sessionId
   * 接口
   */
  public void GetSeesionId(HashMap<String, String> params, final CallBack<String> callBack) {

    OkHttpHelper.getInstance(mContext).postFormData(BIME_URL + ApiConstant.GET_SESSIONID, params,
        new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) throws IOException {
            callBack.onSuccess(str);
            Logger.t("测试参数拼接---------------->").e(params.toString());
          }

          @Override
          public void onFailure(Call call, IOException e) {
            callBack.onFail(e.toString());

          }

          @Override
          public void onResponse(Call call, Response response) throws IOException {

          }
        });

  }

  /**
   * 修改密码
   */
  public void ModifyPassWord(HashMap<String, String> params, final CallBack<BaseResult> callBack) {
    OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.MODIFY_PWD, params,
        new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) throws IOException {
            Logger.t("修改密码-->").e(str);
            if (TextUtils.isEmpty(str)) {
              return;
            }
            callBack.onSuccess(new Gson().fromJson(str, BaseResult.class));
          }

          @Override
          public void onFailure(Call call, IOException e) {

          }

          @Override
          public void onResponse(Call call, Response response) throws IOException {

          }
        });
  }


}
