package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.model.DrawingsModel;
import com.probim.bimenew.model.ModelPhaseDto;
import com.probim.bimenew.model.ModelPointViewDetails;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.model.ModelStages;
import com.probim.bimenew.model.ModelTwoDimensModel;
import com.probim.bimenew.model.ModelViewModel;
import com.probim.bimenew.model.ModelViewPointModel;
import com.probim.bimenew.model.ShareModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;

import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :模型控制器 Author : Gary Email  : <EMAIL> Date   :
 * 2018/7/19/13:52.
 */
public class ModelController {

    private Context mContext = BaseApp.getContext();
    private String BASE_URL = Hawk.get(CustomParam.Base_URL);

    private String BIME_URL = Hawk.get(CustomParam.Bim_URL);

    private String WEB_URL = Hawk.get(CustomParam.Web_URL);

    /**
     * 获取阶段分类列表
     */
    public void GetModelStages(HashMap<String, String> params, final CallBack<ModelStages> callBack) {

        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BASE_URL + ApiConstant.GetModelPhase, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                Logger.t("获取所有分类---->").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, ModelStages.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {
                                callBack.onFail(e.toString());
                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 获取阶段下列表
     */
    public void GetStageList(HashMap<String, String> params,
                             final CallBack<List<ModelSatgeListModel>> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.GetFeaturesByPhase, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
//                                Logger.t("模型列表--->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Type collectionType = new TypeToken<List<ModelSatgeListModel>>() {
                                }.getType();
                                List<ModelSatgeListModel> lcs = (List<ModelSatgeListModel>) new Gson()
                                        .fromJson(str, collectionType);
                                callBack.onSuccess(lcs);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 获取所有模型数据
     */
    public void getAllList(HashMap<String, String> params,
                             final CallBack<List<ModelSatgeListModel>> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.GetAllFeatures, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
//                                Logger.t("模型列表--->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Type collectionType = new TypeToken<List<ModelSatgeListModel>>() {
                                }.getType();
                                List<ModelSatgeListModel> lcs = (List<ModelSatgeListModel>) new Gson()
                                        .fromJson(str, collectionType);
                                callBack.onSuccess(lcs);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 模型视点列表
     */

    public void GetViewPoint(HashMap<String, String> params,
                             final CallBack<List<ModelViewPointModel>> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.VIEWPOINT_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                if (TextUtils.isEmpty(str)) {
                                    callBack.onFail("暂无数据");
                                    return;
                                }

                                Type collectionType = new TypeToken<List<ModelViewPointModel>>() {
                                }.getType();

                                List<ModelViewPointModel> lcs = (List<ModelViewPointModel>) new Gson()
                                        .fromJson(str, collectionType);
                                callBack.onSuccess(lcs);
                                Logger.t("视点列表----->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 模型视图列表
     */

    public void GetView(HashMap<String, String> params,
                        final CallBack<List<ModelViewModel>> callBack) {

        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.VIEW_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                if (TextUtils.isEmpty(str)) {
                                    callBack.onFail("暂无数据");
                                    return;
                                }
                                Type collectionType = new TypeToken<List<ModelViewModel>>() {
                                }.getType();

                                List<ModelViewModel> lcs = (List<ModelViewModel>) new Gson()
                                        .fromJson(str, collectionType);
                                callBack.onSuccess(lcs);

                                //            Logger.t("视图列表----->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 模型二维视图列表
     */
    public void GetTwoDimensional(HashMap<String, String> params,
                                  final CallBack<ModelTwoDimensModel> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.TWODIMENVIEW_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                if (TextUtils.isEmpty(str)) {
                                    callBack.onFail("暂无数据");
                                    return;
                                }
            /*Type collectionType = new TypeToken<List<ModelTwoDimensModel>>() {
            }.getType();

            List<ModelTwoDimensModel> lcs = (List<ModelTwoDimensModel>) new Gson()
                .fromJson(str, collectionType);*/
                                callBack.onSuccess(new Gson().fromJson(str, ModelTwoDimensModel.class));

                                //            Logger.t("二维视图列表----->").e(str);

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 获取完整的视点模型
     */
    public void GetViewPointDetails(HashMap<String, String> params,
                                    final CallBack<ModelPointViewDetails> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.VIEWPOINT_DETAILS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                if (TextUtils.isEmpty(str) || "\\".equals(str)) {
                                    return;
                                }
                                //Logger.t("视点模型-------》").e(str);
                                //去除json多余"/"和引号 replace("\"", "").trim();
                                String datas = str.replace("\\", "").trim();
                                String endStr = datas.replaceAll("\"\\{", "{").replaceAll("\\}\"", "}");
                                callBack.onSuccess(new Gson().fromJson(endStr, ModelPointViewDetails.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 模型二维视图详情
     */
    public void GetTwoDimensionalDetails(HashMap<String, String> params,
                                         final CallBack<DrawingsModel> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.TWODIMENVIEW_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }

                                callBack.onSuccess(new Gson().fromJson(str, DrawingsModel.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 设置默认视图
     */
    public void SetDefaultView(HashMap<String, String> params, final CallBack<String> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.SETDEFAULTVIEW, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                callBack.onSuccess(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 设置默认视点
     */
    public void SetDefaultPointView(HashMap<String, String> params, final CallBack<String> callBack) {
        OkHttpHelper.getInstance(mContext).getAsync(BIME_URL + ApiConstant.SETDEFAULTPOINTVIEW, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {

                        Logger.t("设置默认视点=======>").e(str);
                        callBack.onSuccess(str);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取完整的批注模型
     */
    public void GetNtationDetails(HashMap<String, String> params,
                                  final CallBack<String> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.VIEWPOINT_DETAILS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }

                                //去除json多余"/"和引号 replace("\"", "").trim();
                                String datas = str.replace("\\", "").trim();
                                String endStr = datas.replaceAll("\"\\{", "{").replaceAll("\\}\"", "}");
                                callBack.onSuccess(endStr);

                                //                                Logger.t("完整批注----->").e(endStr);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 删除视点或者批注
     */
    public void DeleteViewPointOrNtation(HashMap<String, String> params,
                                         final CallBack<String> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BIME_URL + ApiConstant.DELETE_VIEWPOINT_NTATION, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(str);
                                //            Logger.t("删除-------------------->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取模型分享接口
     */
   /* Restoration:
    hasRandomPwd: 是否加密。 比如分享到微信 QQ 就不需要加密 就传0  如果时复制私密链接 即是加密  传 1

    Restoration:
    daycount:是分享的有效期。上边的选择  选 1天传 1  ，选 7天 传 7 ，选永久有效 传 -1*/
    public void GetModelShare(HashMap<String, String> params, final CallBack<ShareModel> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.GET_MODEL_SHARE, params, new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {

                        Logger.t("模型分享").e(str);
                        if (TextUtils.isEmpty(str)) return;
                        callBack.onSuccess(new Gson().fromJson(str, ShareModel.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {
                        callBack.onFail(e.toString());
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }
}
