package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.AllPeoplerResult;
import com.probim.bimenew.result.RoleUsersDto;
import com.probim.bimenew.result.UsersDto;
import com.probim.bimenew.result.UsersGroupDto;

import java.io.IOException;
import java.util.HashMap;

import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :人员接口
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/11/26/14:35.
 */

public class UserController {

    private final Context mContext = BaseApp.getContext();

    private final String BASE_URL = Hawk.get(CustomParam.Base_URL);

    private final String BIME_URL = Hawk.get(CustomParam.Bim_URL);

    private final String WEB_URL = Hawk.get(CustomParam.Web_URL);


    /**
     * 字母排序
     */
    public void getUsersAlphabetical(HashMap<String, String> params,
                                     final CallBack<AllPeoplerResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.ALLUSER, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("所有人").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, AllPeoplerResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 分类
     */
    public void getUsersGroup(HashMap<String, String> params,
                              final CallBack<UsersGroupDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.USERGROUP, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("所有人分组").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, UsersGroupDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 分类下人员
     */
    public void getUsersByGroupID(HashMap<String, String> params,
                                  final CallBack<UsersDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.USERBYGROUPID, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("查找人员").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, UsersDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 所有人员
     */
    public void getUsers(HashMap<String, String> params,
                         final CallBack<UsersDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GETPROJECTROLEUSER, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("项目所有人").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, UsersDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }


    /**
     * 所有人员
     */
    public void getUser(HashMap<String, String> params,
                        final CallBack<UsersDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.fffff, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("项目所有人").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, UsersDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 所有人员
     */
    public void getRoleUsers(HashMap<String, String> params,
                             final CallBack<RoleUsersDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.fffff, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("项目所有人").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, RoleUsersDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }


}
