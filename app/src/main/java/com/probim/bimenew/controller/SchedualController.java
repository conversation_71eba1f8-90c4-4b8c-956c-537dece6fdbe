package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.activity.schedule.dto.AddTaskResultDto;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualMaterialDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualMaterialDetailsDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualMecDetailsDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualMecDto;
import com.probim.bimenew.activity.schedule.dto.SchedualPeopleDetailsDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualPeopleListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualSafeDetailsDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualStateListDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualSuperviseDetailsDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualTaskAddDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualTaskDetailsDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualUploadResult;
import com.probim.bimenew.activity.schedule.dto.UnitDto;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.yyx.beautifylib.utils.ToastUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

import okhttp3.Call;
import okhttp3.Response;

public class SchedualController {
    private final Context mContext = BaseApp.getContext();

    private final String BASE_URL = Hawk.get(CustomParam.Base_URL);

    private final String SCHEDUAL_URL = Hawk.get(CustomParam.Schedual_URL);


    /**
     * 获取进度计划
     */
    public void getSchedualList(HashMap<String, String> params,
                                final CallBack<SchedualListDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(SCHEDUAL_URL + ApiConstant.GET_SCHEDUAL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取进度计划---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualListDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取进度计划
     */
    public void getUnitText(HashMap<String, String> params,
                            final CallBack<UnitDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_UNIT_TEXT,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取进度单位---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, UnitDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取人员分类
     *
     * @param params
     * @param callBack
     */
    public void getSchedualPeopleList(HashMap<String, String> params,
                                      final CallBack<SchedualPeopleListDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_PEOPLE,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取人员分类---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualPeopleListDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 人员新增接口
     *
     * @param params
     * @param callBack
     */
    public void addSchedualPeople(HashMap<String, String> params,
                                  final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(BASE_URL + ApiConstant.ADD_SCHEDUAL_PEOPLE,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("新增人员---------->>>").e(str);
                                    Logger.t("新增人员---------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取机械
     *
     * @param params
     * @param callBack
     */
    public void getSchedualMecList(HashMap<String, String> params,
                                   final CallBack<SchedualMecDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_MEC,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取机械分类---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualMecDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 督导会新增接口
     *
     * @param params
     * @param callBack
     */
    public void addSchedualSupervise(HashMap<String, String> params,
                                     final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(BASE_URL + ApiConstant.ADD_SCHEDUAL_SUPERVISE,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("督导会新增接口---------->>>").e(str);
                                    Logger.t("督导会新增接口---------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }


    /**
     * 机械新增接口
     *
     * @param params
     * @param callBack
     */
    public void addSchedualMec(HashMap<String, String> params,
                               final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(BASE_URL + ApiConstant.ADD_SCHEDUAL_MEC,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("新增机械---------->>>").e(str);
                                    Logger.t("新增机械---------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取材料
     *
     * @param params
     * @param callBack
     */
    public void getSchedualMateriaTypelList(HashMap<String, String> params,
                                            final CallBack<SchedualMaterialDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_MATERIAL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取材料分类---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualMaterialDTO.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 材料新增接口
     *
     * @param params
     * @param callBack
     */
    public void addSchedualMaterial(HashMap<String, String> params,
                                    final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(BASE_URL + ApiConstant.ADD_SCHEDUAL_MATERIAL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("新增材料---------->>>").e(str);
                                    Logger.t("新增材料---------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void uploadSchedualPhoto(List<File> fileList, HashMap<String, String> params,
                                    final CallBack<SchedualUploadResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadPhoto(BASE_URL + ApiConstant.UPLOAD_SCHEDUA_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {

                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("进度任务上传照片返回数据").e(str);
                                    Logger.t("进度任务上传照片参数").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualUploadResult.class));
                                } else {
                                }
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {
                                e.printStackTrace();
                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 任务新增接口
     *
     * @param params
     * @param callBack
     */
    public void addSchedualTask(HashMap<String, String> params,
                                final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(BASE_URL + ApiConstant.ADD_SCHEDUAL_TASK,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("新增任务---------->>>").e(str);
                                    Logger.t("新增任务---------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取进度计划
     */
    public void getAllSchedualList(HashMap<String, String> params,
                                   final CallBack<SchedualPreviewListDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_LIST,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取全部列表---------->>>").e(params.toString());
                                    Logger.t("获取全部列表---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualPreviewListDTO.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取进度计划
     */
    public void getAllSchedualStateList(HashMap<String, String> params,
                                        final CallBack<SchedualStateListDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_STATE_LIST,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取全部状态列表---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualStateListDTO.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取人员详情
     */
    public void getPeopleDetaisl(HashMap<String, String> params,
                                 final CallBack<SchedualPeopleDetailsDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_USER,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取人员---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualPeopleDetailsDTO.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取机械详情
     */
    public void getMecDetaisl(HashMap<String, String> params,
                              final CallBack<SchedualMecDetailsDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_MEC,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取机械详情--------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualMecDetailsDTO.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取机械详情
     */
    public void getMaterialDetaisl(HashMap<String, String> params,
                                   final CallBack<SchedualMaterialDetailsDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_MATERIAL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取材料详情--------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualMaterialDetailsDTO.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取督导会详情
     */
    public void getSuperviseDetials(HashMap<String, String> params,
                                    final CallBack<SchedualSuperviseDetailsDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_SUPERVISE,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取督导会详情--------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualSuperviseDetailsDTO.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取督导会详情
     */
    public void getTaskDetials(HashMap<String, String> params,
                               final CallBack<SchedualTaskDetailsDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_TASK,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取任务详情--------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualTaskDetailsDTO.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除图片接口
     */
    public void deletePhoto(HashMap<String, String> params,
                            final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.DELETE_SCHEDUAL_PHOTO,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("删除图片--------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除item
     */
    public void deleteScedual(HashMap<String, String> params,
                              final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postWithUrlParams(BASE_URL + ApiConstant.DELETE_SCHEDUAL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("删除填报--------->>>").e(str);
                                    Logger.t("删除填报--------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除item
     */
    public void updateState(HashMap<String, String> params,
                            final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.UPDATE_SCHEDUAL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("发布接口--------->>>").e(str);
                                    Logger.t("发布接口--------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 质量新增接口
     *
     * @param params
     * @param callBack
     */
    public void addSchedualSafe(HashMap<String, String> params,
                                final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(BASE_URL + ApiConstant.ADD_SCHEDUAL_SAFE,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("质量安全新增接口---------->>>").e(str);
                                    Logger.t("质量安全新增接口---------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * -质量安全显示接口
     */
    public void getSafeDetials(HashMap<String, String> params,
                               final CallBack<SchedualSafeDetailsDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_SCHEDUAL_SAFE,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("-质量安全显示接口--------->>>").e(str);
                                    Logger.t("-质量安全显示接口--------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualSafeDetailsDTO.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }


    /**
     * 任务进度累计接口
     */
    public void getTaskAdd(HashMap<String, String> params,
                           final CallBack<SchedualTaskAddDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(SCHEDUAL_URL + ApiConstant.GET_SCHEDUAL_TASK_ADD,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("任务进度累计接口--------->>>").e(str);
                                    Logger.t("任务进度累计接口--------->>>").e(params.toString());
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualTaskAddDTO.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取进度计划
     */
    public void addTask(String params,
                        final CallBack<AddTaskResultDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.ADD_TASk,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取进度计划---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, AddTaskResultDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取进度计划
     */
    public void setUnit(String params,
                        final CallBack<AddTaskResultDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.SetUnitText,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("设置单位---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, AddTaskResultDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 编辑进度
     */
    public void editTask(String params,
                        final CallBack<AddTaskResultDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.EDIT_TASk,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("编辑填报---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, AddTaskResultDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 编辑进度
     */
    public void uploadTask(String params,
                         final CallBack<AddTaskResultDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.UPLOAD_TASk,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("发布提交填报---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, AddTaskResultDto.class));
                                } else {
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }
}
