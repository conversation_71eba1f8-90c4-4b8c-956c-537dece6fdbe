package com.probim.bimenew.controller;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.model.ProblemStatusModel;
import com.probim.bimenew.model.UpdateDto;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;

import java.io.IOException;
import java.util.HashMap;

import okhttp3.Call;
import okhttp3.Response;

public class UpdateController {
    private String BASE_URL = Hawk.get(CustomParam.Base_URL);

    public void getAppVersions(HashMap<String, String> params,
                                 final CallBack<UpdateDto> callBack) {

        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BASE_URL + ApiConstant.GetAppVersions, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("获取更新记录------------------->").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, UpdateDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

}
