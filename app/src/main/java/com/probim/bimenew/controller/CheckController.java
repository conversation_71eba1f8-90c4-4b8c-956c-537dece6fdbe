package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.BaseDto;
import com.probim.bimenew.model.CheckDetail;
import com.probim.bimenew.model.CheckDetailsDrawsModel;
import com.probim.bimenew.model.CheckPhoto;
import com.probim.bimenew.model.CheckRecord;
import com.probim.bimenew.model.CheckTwoNameModel;
import com.probim.bimenew.model.DocRelevance;
import com.probim.bimenew.model.NewCheckConfig;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.AllMaterialListBean;
import com.probim.bimenew.result.AllPeoplerResult;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.result.AllTaskResult;
import com.probim.bimenew.result.AllUsersDto;
import com.probim.bimenew.result.BaseResponseResult;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.CheckDetailResult;
import com.probim.bimenew.result.CheckDetailsDto;
import com.probim.bimenew.result.CheckListBean;
import com.probim.bimenew.result.CheckListResult;
import com.probim.bimenew.result.CheckResult;
import com.probim.bimenew.result.CheckTypeResult;
import com.probim.bimenew.result.CheckUploadPhotoResult;
import com.probim.bimenew.result.DeleteCheckPhotoResult;
import com.probim.bimenew.result.DeleteCheckResult;
import com.probim.bimenew.result.IssueJoinerResult;
import com.probim.bimenew.result.IssueUploadPhotoResult;
import com.probim.bimenew.result.MateriaListBean;
import com.probim.bimenew.result.NewCheckResult;
import com.probim.bimenew.result.ProjectStrcutreResult;
import com.probim.bimenew.result.UpdateCheckPhotoResult;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;

import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :检查接口
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/11/26/14:35.
 */

public class CheckController {

    private final Context mContext = BaseApp.getContext();

    private final String BASE_URL = Hawk.get(CustomParam.Base_URL);

    private final String BIME_URL = Hawk.get(CustomParam.Bim_URL);

    private final String SCHEDUAL_URL = Hawk.get(CustomParam.Schedual_URL);
    /**
     * 获取质量安全列表
     */
    public void getCheckList2(HashMap<String, String> params,
                              final CallBack<CheckListResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.GET_EXAMINE_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("质量检查列表------------->").e(str);
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, CheckListResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取质量安全列表
     */
    public void getCheckList(HashMap<String, String> params, final CallBack<CheckListBean> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.CHECK_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("质量检查列表------------->").e(str);
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, CheckListBean.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取质量安全列表
     */
    public void getCheckListBack(HashMap<String, String> params, final CallBack<CheckListBean> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.GET_EXAMINE_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("质量检查列表------------->").e(str);
                                Logger.t("质量检查列表------------->").e(params.toString());
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, CheckListBean.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取检查详情
     */
    public void getCheckDetail2(HashMap<String, String> params,
                                final CallBack<CheckDetail> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_EXAMINE_DETAIL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, CheckDetail.class));
                                // Logger.t("检查详情----------").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取检查详情
     */
    public void getCheckDetail3(HashMap<String, String> params,
                                final CallBack<CheckDetailResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_EXAMINE_DETAIL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, CheckDetailResult.class));
                                Logger.t("检查详情----------").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除列表
     */
    public void deleteCheckItem(String params, final CallBack<DeleteCheckResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.DELETE_CHECK,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("删除检查结果----------").e(str);
                                Logger.t("删除检查结果----------").e(params.toString());
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, DeleteCheckResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }


    /**
     * 删除列表
     */
    public void closeCheckItem(HashMap<String, String> params, final CallBack<BaseResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.CLOSE_CHECK,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("归档检查结果----------").e(str);
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, BaseResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取检查详情图片
     */
    public void getCheckPhoto(HashMap<String, String> params, final CallBack<CheckPhoto> callBack) {
        OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.GET_CHECK_PHOTO, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (TextUtils.isEmpty(str)) return;
                        callBack.onSuccess(new Gson().fromJson(str, CheckPhoto.class));
                        Logger.t("什么结果----------").e(str);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取关联文档
     */
    public void getRelevance(HashMap<String, String> params,
                             final CallBack<List<DocRelevance>> callBack) {

        OkHttpHelper.getInstance(mContext).postFormData(BIME_URL + ApiConstant.GETDOCBYID, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {

                        //Logger.t("检查列表照片和音频 ").e(str);
                        //Logger.t("检查列表参数").e(params.toString());

                        if (TextUtils.isEmpty(str)) {
                            return;
                        }

                        Type collectionType = new TypeToken<List<DocRelevance>>() {
                        }.getType();
                        List<DocRelevance> lcs = new Gson()
                                .fromJson(str, collectionType);

                        callBack.onSuccess(lcs);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 删除附件
     * parmas : ExamineId    ExamineAttachmentIds 所有id拼接
     */
    public void deleteRelevance(HashMap<String, String> params, final CallBack<BaseDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.DELETE_CHECK_PHOTO, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                Logger.t("删除关联照片").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取整改信息记录列表
     * parmas : ExamineId
     */

    public void getCheckRecord(String json, final CallBack<CheckRecord> callBack) {
        OkHttpHelper.getInstance(mContext).postJsonString(BASE_URL + ApiConstant.GET_RECORD_LIST, json,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (TextUtils.isEmpty(str)) return;
                        callBack.onSuccess(new Gson().fromJson(str, CheckRecord.class));
                        Logger.t("获取整改信息记录列表").e(str);
                        Logger.t("参数").e(json);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取新建检查配置
     */
    public void getNewCheckConfig(HashMap<String, String> params,
                                  final CallBack<NewCheckConfig> callBack) {
        OkHttpHelper.getInstance(mContext).getAsync(BIME_URL + ApiConstant.GET_NEW_CHECK_CONFIG, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        Logger.t("获取新建检查数据----------------->").e(str);
                        if (TextUtils.isEmpty(str)) return;
                        callBack.onSuccess(new Gson().fromJson(str, NewCheckConfig.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 修改检查
     * <p>
     * {
     * ExamineId = "34D291C0-6716-4569-ADD3-BEC2EFF9C27C";//修改任何信息都需要传
     * ConstructUnit = "\U5ba4\U5916\U5efa\U7b51\U73af\U5883";
     * ConstructUnitID = Q1521598096430;
     * Axis = "";
     * AxisID = "";
     * FlowPhase = "";
     * FlowPhaseID = "";
     * SubConstructUnit = "";
     * SubConstructUnitID = "";
     * Subdivisional = "";
     * SubdivisionalID = "";
     * VerifyBatch = "";
     * VerifyBatchID = "";
     * }
     */
    public void modifyCheck(String json, final CallBack<BaseDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.MODIFY_CHECK,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("修改检查参数---------------------->").e(json);
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新建检查
     */
    public void newCheck(HashMap<String, String> json, final CallBack<NewCheckResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.CREATE_NEW_CHECK,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("新建检查返回数据------------>").e(str);
                                Logger.t("新建检查参数------------>").e(json.toString());
                                callBack.onSuccess(new Gson().fromJson(str, NewCheckResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新建检查
     */
    public void newCheck2(String json, final CallBack<BaseDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.CREATE_NEW_CHECK,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("新建检查返回数据------------>").e(str);
                                Logger.t("新建检查参数------------>").e(json);
                                callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新建复检
     */
    public void reviewCheck(String json, final CallBack<BaseDto> callBack) {
        OkHttpHelper.getInstance(mContext).postJsonString(BASE_URL + ApiConstant.REVIEW_CHECK,
                json, new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        Logger.t("复检返回数据------------>").e(str);
                        Logger.t("复检参数------------>").e(json);
                        callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 上传照片到服务器
     */
    public void uploadCheckPhoto(List<File> fileList, HashMap<String, String> params,
                                 final CallBack<CheckUploadPhotoResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadPhoto(BASE_URL + ApiConstant.UPLOAD_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("新建质检数据 上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, CheckUploadPhotoResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void UploadPhoto(List<File> fileList, HashMap<String, String> params,
                            final CallBack<String> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadPhoto(BASE_URL + ApiConstant.UPLOAD_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                //Logger.t("新建检查上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void UploadAudio(List<File> fileList, HashMap<String, String> params,
                            final CallBack<String> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadAudio(BIME_URL + ApiConstant.UPLOAD_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                //Logger.t("新建检查录音返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取图纸信息
     * <p>
     * params :    ExamineID
     */
    public void getDrawPos(String json, final CallBack<CheckDetailsDrawsModel> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.CHECK_DETAILS_DRAWPOS_LIST, json,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                //Logger.t("图纸详情").e(str);
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, CheckDetailsDrawsModel.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新增图纸
     * params :
     * <p>
     * 参数NSDictionary *dict = @{
     *
     * @"GID":[NSString getUniqueStrByUUID],
     * @"FileId":examineAddModel.drawingposModel.FileId,
     * @"PosX":examineAddModel.drawingposModel.PosX,
     * @"PosY":examineAddModel.drawingposModel.PosY,
     * @"OpId":examineAddModel.drawingposModel.OpId,
     * @"OpUser":examineAddModel.drawingposModel.OpUser,
     * @"CreatDate":examineAddModel.drawingposModel.CreateDate,
     * @"X":examineAddModel.drawingposModel.X,
     * @"Y":examineAddModel.drawingposModel.Y,
     * @"Z":examineAddModel.drawingposModel.Z,
     * @"Pixel2MX":examineAddModel.drawingposModel.Pixel2MX,
     * @"Pixel2MY":examineAddModel.drawingposModel.Pixel2MY,
     * @"Width":examineAddModel.drawingposModel.Width,
     * @"Height":examineAddModel.drawingposModel.Height,
     * @"isDel":examineAddModel.drawingposModel.IsDel,
     * @"ExamineId":self.examineListModel.ExamineID, };
     */
    public void newDrawpos(String json, final CallBack<BaseDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.CHECK_DETAILS_DRAWPOS_CREATE,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                                Logger.t("新建图纸选点---------------->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除图纸
     * <p>
     * params :    ExamineID
     */
    public void deleteDrawpos(String json, final CallBack<BaseDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.CHECK_DETAILS_DRAWPOS_DELETE,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                                Logger.t("删除图纸----------------").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传完图片 绑定图片
     */

    public void attachPhoto(String json, final CallBack<BaseDto> callBack) {
        OkHttpHelper.getInstance(mContext).postJsonString(BASE_URL + ApiConstant.CHECK_ATTACH_PHOTO,
                json, new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (TextUtils.isEmpty(str)) return;
                        callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                        Logger.t("修改检查关联照片").e(json);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取model  name
     */

    public void getModelName(HashMap<String, String> params, final CallBack<String> callBack) {
        OkHttpHelper.getInstance(mContext).getAsync(BIME_URL + ApiConstant.GET_MODEL, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        //Logger.t("获取到model name").e(str);
                        callBack.onSuccess(str);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 模型二维视图列表
     */
    public void GetTwoDimensional(HashMap<String, String> params,
                                  final CallBack<CheckTwoNameModel> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BIME_URL + ApiConstant.TWODIMENVIEW_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                if (TextUtils.isEmpty(str)) {
                                    callBack.onFail("暂无数据");
                                    return;
                                }
            /*Type collectionType = new TypeToken<List<ModelTwoDimensModel>>() {
            }.getType();

            List<ModelTwoDimensModel> lcs = (List<ModelTwoDimensModel>) new Gson()
                .fromJson(str, collectionType);*/
                                callBack.onSuccess(new Gson().fromJson(str, CheckTwoNameModel.class));

                                //Logger.t("二维视图列表----->").e(str);

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 获取工程结构
     */
    public void getProjectStrcutre(HashMap<String, String> params,
                                   final CallBack<ProjectStrcutreResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_CHECK_PROJECT_STRUCTER,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                callBack.onSuccess(new Gson().fromJson(str, ProjectStrcutreResult.class));
                                Logger.t("获取工程结构----------").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取工程结构
     */
    public void getProjectStrcutreItem(HashMap<String, String> params,
                                       final CallBack<ProjectStrcutreResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_CHECK_PROJECT_STRUCTER_ITEM,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                Logger.t("获取工程结构分级----------").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, ProjectStrcutreResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取检查分类
     */
    public void getCheckType(HashMap<String, String> params,
                             final CallBack<CheckTypeResult> callBack) {

        OkHttpHelper.getInstance(mContext).getAsync(BASE_URL + ApiConstant.GET_CHECK_TYPE,
                params, new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (TextUtils.isEmpty(str)) return;
                        Logger.t("获取检查分类----------").e(str);
                        callBack.onSuccess(new Gson().fromJson(str, CheckTypeResult.class));

                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取项目所有人员
     */
    public void getCheckJoiner(HashMap<String, String> params,
                               final CallBack<IssueJoinerResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GETPROJECTROLEUSER, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("项目所有人").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, IssueJoinerResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取项目所有人员
     */
    public void getCheckPeople(HashMap<String, String> params,
                               final CallBack<AllUsersDto> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GetUserPaged, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("项目所有人").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, AllUsersDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 修改检查
     */
    public void modifyCheckItem(HashMap<String, String> json, final CallBack<BaseResponseResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.MODIFY_CHECK_ITEM,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("修改检查返回数据------------>").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, BaseResponseResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除问题照片
     */
    public void deleteCheckPhoto(HashMap<String, String> params,
                                 final CallBack<DeleteCheckPhotoResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormBody(BASE_URL + ApiConstant.CHECK_REMOVE_PHOTO, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("删除检查照片").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, DeleteCheckPhotoResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void updateCheckPhoto(List<File> fileList, HashMap<String, String> params,
                                 final CallBack<UpdateCheckPhotoResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadPhoto(BASE_URL + ApiConstant.CHECK_UPDATE_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("检查上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, UpdateCheckPhotoResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取构件列表
     */
    public void getMateriaList2(HashMap<String, String> params,
                                final CallBack<MateriaListBean> callBack) {
        OkHttpHelper.getInstance(mContext).getAsync(BASE_URL + ApiConstant.GETMATERIALISTITEM, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        Logger.t("构件文件夹下列表----------").e(str);
                        callBack.onSuccess(new Gson().fromJson(str, MateriaListBean.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取构件列表
     */
    public void getMateriaList(HashMap<String, String> params,
                               final CallBack<AllMaterialListBean> callBack) {
        OkHttpHelper.getInstance(mContext).getAsync(BASE_URL + ApiConstant.GETMATERIALIST, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (TextUtils.isEmpty(str)) return;
                        Logger.t("构件文件夹列表----------").e(str);
                        callBack.onSuccess(new Gson().fromJson(str, AllMaterialListBean.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 申请验收操作
     */

    public void applyCheck(List<File> fileList, HashMap<String, String> params,
                           CallBack<CheckResult> callBack) {

        OkHttpHelper.getInstance(mContext).uploadIssuePhoto(BASE_URL + ApiConstant.RECHECK, fileList, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {

                        Logger.t(" 整改 复检 _____________").e(str);
                        callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 验收操作
     */

    public void acceptCheck(List<File> fileList, HashMap<String, String> params,
                            CallBack<CheckResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadIssuePhoto(BASE_URL + ApiConstant.APPLY_CHECK, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {

                                Logger.t("验收_____________").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 检查操作
     */

    public void check(List<File> fileList, HashMap<String, String> params,
                      CallBack<CheckResult> callBack) {

        OkHttpHelper.getInstance(mContext).uploadIssuePhoto(BASE_URL + ApiConstant.CHECK, fileList, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {

                        Logger.t("检查操作_____________").e(str);
                        callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取检查详情
     */
    public void getCheckDetail(HashMap<String, String> params,
                               final CallBack<CheckDetailsDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.CHECK_ITEM,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (TextUtils.isEmpty(str)) return;
                                Logger.t("检查详情----------").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, CheckDetailsDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 修改检查数据  修改整改人或验收人
     */
    public void modifyCheckPeople(HashMap<String, String> params, CallBack<CheckResult> callBack) {
        OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.ModifyCheckPeople,
                params, new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (!TextUtils.isEmpty(str)) {
                            Logger.t("修改人数据").e(params.toString());
                            callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                        }
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 修改检查数据  严重等级
     */
    public void modifyCheckSe(HashMap<String, String> params, CallBack<CheckResult> callBack) {
        OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.ModifyCheckSe,
                params, new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (!TextUtils.isEmpty(str)) {
                            callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                        }
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 新增检查任务
     */
    public void addMission(HashMap<String, String> params, CallBack<CheckResult> callBack) {

        OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.NEWCHECK, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (!TextUtils.isEmpty(str)) {
                            Logger.t("addMision").e(params.toString());
                            Logger.t("addMision").e(str);
                            callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                        }
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 任务列表
     */
    public void getCheckTask(HashMap<String, String> json, final CallBack<AllTaskResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(SCHEDUAL_URL + ApiConstant.CHECK_TASK_LIST,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("获取任务列表------------>").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, AllTaskResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 任务列表
     */
    public void getCheckTaskItem(HashMap<String, String> json, final CallBack<AllTaskItemResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(SCHEDUAL_URL + ApiConstant.CHECK_TASK_ITEM_LIST,
                        json, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                Logger.t("获取子任务列表------------>").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, AllTaskItemResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新增检查任务
     */
    public void modifyMissionInfo(HashMap<String, String> params, CallBack<CheckResult> callBack) {

        OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.ModifyMissionInfo, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (!TextUtils.isEmpty(str)) {
                            Logger.t("ModifyMissionInfo").e(params.toString());
                            Logger.t("ModifyMissionInfo").e(str);
                            callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                        }
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 新增检查任务
     */
    public void modifyMissionCheckUser(HashMap<String, String> params, CallBack<CheckResult> callBack) {

        OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.ModifyMissionCheckUser, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        if (!TextUtils.isEmpty(str)) {
                            Logger.t("ModifyMissionCheckUser").e(params.toString());
                            Logger.t("ModifyMissionCheckUser").e(str);
                            callBack.onSuccess(new Gson().fromJson(str, CheckResult.class));
                        }
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 上传照片到服务器
     */
    public void uploadPhoto(List<File> fileList, HashMap<String, String> params,
                            final CallBack<IssueUploadPhotoResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadIssuePhoto(BASE_URL + ApiConstant.UPLOAD_CHECK_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, IssueUploadPhotoResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

}
