package com.probim.bimenew.controller;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.AuthorityDto;
import com.probim.bimenew.dto.ProjectsDto;
import com.probim.bimenew.model.Authority;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.AuthorizeResult;
import com.probim.bimenew.result.ProjectIDResult;
import com.probim.bimenew.result.ProjectListResult;
import com.probim.bimenew.utils.GsonSingleton;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;

import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :项目列表控制器 Author : Gary Email : <EMAIL> Date : 2018/7/17/13:18.
 */
public class ProjectController {

    private final String BASE_URL = Hawk.get(CustomParam.Base_URL);

    private final String BIME_URL = Hawk.get(CustomParam.Bim_URL);

    private final String WEB_URL = Hawk.get(CustomParam.Web_URL);

    /**
     * 项目列表
     */
    public void getProjectList(HashMap<String, String> params,
                               final CallBack<ProjectListResult> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BASE_URL + ApiConstant.PROJECT_LIST, params, null,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                Logger.t("项目列表").e(str);

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, ProjectListResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {
                                callBack.onFail(e.toString());
                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 项目列表
     */
    public void getProjectList2(HashMap<String, String> params,
                                final CallBack<ProjectsDto> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BASE_URL + ApiConstant.PROJECT_LIST2, params, null,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) {
                                //Logger.t("项目列表").e(str);

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                //callBack.onSuccess(new Gson().fromJson(str, ProjectsDto.class));
                                callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, ProjectsDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {
                                callBack.onFail(e.toString());
                            }

                            @Override
                            public void onResponse(Call call, Response response) {

                            }
                        });
    }

    /**
     * 获取登录人项目权限
     */

    public void getAuthorize(HashMap<String, String> params,
                             final CallBack<List<Authority>> callBack) {

        OkHttpHelper.getInstance(BaseApp.getContext()).postFormData(WEB_URL + ApiConstant.GETAU, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {
                        //Logger.t("项目权限--------------->").e(str);
                        if (TextUtils.isEmpty(str)) {
                            return;
                        }
                        Type collectionType = new TypeToken<List<Authority>>() {
                        }.getType();

                        List<Authority> lcs = (List<Authority>) new Gson().fromJson(str, collectionType);
                        callBack.onSuccess(lcs);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 获取登录人项目权限
     */

    public void getAuthorize2(HashMap<String, String> params,
                              final CallBack<AuthorizeResult> callBack) {

        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BASE_URL + ApiConstant.GET_USER_ROLE, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("项目权限--------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }

                                callBack.onSuccess(new Gson().fromJson(str, AuthorizeResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 项目的所属机构
     */
    public void getProject(HashMap<String, String> params,
                           final CallBack<ProjectIDResult> callBack) {
        OkHttpHelper.getInstance(BaseApp.getContext())
                .getAsync(BASE_URL + ApiConstant.GET_PROJECT, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
//                                Logger.t("通过项目ID获取机构ID--------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }

                                callBack.onSuccess(new Gson().fromJson(str, ProjectIDResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });

    }

    /**
     * 收藏项目
     */

    public void collectProject(HashMap<String, String> params, final CallBack<ProjectIDResult> callBack) {

        OkHttpHelper.getInstance(BaseApp.getContext()).postJsonAsync(BASE_URL + ApiConstant.COLLECT_PROJECT, params, new IHttpRequestHandler1() {
            @Override
            public void onFailure(Call call, IOException e) {

            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {

            }

            @Override
            public void onResponse(Call call, Response response, String str) throws IOException {
                if (!TextUtils.isEmpty(str)) {

                    callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, ProjectIDResult.class));
                }

                Logger.e(str);
            }
        });


    }

    /**
     * 获取登录人项目权限
     */

    public void getAuthority(HashMap<String, String> params,
                             final CallBack<AuthorityDto> callBack) {

        OkHttpHelper.getInstance(BaseApp.getContext()).getAsync(BASE_URL + ApiConstant.Get_Authority, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {
//                        Logger.t("项目权限--------------->").e(str);

                        callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, AuthorityDto.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }
}
