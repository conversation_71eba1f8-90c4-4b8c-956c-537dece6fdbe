package com.probim.bimenew.controller.weather;


import android.content.Context;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.dto.BaseDto;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import java.io.IOException;
import java.util.HashMap;
import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/6/14/12:09.
 */

public class WeatherModelImpl implements WeatherModel {

  private Context context;

  public WeatherModelImpl(Context context) {
    this.context = context;
  }

  @Override
  public void getWeather(HashMap<String, String> parmas, OnWeatherListener listener) {
/*

    RetrofitHelper.getInstance()
        .getApiRetrofit(ApiServices.class)
        .getAliSign(parmas)
        .compose(BaseApiMoudle.handleResult(AliSignDto.class))
        .subscribe(new Consumer<AliSignDto>() {
          @Override
          public void accept(AliSignDto aliSignBean) throws Exception {

            if (aliSignBean.getCode() == 0) {

              listener.onSuccess(aliSignBean);

            } else {

              listener.onFail(aliSignBean.getMessage());
            }
          }

        }, new Consumer<Throwable>() {
          @Override
          public void accept(Throwable throwable) throws Exception {

          }
        });
*/

    OkHttpHelper.getInstance(context).getAsync(ApiConstant.ISREGISTER, parmas,
        new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) {

            if (TextUtils.isEmpty(str)) {
              return;
            }
            Logger.t("返回来的数据---->").e(str);
            listener.onSuccess(new Gson().fromJson(str, BaseDto.class));

          }

          @Override
          public void onFailure(Call call, IOException e) {

            listener.onFail(e.getMessage());
          }

          @Override
          public void onResponse(Call call, Response response) {

          }
        });


  }
}
