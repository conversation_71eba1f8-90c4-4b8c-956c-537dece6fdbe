package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.model.BaseDocDto;
import com.probim.bimenew.model.BaseModel;
import com.probim.bimenew.model.DocFileDto;
import com.probim.bimenew.model.DocTreeDto;
import com.probim.bimenew.model.DocFirstFloor;
import com.probim.bimenew.model.DocShareResult;
import com.probim.bimenew.model.DocUserPermissions;
import com.probim.bimenew.model.DocVersionDto;
import com.probim.bimenew.model.ProjectRole;
import com.probim.bimenew.model.ShareDwg;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.DocAuResult;
import com.probim.bimenew.utils.GsonSingleton;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :文档控制器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/30/11:26.
 */
public class DocController {

  private Context mContext = BaseApp.getContext();
  private String BASE_URL = Hawk.get(CustomParam.Base_URL);

  private String BIME_URL = Hawk.get(CustomParam.Bim_URL);

  private String Doc_Url = Hawk.get(CustomParam.Doc_URL);

    /**
     * 获取树
     * @param params
     * @param callBack
     */
  public void getDocTree(HashMap<String, String> params, final CallBack<DocTreeDto> callBack){
      OkHttpHelper.getInstance(mContext).getAsync(Doc_Url + ApiConstant.Doc_Tree, params,
              new IHttpRequestHandler1() {
                  @Override
                  public void onResponse(Call call, Response response, String str) throws IOException {
                      Logger.t("文档树结构").e(str);
                      callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, DocTreeDto.class));
                  }

                  @Override
                  public void onFailure(Call call, IOException e) {
                      callBack.onFail(e.toString());
                  }

                  @Override
                  public void onResponse(Call call, Response response) throws IOException {

                  }
              });

  }
    /**
     * 获取文档文件
     * @param params
     * @param callBack
     */
    public void getDocFile(HashMap<String, String> params, final CallBack<DocFileDto> callBack){
        OkHttpHelper.getInstance(mContext).getAsync(Doc_Url + ApiConstant.Doc_File, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {
                        Logger.t("文档数据").e(str);
                        callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, DocFileDto.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {
                        callBack.onFail(e.toString());
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });

    }

    /**
     * 删除文件
     */
    public void deleteFiles(String userId, String json, final CallBack<BaseDocDto> callBack) {
        String requestUrl = Doc_Url + ApiConstant.Delete_Doc + "?userId=" + userId + "&Token=" + Hawk.get(CustomParam.Token);
        OkHttpHelper.getInstance(mContext).postJsonString(requestUrl, json,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {
                        Logger.t("删除文件------------------->").e(str);
                        Logger.t("删除文件------------------->").e(json.toString());
                        if (TextUtils.isEmpty(str)) {
                            return;
                        }
                        callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, BaseDocDto.class));
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

  /**
   * 文档搜索
   */
  public void searchDoc(HashMap<String, String> params,
      final CallBack<DocFileDto> callBack) {
    OkHttpHelper.getInstance(mContext).getAsync(Doc_Url + ApiConstant.Search_Doc, params,
        new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) throws IOException {

            callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str,DocFileDto.class));
          }

          @Override
          public void onFailure(Call call, IOException e) {

          }

          @Override
          public void onResponse(Call call, Response response) throws IOException {

          }
        });
  }

  /**
   * 获取dwg文件预览地址
   */
  public void getDwgUrl(HashMap<String, String> params, final CallBack<ShareDwg> callBack) {
    OkHttpHelper.getInstance(mContext)
        .postFormData(BASE_URL + ApiConstant.GET_DWG_URL, params, new IHttpRequestHandler1() {
          @Override
          public void onResponse(Call call, Response response, String str) throws IOException {
            Logger.t("dwg预览url----->").e(str);
            if (TextUtils.isEmpty(str)) return;
            callBack.onSuccess(new Gson().fromJson(str, ShareDwg.class));
          }

          @Override
          public void onFailure(Call call, IOException e) {

          }

          @Override
          public void onResponse(Call call, Response response) throws IOException {

          }
        });
  }

  /***
   * 获取文件分享内容
   * @param params
   * @param callBack
   */
  public void getDocShare(HashMap<String, String> params, final CallBack<DocShareResult> callBack) {
    OkHttpHelper.getInstance(mContext).postJsonAsync(BASE_URL + ApiConstant.SHARE_DOC, params,
        new IHttpRequestHandler1() {
          @Override public void onFailure(Call call, IOException e) {

          }

          @Override public void onResponse(Call call, Response response) throws IOException {

          }

          @Override public void onResponse(Call call, Response response, String str)
              throws IOException {
            Logger.t("文档分享").e(str);
            if (!TextUtils.isEmpty(str)) {

              callBack.onSuccess(new Gson().fromJson(str, DocShareResult.class));
            }
          }
        });
  }

    /***
     * 获取文件分享内容
     * @param params
     * @param callBack
     */
    public void getDocVersion(HashMap<String, String> params, final CallBack<List<DocVersionDto>> callBack) {
        OkHttpHelper.getInstance(mContext).postFormBody(BaseApp.getBimUrl() + ApiConstant.GetDocumentVersion, params,
                new IHttpRequestHandler1() {
                    @Override public void onFailure(Call call, IOException e) {

                    }

                    @Override public void onResponse(Call call, Response response) throws IOException {

                    }

                    @Override public void onResponse(Call call, Response response, String str)
                            throws IOException {
                        Logger.t("DocVersionDto").e(str);
                        if (!TextUtils.isEmpty(str)) {

                            Type collectionType = new TypeToken<List<DocVersionDto>>() {
                            }.getType();

                            List<DocVersionDto> lcs = (List<DocVersionDto>) new Gson()
                                    .fromJson(str, collectionType);
                            callBack.onSuccess(lcs);
                        }
                    }
                });
    }
}
