package com.probim.bimenew.controller;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.dto.BaseDto;
import com.probim.bimenew.dto.ProblemDetail;
import com.probim.bimenew.model.Comments;
import com.probim.bimenew.model.Comments2;
import com.probim.bimenew.model.DocRelevance;
import com.probim.bimenew.model.ProblemListModel;
import com.probim.bimenew.model.ProblemStatusModel;
import com.probim.bimenew.model.ProjectUser;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.AddNewCommente;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.BaseResultDto;
import com.probim.bimenew.result.DeleteCommentResult;
import com.probim.bimenew.result.DeleteIssuePhotoResult;
import com.probim.bimenew.result.DeleteIssueResult;
import com.probim.bimenew.result.IssueDetailResult;
import com.probim.bimenew.result.IssueJoinerResult;
import com.probim.bimenew.result.IssueTagsResult;
import com.probim.bimenew.result.IssueTypeDto;
import com.probim.bimenew.result.IssueUploadPhotoResult;
import com.probim.bimenew.result.NewIssueResult;
import com.probim.bimenew.result.UpdateIssueDateResult;
import com.probim.bimenew.result.UpdateIssuePeopleResult;
import com.probim.bimenew.result.UpdateIssueTypeResult;
import com.probim.bimenew.result.UploadHideResult;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;

import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :问题控制器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/9/10:54.
 */
public class IssueController {

    private Context mContext = BaseApp.getContext();
    private String BASE_URL = Hawk.get(CustomParam.Base_URL);

    private String BIME_URL = Hawk.get(CustomParam.Bim_URL);

    private String WEB_URL = Hawk.get(CustomParam.Web_URL);

    /**
     * 状态数据请求
     */
    public void getProblemStatus(HashMap<String, String> params,
                                 final CallBack<ProblemStatusModel> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GetIssueStatus, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("创建问题状态数据------------------->").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, ProblemStatusModel.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 分类数据请求
     */
    public void getProblemType(HashMap<String, String> params,
                               final CallBack<IssueTypeDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GetIssueTypes, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("创建问题分类数据------------------->").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, IssueTypeDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 问题列表
     */
    public void GetProblemList(HashMap<String, String> params,
                               final CallBack<ProblemListModel> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.PORBLEM_LIST, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
//                                Logger.t("搜索问题---->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }

                                callBack.onSuccess(new Gson().fromJson(str, ProblemListModel.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除问题
     */
    public void DeleteProblem(HashMap<String, String> params, final CallBack<DeleteIssueResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.DELETE_PROBLEM, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {

                                callBack.onSuccess(new Gson().fromJson(str, DeleteIssueResult.class));
                                Logger.t("删除问题").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取项目所有人员
     */

    public void GetProjectUser(HashMap<String, String> params,
                               final CallBack<List<ProjectUser>> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GETPROJECTROLEUSER, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("项目所有人").e(str);
                                Type collectionType = new TypeToken<List<ProjectUser>>() {
                                }.getType();
                                List<ProjectUser> lcs = (List<ProjectUser>) new Gson()
                                        .fromJson(str, collectionType);

                                callBack.onSuccess(lcs);

                                //Logger.t("所有项目参与人---------->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    public void getIssueJoiner(HashMap<String, String> params,
                               final CallBack<IssueJoinerResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GETISSUEPEOPLE, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                Logger.t("项目所有人").e(str);

                                callBack.onSuccess(new Gson().fromJson(str, IssueJoinerResult.class));

                                //Logger.t("所有项目参与人---------->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void UploadPhoto(List<File> fileList, HashMap<String, String> params,
                            final CallBack<String> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadPhoto(BASE_URL + ApiConstant.UPLOAD_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                // Logger.t("上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void uploadIssuePhoto(List<File> fileList, HashMap<String, String> params,
                                 final CallBack<IssueUploadPhotoResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadIssuePhoto(BASE_URL + ApiConstant.UPLOAD_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, IssueUploadPhotoResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void updateIssuePhoto(List<File> fileList, HashMap<String, String> params,
                                 final CallBack<IssueUploadPhotoResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadPhoto(BASE_URL + ApiConstant.UPDATE_ISSUE_PHOTO, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, IssueUploadPhotoResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 保存新增问题
     */
    public void SaveIssue(HashMap<String, String> params, final CallBack<NewIssueResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.SAVE_ISSUE, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("新增问题结果----------------->>>").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, NewIssueResult.class));

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {
                                e.printStackTrace();
                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 问题追踪详情
     */
    public void IssueDetail(HashMap<String, String> params, final CallBack<ProblemDetail> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.ISSUE_DEATIL, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, ProblemDetail.class));
                                Logger.t("问题追踪详情----->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 问题追踪详情
     */
    public void getIssueDetail(HashMap<String, String> params,
                               final CallBack<IssueDetailResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.ISSUE_DEATIL, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, IssueDetailResult.class));
                                Logger.t("问题详情----->").e(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 修改问题
     */
    public void UpdateIssue(HashMap<String, String> params, final CallBack<String> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postFormBody(BASE_URL + ApiConstant.UPDATE_ISSUE, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("修改问题参数------------>").e(params.toString());
                                Logger.t("修改问题------------>").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取关联文档
     */
    public void GetRelevance(HashMap<String, String> params,
                             final CallBack<List<DocRelevance>> callBack) {

        OkHttpHelper.getInstance(mContext).postFormData(BIME_URL + ApiConstant.GETDOCBYID, params,
                new IHttpRequestHandler1() {
                    @Override
                    public void onResponse(Call call, Response response, String str) throws IOException {

                        if (TextUtils.isEmpty(str)) {
                            return;
                        }

                        Type collectionType = new TypeToken<List<DocRelevance>>() {
                        }.getType();
                        List<DocRelevance> lcs = (List<DocRelevance>) new Gson()
                                .fromJson(str, collectionType);

                        callBack.onSuccess(lcs);

                        Logger.t("关联文档").e(str);
                    }

                    @Override
                    public void onFailure(Call call, IOException e) {

                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {

                    }
                });
    }

    /**
     * 删除关联文档
     */
    public void DeleteRelevance(HashMap<String, String> params, final CallBack<String> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormBody(BASE_URL + ApiConstant.DELETE_RELEVANCE, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("删除关联文档").e(str);
                                callBack.onSuccess(str);
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除问题照片
     */
    public void deleteIssuePhoto(HashMap<String, String> params,
                                 final CallBack<DeleteIssuePhotoResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.DELETE_ISSUE_PHOTO, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("删除问题照片").e(str);
                                callBack.onSuccess(new Gson().fromJson(str, DeleteIssuePhotoResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 批量新增关联
     */
    public void NewRelevanceDoc(String josn, final CallBack<BaseDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonString(BASE_URL + ApiConstant.NEW_RELEVANCE, josn,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {

                                Logger.t("新增文件关联------------>").e(str);

                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, BaseDto.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取评论列表
     */
    public void GetCommentsList(HashMap<String, String> params, final CallBack<Comments> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_COMMENTS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("评论列表").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, Comments.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取评论列表
     */
    public void GetCommentsList2(HashMap<String, String> params, final CallBack<Comments2> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_COMMENTS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("评论列表").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, Comments2.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除评论
     */
    public void deleteComments(HashMap<String, String> params, final CallBack<DeleteCommentResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.DELETE_COMMENTS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("删除评论----》").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, DeleteCommentResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新增评论
     */
    public void NewComments(HashMap<String, String> params, final CallBack<String> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(BASE_URL + ApiConstant.NEW_COMMENTS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("新增评论---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(str);
                            }
                        });
    }

    /**
     * 修改截止时间
     */

    public void updateIssueDate(HashMap<String, String> params, final CallBack<UpdateIssueDateResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.UPDATE_ISSUE_DATE, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("更新问题的截止时间---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, UpdateIssueDateResult.class));
                            }
                        });
    }

    /**
     * 修改问题状态
     */

    public void updateIssueStatus(HashMap<String, String> params,
                                  final CallBack<UpdateIssueTypeResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.UPDATE_ISSUE_STATUS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("更新问题的状态---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, UpdateIssueTypeResult.class));
                            }
                        });
    }

    /**
     * 修改问题状态
     */

    public void updateIssueType(HashMap<String, String> params, final CallBack<UpdateIssueTypeResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.UPDATE_ISSUE_TYPE, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("更新问题的分类---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, UpdateIssueTypeResult.class));
                            }
                        });
    }

    /**
     * 增加问题参与人
     */

    public void addIssueJoiner(HashMap<String, String> params, final CallBack<UpdateIssuePeopleResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.UPDATE_ISSUE_ADD_JOINER, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("增加问题参与人---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, UpdateIssuePeopleResult.class));
                            }
                        });
    }

    /**
     * 删减问题参与人
     */

    public void deleteIssueJoiner(HashMap<String, String> params,
                                  final CallBack<UpdateIssueTypeResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.UPDATE_ISSUE_DELETE_JOINER, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("删减问题参与人---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, UpdateIssueTypeResult.class));
                            }
                        });
    }

    /**
     * 新增评论
     */
    public void addComments(HashMap<String, String> params, final CallBack<AddNewCommente> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.NEW_COMMENTS, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("新增评论---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, AddNewCommente.class));
                            }
                        });
    }

    /**
     * 上传照片到服务器
     */
    public void uploadIssueCommentsPhoto(List<File> fileList, HashMap<String, String> params,
                                         final CallBack<UploadHideResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadPhoto(BIME_URL + ApiConstant.UPLOAD_FILE_HIDE, fileList, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("上传照片返回数据").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, UploadHideResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新增评论
     */
    public void addCommentsWithPhoto(HashMap<String, String> params,
                                     final CallBack<AddNewCommente> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.ADD_COMMENTS_WITH_PHOTO, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("新增图片评论---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, AddNewCommente.class));
                            }
                        });
    }

    /**
     * 获取问题标签
     */
    public void getIssueTags(HashMap<String, String> params, final CallBack<IssueTagsResult> callBack) {
        OkHttpHelper.getInstance(mContext)
                .getAsync(BASE_URL + ApiConstant.GET_ISSUE_TAG, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("问题标签------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, IssueTagsResult.class));
                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 设置问题标签
     */
    public void setIssueTag(HashMap<String, String> params,
                            final CallBack<BaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.SET_ISSUE_TAG, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("设置问题标签---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, BaseResult.class));
                            }
                        });
    }

    /**
     * 移除问题标签
     */
    public void removeIssueTag(HashMap<String, String> params,
                               final CallBack<BaseResultDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + ApiConstant.REMOVE_ISSUE_TAG, params,
                        new IHttpRequestHandler1() {
                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }

                            @Override
                            public void onResponse(Call call, Response response, String str) throws IOException {
                                Logger.t("移除问题标签---------------------->").e(str);
                                if (TextUtils.isEmpty(str)) {
                                    return;
                                }
                                callBack.onSuccess(new Gson().fromJson(str, BaseResultDto.class));
                            }
                        });
    }
}
