package com.probim.bimenew.controller;

import android.text.TextUtils;
import android.widget.Toast;
import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.model.Company;
import com.probim.bimenew.model.LoginModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.probim.bimenew.result.ExitResultDto;
import com.probim.bimenew.result.LoginResult;
import com.probim.bimenew.result.UserInforResult;
import com.probim.bimenew.result.UserSessionDto;
import com.probim.bimenew.utils.GsonSingleton;
import java.io.IOException;
import java.util.HashMap;
import okhttp3.Call;
import okhttp3.Response;

/**
 * Description :登陆控制器 Author : Gary Email  : <EMAIL> Date   :
 * 2018/6/14/11:26.
 */

public class LoginController {

  String BaseUrl = Hawk.get(CustomParam.Base_URL);


  public void Login(HashMap<String, String> params, final CallBack<LoginModel> callBack) {



    OkHttpHelper.getInstance(BaseApp.getContext())
        .postJsonAsync(BaseUrl + ApiConstant.LOGIN, params, null,
            new IHttpRequestHandler1() {
              @Override
              public void onResponse(Call call, Response response, String str) {

                Logger.t("登录----------->").e(str);
                if (TextUtils.isEmpty(str) || str.contains("502 Bad Gateway")) {
                  Toast.makeText(BaseApp.getContext(), "登录失败或者网络错误502", Toast.LENGTH_SHORT).show();
                  return;
                }
                callBack.onSuccess(new Gson().fromJson(str, LoginModel.class));
              }

              @Override
              public void onFailure(Call call, IOException e) {

              }

              @Override
              public void onResponse(Call call, Response response) {

              }
            });
  }

  /**
   * 账号登录
   */
  public void Login2(String params, final CallBack<LoginResult> callBack) {

    OkHttpHelper.getInstance(BaseApp.getContext())
        .postJsonString(BaseUrl + ApiConstant.LOGIN2, params,
            new IHttpRequestHandler1() {
              @Override public void onResponse(Call call, Response response, String str)
                  throws IOException {
                Logger.t("登录----------->").e(str);

                if (TextUtils.isEmpty(str)) return;
                callBack.onSuccess(new Gson().fromJson(str, LoginResult.class));
              }

              @Override public void onFailure(Call call, IOException e) {

              }

              @Override public void onResponse(Call call, Response response) throws IOException {

              }
            });
  }

  public void GetCompany(HashMap<String, String> params, final CallBack<Company> callBack) {
    OkHttpHelper.getInstance(BaseApp.getContext())
        .getAsync(Hawk.get(CustomParam.CompanyIP) + ApiConstant.GET_COMPANY, params,
            new IHttpRequestHandler1() {
              @Override
              public void onResponse(Call call, Response response, String str) throws IOException {

                Logger.t("企业信息").e(str);
                if (TextUtils.isEmpty(str)) {
                  return;
                }
                callBack.onSuccess(new Gson().fromJson(str, Company.class));
              }

              @Override
              public void onFailure(Call call, IOException e) {

              }

              @Override
              public void onResponse(Call call, Response response) throws IOException {

              }
            });
  }

  /*

  获取登录人信息
   */
  public void getUserInfo(HashMap<String, String> params,
      final CallBack<UserInforResult> callBack) {
    OkHttpHelper.getInstance(BaseApp.getContext()).getAsync(
        BaseUrl + ApiConstant.GetUser, params, new IHttpRequestHandler1() {
          @Override public void onResponse(Call call, Response response, String str)
              throws IOException {
            Logger.t("用户信息").e(str);
            if (TextUtils.isEmpty(str)) return;
            callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, UserInforResult.class));
          }

          @Override public void onFailure(Call call, IOException e) {

          }

          @Override public void onResponse(Call call, Response response) throws IOException {

          }
        });
  }

  /**
   * 注销登录
   */
  public void exit(String params, final CallBack<ExitResultDto> callBack) {
    OkHttpHelper.getInstance(BaseApp.getContext())
        .postJsonString(BaseUrl + ApiConstant.EXIT, params,
            new IHttpRequestHandler1() {
              @Override public void onFailure(Call call, IOException e) {

              }

              @Override public void onResponse(Call call, Response response) throws IOException {

              }

              @Override public void onResponse(Call call, Response response, String str)
                  throws IOException {
                Logger.t("退出登录").e(str);
                if (TextUtils.isEmpty(str)) return;
                callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, ExitResultDto.class));
              }
            });
  }

    /**
     * 获取用户信息
     * @param params
     * @param callBack
     */
  public void getUserSession(HashMap<String,String> params ,final CallBack<UserSessionDto> callBack ){
      OkHttpHelper.getInstance(BaseApp.getContext())
              .getAsync(BaseUrl + ApiConstant.GetUserSession, params,
                      new IHttpRequestHandler1() {
                          @Override public void onFailure(Call call, IOException e) {

                          }

                          @Override public void onResponse(Call call, Response response) throws IOException {

                          }

                          @Override public void onResponse(Call call, Response response, String str)
                                  throws IOException {
                              Logger.t("用户信息").e(str);
                              callBack.onSuccess(GsonSingleton.INSTANCE.jsonTobean(str, UserSessionDto.class));
                          }
                      });
  }
}
