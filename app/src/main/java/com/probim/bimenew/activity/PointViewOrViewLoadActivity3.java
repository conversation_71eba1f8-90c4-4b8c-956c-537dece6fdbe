package com.probim.bimenew.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Picture;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Base64;
import android.view.View;
import android.webkit.ValueCallback;
import android.webkit.WebMessage;
import android.webkit.WebMessagePort;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CustomController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.json.MapToJson;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * Description :视图加载或者视点加载
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/30/17:24.
 */
public class PointViewOrViewLoadActivity3 extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.web_x)
  WebView webX;
  @BindView(R.id.tv_right)
  TextView tvRight;
  private String projectId;
  private String bim365Id;
  private String projectType;
  private String projectName;
  private String WEB_URL = Hawk.get(CustomParam.Web_URL);
  private String modelId;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_web);
    ButterKnife.bind(this);
    StatusBarUtils.transparencyBar(this);
    initWebView();
    loadData();
  }

  /**
   * 初始化webview
   */
  private void initWebView() {
    WebSettings webSetting = webX.getSettings();
    webSetting.setJavaScriptEnabled(true);
    webSetting.setJavaScriptCanOpenWindowsAutomatically(true);
    webSetting.setAllowFileAccess(true);
    webSetting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
    webSetting.setSupportZoom(true);
    webSetting.setBuiltInZoomControls(true);
    webSetting.setUseWideViewPort(true);
    webSetting.setSupportMultipleWindows(true);
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      String viewId = bundle.getString(CustomParam.ViewId);
      modelId = bundle.getString(CustomParam.ModelId);
      String viewPointId = bundle.getString(CustomParam.ViewPointId);
      tvLeft.setText(bundle.getString(CustomParam.TvLeft));
      ProjectBeanDao dao = BaseApp.getInstance().getDao();
      List<ProjectBean> beanList = dao.loadAll();
      for (ProjectBean bean : beanList) {
        projectId = bean.getProjectID();
        bim365Id = bean.getBimProjectId();
        projectType = bean.getProjectType();
        projectName = bean.getProjectName();
      }

      webX.loadUrl(
          WEB_URL + ApiConstant.LOAD_VIEW + "?projectId=" + projectId + "&model=" + modelId);
      //GetSessionId(viewId, modelId, viewPointId, projectId, bim365Id, projectType, projectName);
    }
  }

  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  private void GetSessionId(String viewId, String modelId, String viewPointId, String projectId,
      String bim365Id, String projectType, String projectName) {

    CustomController customController = new CustomController();
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectId);
    params.put("BIM365ProjectID", bim365Id);
    params.put("ProjectType", projectType);
    params.put("ModelID", modelId);
    params.put("ProjectName", projectName);
    params.put("VersionNO", "");
    if (TextUtils.isEmpty(viewPointId)) {
      viewPointId = "";
    }
    params.put("ViewpointID", viewPointId);
    params.put("Snapshot", "fasle");
    params.put("Texture", "true");
    params.put("UserName", Hawk.get(CustomParam.RealName));
    params.put("UserNameCN", "");
    params.put("ViewID", viewId);
    params.put("Access", "");
    params.put("Workflow", "");
    params.put("MarkupCategory", "");
    params.put("ViewpointCategory", "");
    params.put("category", "model");
    String paramStr = MapToJson.simpleMapToJsonStr(params);
    Logger.t("参数拼接------------->").e(paramStr);
    HashMap<String, String> endParams = new HashMap<>();
    endParams.put("Params", paramStr);
    customController.GetSeesionId(endParams, new CallBack<String>() {
      @Override
      public void onSuccess(String s) {

        Logger.t("加载数据------------->").e(WEB_URL + ApiConstant.LOAD_VIEW + "?SessionID=" + s);
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  @SuppressLint("NewApi") @RequiresApi(api = Build.VERSION_CODES.KITKAT)
  @OnClick({ R.id.iv_back, R.id.tv_left, R.id.lin_back, R.id.tv_right })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.tv_left:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        loadViewJs(new JsViewPoistionCallBack() {
          @Override public void selectedViewPoistion(String viewPoistion) {
            /*
             * 相机位置数据
             *
             */
            Intent intent = new Intent(PointViewOrViewLoadActivity3.this, NewIssueActivity.class);
            intent.putExtra(CustomParam.ModelId, modelId);
            intent.putExtra(CustomParam.JSVIEWPOISTION, viewPoistion);
            //intent.putExtra(CustomParam.JSVIEWPOISTIONIMG, modeImgUrl);
            startActivity(intent);
          }
        });
        //androigToJsHtml();
        break;
    }
  }

  /**
   * web加载js
   */
  private void loadViewJs(JsViewPoistionCallBack jsViewPoistionCallBack) {

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
      webX.evaluateJavascript("window.BIMe.view.BIMeViewpoint.getViewPointBasicInfo()",
          new ValueCallback<String>() {
            @Override public void onReceiveValue(String s) {
              Logger.t("相机位置").e(s);
              //模型截图 转换成 数组
              jsViewPoistionCallBack.selectedViewPoistion(s);
              /*String modelImgUrl = bitmapToByte(sceenshot2webiew(webX));

              if (!TextUtils.isEmpty(modelImgUrl)) {


              }*/
            }
          });
    }

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
      webX.evaluateJavascript("BIMe.control.BIMeUtility.getPicture()",
          new ValueCallback<String>() {
            @Override public void onReceiveValue(String s) {
              Logger.t("图片截图").e(s);
              //模型截图 转换成 数组

            }
          });
    }
  }

  /***
   * jsViewPoistion CallBack
   */
  interface JsViewPoistionCallBack {

    void selectedViewPoistion(String viewPoistion);
  }

  /**
   * 截取 webview
   */
  public static Bitmap loadBitmapFromView(WebView v) {
    if (v == null) {
      return null;
    }
    Picture snapShot = v.capturePicture();
    Bitmap bmp = Bitmap.createBitmap(snapShot.getWidth(),
        snapShot.getHeight(), Bitmap.Config.ARGB_8888);
    Canvas canvas = new Canvas(bmp);
    snapShot.draw(canvas);
    return bmp;
  }

  /**
   *
   */

  private String bitmapToByte(Bitmap bitmap) {

    //其次，转为输出流，转为字节数组
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    bitmap.compress(Bitmap.CompressFormat.PNG, 50, baos);
    String imageBase64 = new String(Base64.encode(baos.toByteArray(), 0));
    return imageBase64;
  }

  /**
   * 截图是黑色背景，还可以提供一种思路，画笔–画板，把这个图画在一张白色的画板上试试呗
   */
  public static Bitmap drawBg4Bitmap(Bitmap orginBitmap) {
    Paint paint = new Paint();
    paint.setColor(R.color.white);
    Bitmap bitmap = Bitmap.createBitmap(orginBitmap.getWidth(),
        orginBitmap.getHeight(), orginBitmap.getConfig());
    Canvas canvas = new Canvas(bitmap);
    canvas.drawRect(0, 0, orginBitmap.getWidth(), orginBitmap.getHeight(), paint);
    canvas.drawBitmap(orginBitmap, 0, 0, paint);
    if (orginBitmap != null) {
      orginBitmap.recycle();
    }
    saveImageToGallery(BaseApp.getContext(), bitmap);
    return bitmap;
  }

  public static void saveImageToGallery(Context context, Bitmap bmp) {
    // 首先保存图片路径
    File appDir = new File(Environment.getExternalStorageDirectory(),
        "BIMe");
    if (!appDir.exists()) {
      appDir.mkdir();
    }
    //当前时间来命名图片
    String fileName = System.currentTimeMillis() + ".png";
    File file = new File(appDir, fileName);
    try {
      FileOutputStream fos = new FileOutputStream(file);
      bmp.compress(Bitmap.CompressFormat.PNG, 100, fos);
      fos.flush();
      fos.close();
    } catch (FileNotFoundException e) {
      e.printStackTrace();
    } catch (IOException e) {
      e.printStackTrace();
    }

    // 其次把文件插入到系统图库
    try {
      MediaStore.Images.Media.insertImage(context.getContentResolver(),
          file.getAbsolutePath(), fileName, null);
    } catch (FileNotFoundException e) {
      e.printStackTrace();
    }
    // 最后通知图库更新
    Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
    Uri uri = Uri.fromFile(file);
    intent.setData(uri);
    context.sendBroadcast(intent);

    Toast.makeText(context, "图片保存到本地成功", Toast.LENGTH_SHORT).show();
  }

  /**
   * 截取 webview
   */

  private Bitmap sceenshot2webiew(WebView webView) {
   /* // WebView 生成当前屏幕大小的图片，shortImage 就是最终生成的图片
    Bitmap shortImage = Bitmap.createBitmap(webView.getMeasuredWidth(), webView.getMeasuredHeight(),
        Bitmap.Config.RGB_565);
    Canvas canvas = new Canvas(shortImage);   // 画布的宽高和屏幕的宽高保持一致
    Paint paint = new Paint();
    canvas.drawBitmap(shortImage, webView.getMeasuredWidth(), webView.getMeasuredHeight(), paint);
    webView.draw(canvas);
    saveImageToGallery(BaseApp.getContext(), shortImage);
    return shortImage;*/

   /* View cv = getWindow().getDecorView();
    Bitmap bmp = Bitmap.createBitmap(cv.getWidth(), cv.getHeight(), Bitmap.Config.ARGB_8888);
    Canvas canvas = new Canvas(bmp);
    cv.draw(canvas);
    saveImageToGallery(BaseApp.getContext(),bmp);
    return bmp;*/

   /* Picture snapShot = webView.capturePicture();
    Bitmap bmp = Bitmap.createBitmap(snapShot.getWidth(),
        snapShot.getHeight(), Bitmap.Config.ARGB_8888);
    Canvas canvas = new Canvas(bmp);
    snapShot.draw(canvas);
    saveImageToGallery(BaseApp.getContext(),bmp);
    return bmp;*/
    Bitmap bmp = webView.getDrawingCache();
    saveImageToGallery(BaseApp.getContext(), bmp);
    return bmp;
  }

  /**
   * 使用HTML消息通道
   */
  @RequiresApi(api = Build.VERSION_CODES.M)
  private void androigToJsHtml() {

    // messagePorts[0] and messagePorts[1] represent the two ports.
    // They are already tangled to each other and have been started.
    WebMessagePort[] channel = webX.createWebMessageChannel();

    // Create handler for channel[0] to receive messages.
    channel[0].setWebMessageCallback(new WebMessagePort.WebMessageCallback() {
      @Override
      public void onMessage(WebMessagePort port, WebMessage message) {
        Logger.t("html 通信").e("On port " + port + ", received this message: " + message.getData());
      }
    });

    // Send a message from channel[1] to channel[0].
    channel[1].postMessage(
        new WebMessage("window.BIMe.control.BIMeUtility.getPicture().then((image_base64)"));
  }
}
