package com.probim.bimenew.activity.schedule.adapter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.schedule.dto.SchedualNewMaterialDTO;
import com.probim.bimenew.interfaces.IOnItemSchedualMaterailClickListener;
import com.probim.bimenew.result.MateriaOriginListBean;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualNewMaterialListAdapter extends RecyclerView.Adapter<SchedualNewMaterialListAdapter.ViewHolder> {

    private List<SchedualNewMaterialDTO> dtoList = new ArrayList<>();
    private IOnItemSchedualMaterailClickListener onItemClickListener;

    public SchedualNewMaterialListAdapter(List<SchedualNewMaterialDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_material, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewMaterialDTO dto = dtoList.get(position);
        int xx = position + 1;
        holder.tvTitle.setText(" 材料" + xx);
        holder.tvName.setText(dto.getMachineDetial_Name());
        holder.edtSize.setText(dto.getMachineDetial_Size() + "");
        holder.edtUnit.setText(dto.getMachineDetial_Unit() + "");
        holder.tvSum.setText(dto.getMachineDetial_Sum() + "");
        holder.edtApprocach.setText(dto.getMachineDetial_Approcach() + "");
        holder.tvEstimated.setText(dto.getMachineDetial_Estimated());
        holder.edtRemark.setText(dto.getMachineDetial_Remark());
        holder.edtProcudt.setText(dto.getMachineDetial_Product());
        //  部位数据
        initRecyclerView(holder.rvParts, dto.getMachineDetial_Parts());
       /* holder.tvName.setText(dto.getMaterialsDetial_Name());
        holder.edtPlan.setText(dto.getMaterialsDetial_PlanNum() + "");
        holder.edtChange.setText(dto.getMaterialsDetial_AdddelNum() + "");
        holder.edtLive.setText(dto.getMaterialsDetial_NewNum() + "");
        holder.edtLeiji.setText(dto.getMaterialsDetial_AddNum() + "");*/
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
        holder.rlSelectMaterail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onMaterailClick(position, dto);
                }
            }
        });
        holder.rlSelectEs.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onEstimeClick(position, dto);
                }
            }
        });
        holder.linSelectParts.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onPartsClick(position, dto);
                }
            }
        });
        holder.edtSize.clearFocus();
        holder.edtSize.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showSizeChange(position, editable.toString());
                }

            }
        });
        holder.edtApprocach.clearFocus();
        holder.edtApprocach.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showApproChange(position, editable.toString());
                }

            }
        });
        holder.edtRemark.clearFocus();
        holder.edtRemark.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showRemarkChange(position, editable.toString());
                }

            }
        });

        holder.edtProcudt.clearFocus();
        holder.edtProcudt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showProductChange(position, editable.toString());
                }

            }
        });
        holder.edtUnit.clearFocus();
        holder.edtUnit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showUnitChange(position, editable.toString());
                }

            }
        });
        holder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onDelete(position, dto);
            }
        });

    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualMaterailClickListener listener) {
        this.onItemClickListener = listener;
    }

    /**
     * 初始化数据
     *
     * @param rvParts
     * @param machineDetial_parts
     */
    private void initRecyclerView(RecyclerView rvParts, String machineDetial_parts) {
        if (!TextUtils.isEmpty(machineDetial_parts)) {
            Type collectionType = new TypeToken<List<MateriaOriginListBean.DataBean.ListBean>>() {
            }.getType();
            List<MateriaOriginListBean.DataBean.ListBean> lcs = (List<MateriaOriginListBean.DataBean.ListBean>) new Gson().fromJson(machineDetial_parts, collectionType);
            SchedualPartsAdapter schedualPartsAdapter = new SchedualPartsAdapter(lcs);
            VerticalNoItemRecyclerView.initialize(rvParts).setAdapter(schedualPartsAdapter);

        }
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_mec_name)
        TextView tvName;
        @BindView(R.id.edt_remark)
        EditText edtRemark;
        @BindView(R.id.edt_product)
        EditText edtProcudt;
        @BindView(R.id.tv_title)
        TextView tvTitle;
        @BindView(R.id.rl_select_estimated)
        RelativeLayout rlSelectEs;
        @BindView(R.id.rl_select_materail)
        RelativeLayout rlSelectMaterail;
        @BindView(R.id.tv_estimated)
        TextView tvEstimated;
        @BindView(R.id.rl_select_parts)
        LinearLayout linSelectParts;
        @BindView(R.id.edt_size)
        EditText edtSize;
        @BindView(R.id.edt_unit)
        EditText edtUnit;
        @BindView(R.id.tv_sum)
        TextView tvSum;
        @BindView(R.id.edt_approcach)
        EditText edtApprocach;
        @BindView(R.id.tv_add)
        TextView tvAdd;
        @BindView(R.id.tv_parts)
        TextView tvParts;
        @BindView(R.id.iv_delete_item)
        ImageView ivDelete;
        @BindView(R.id.rv_parts)
        RecyclerView rvParts;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
