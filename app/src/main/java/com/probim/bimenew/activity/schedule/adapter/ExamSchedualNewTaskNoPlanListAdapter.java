package com.probim.bimenew.activity.schedule.adapter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualNewTaskNoPlanDTO;
import com.probim.bimenew.interfaces.IOnItemSchedualTaskNoPlanClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class ExamSchedualNewTaskNoPlanListAdapter extends RecyclerView.Adapter<ExamSchedualNewTaskNoPlanListAdapter.ViewHolder> {
    private IOnItemSchedualTaskNoPlanClickListener onItemClickListener;
    private List<SchedualNewTaskNoPlanDTO> dtoList = new ArrayList<>();

    public ExamSchedualNewTaskNoPlanListAdapter(List<SchedualNewTaskNoPlanDTO> dtoList) {
        this.dtoList = dtoList;
    }


    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_exam_new_task_no_plan, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewTaskNoPlanDTO dto = dtoList.get(position);
        int temp = position + 1;
        holder.tvNoPlanName.setText("序号" + temp);
        holder.edtNoPlan.setText(dto.getNoProgress_Name());
        holder.edtNoPlan.clearFocus();
        holder.edtNoPlan.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    onItemClickListener.showEdtNoPlanChange(position, editable.toString());
                }
            }
        });
        holder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onDelete(position, dto);
            }
        });
    }


    @Override
    public int getItemCount() {

        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualTaskNoPlanClickListener listener) {
        this.onItemClickListener = listener;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.edt_no_plan)
        EditText edtNoPlan;
        @BindView(R.id.tv_no_plan_name)
        TextView tvNoPlanName;
        @BindView(R.id.iv_delete_item)
        ImageView ivDelete;


        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }


}
