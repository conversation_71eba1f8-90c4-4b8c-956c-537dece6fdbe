package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.TwoDimenAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.model.ModelTwoDimensModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.view.AutoLoadRecyclerView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/1/15/16:57.
 */

public class TwoDimensionalForNewQuestionActivity extends BaseActivity {
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.rv_two_dim) AutoLoadRecyclerView rvTwoDim;
  @BindView(R.id.smartRefreshLayout) SmartRefreshLayout smartRefreshLayout;
  @BindView(R.id.rv_status_layout) RelativeLayout rvStatusLayout;
  @BindView(R.id.tv_status) TextView tvStatus;
  @BindView(R.id.tv_right) TextView tvRight;
  private List<ModelTwoDimensModel.SheetsBean> list = new ArrayList<>();
  private TwoDimenAdapter adapter;
  private String projectId;
  private String modelId;


  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_two_dimensional_view);
    ButterKnife.bind(this);
    initView();
    loadData();
  }

  @Override protected void loadData() {
    ModelController controller = new ModelController();
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectId);
    params.put("ModelID", modelId);
    params.put("VersionNO", "");
    params.put("FileType", "PlanView");
    params.put("FileName", "sheets");
    controller.GetTwoDimensional(params, new CallBack<ModelTwoDimensModel>() {
      @Override
      public void onSuccess(ModelTwoDimensModel modelTwoDimensModel) {

        list.clear();
        list.addAll(modelTwoDimensModel.getSheets());
        adapter.notifyDataSetChanged();
      }

      @Override
      public void onFail(String erroMsg) {
        rvStatusLayout.setVisibility(View.VISIBLE);
        tvStatus.setText("暂无数据");
      }
    });
  }

  @Override protected void initView() {
    tvTitle.setText("图纸选择");
    if (getIntent() != null) {
      modelId = getIntent().getStringExtra(CustomParam.ModelId);
      projectId = getIntent().getStringExtra(CustomParam.ProjectId);
      initRecycleview();
      initRefresh();
    }
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

    //为 RecyclerView 设置布局管理器
    rvTwoDim.setLayoutManager(new LinearLayoutManager(this));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvTwoDim.addItemDecoration(
        new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    //动画
    rvTwoDim.setItemAnimator(new DefaultItemAnimator());

    adapter = new TwoDimenAdapter(this, list);

    rvTwoDim.setAdapter(adapter);

    adapter.addRecycleItemListener(new TwoDimenAdapter.OnRecycleItemListener() {
      @Override
      public void OnRecycleItemClick(View v, Object o) {
        ModelTwoDimensModel.SheetsBean data = (ModelTwoDimensModel.SheetsBean) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.TwoDimensName, data.getName());
        bundle.putString(CustomParam.TwoDimensId, data.getId());
        bundle.putString(CustomParam.ProjectId, projectId);
        bundle.putString(CustomParam.ModelId, modelId);
        Intent intent =
            new Intent(TwoDimensionalForNewQuestionActivity.this, MarkActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        finish();
      }
    });
  }

  @Override protected void initRefresh() {

  }

  @OnClick({ R.id.lin_back, R.id.tv_title,R.id.tv_right }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_title:
        break;
      case R.id.tv_right:
        finish();
        break;
    }
  }
}
