package com.probim.bimenew.activity.flow;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.dto.AllFlowDto;
import com.probim.bimenew.dto.HandleFlowDataDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;

import java.util.ArrayList;
import java.util.List;

public class AllFlowItemAdapter extends RecyclerView.Adapter<AllFlowItemAdapter.ViewHolder> {

    private List<AllFlowDto> mDatas = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;

    public AllFlowItemAdapter(List<AllFlowDto> mDatas) {
        this.mDatas = mDatas;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_flow_item, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AllFlowDto dto = mDatas.get(position);
        holder.tvName.setText(dto.getTitle());
        holder.tvUser.setText(dto.getStarter());
        holder.tvStart.setText(dto.getRDT());
        if (!TextUtils.isEmpty(dto.getSendDT())) {
            holder.linEnd.setVisibility(View.VISIBLE);
            holder.tvEnd.setText(dto.getSendDT());
        } else {
            holder.linEnd.setVisibility(View.GONE);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onClick(position, dto);
            }
        });

    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvName;
        private final TextView tvUser;
        private final TextView tvStart;
        private final TextView tvEnd;
        private final LinearLayout linEnd;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_flow_name);
            tvUser = itemView.findViewById(R.id.tv_createUser);
            tvStart = itemView.findViewById(R.id.tv_start_time);
            tvEnd = itemView.findViewById(R.id.tv_end_time);
            linEnd = itemView.findViewById(R.id.lin_end);
        }
    }
}
