package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualNewTaskNoPlanDTO {

    @SerializedName("NoProgress_unittime")
    private String noProgress_unittime;
    @SerializedName("NoProgress_Username")
    private String noProgress_Username;
    @SerializedName("NoProgress_Name")
    private String noProgress_Name;
    private String NoProgress_ProjectID;
    private String organizeId;
    private String NoProgress_MobileCompany;

    private String Progress_Name;

    public String getProgress_Name() {
        return Progress_Name;
    }

    public void setProgress_Name(String progress_Name) {
        Progress_Name = progress_Name;
    }

    public String getNoProgress_MobileCompany() {
        return NoProgress_MobileCompany;
    }

    public void setNoProgress_MobileCompany(String noProgress_MobileCompany) {
        NoProgress_MobileCompany = noProgress_MobileCompany;
    }

    public String getOrganizeId() {
        return organizeId;
    }

    public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
    }

    public String getNoProgress_ProjectID() {
        return NoProgress_ProjectID;
    }

    public void setNoProgress_ProjectID(String noProgress_ProjectID) {
        NoProgress_ProjectID = noProgress_ProjectID;
    }
    /* @SerializedName("TaskType")
    private int taskType;

    public int getTaskType() {
        return taskType;
    }

    public void setTaskType(int taskType) {
        this.taskType = taskType;
    }
*/

    public String getNoProgress_unittime() {
        return noProgress_unittime;
    }

    public void setNoProgress_unittime(String noProgress_unittime) {
        this.noProgress_unittime = noProgress_unittime;
    }

    public String getNoProgress_Username() {
        return noProgress_Username;
    }

    public void setNoProgress_Username(String noProgress_Username) {
        this.noProgress_Username = noProgress_Username;
    }

    public String getNoProgress_Name() {
        return noProgress_Name;
    }

    public void setNoProgress_Name(String noProgress_Name) {
        this.noProgress_Name = noProgress_Name;
    }
}
