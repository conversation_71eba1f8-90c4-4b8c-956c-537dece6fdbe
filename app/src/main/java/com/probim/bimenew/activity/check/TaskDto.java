package com.probim.bimenew.activity.check;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/8/19/11:37
 */
public class TaskDto {

  /**
   * UID_ : 7
   * ID_ : 7
   * NAME_ : 任务二三
   * START_ : 2019-12-10T00:00:00
   * FINISH_ : 2019-12-16T23:59:59
   * DURATION_ : 5
   * WORK_ : 0
   * PERCENTCOMPLETE_ : 0
   * MANUAL_ : 0
   * WEIGHT_ : 0
   * CONSTRAINTTYPE_ : 4
   * CONSTRAINTDATE_ : 2019-12-10T00:00:00
   * MILESTONE_ : 0
   * SUMMARY_ : 0
   * CRITICAL_ : 1
   * PRIORITY_ : 0
   * NOTES_ : null
   * DEPARTMENT_ : null
   * PRINCIPAL_ : null
   * PREDECESSORLINK_ : [] 2020-08-19 11:33:41.309 21978-21978/com.probim.BIMe E/PRETTY_LOGGER-检查详情----------: │
   * FIXEDDATE_ : 0
   * PARENTTASKUID_ : 4
   * PROJECTUID_ : 1de6c92b-76d6-4115-8c20-9e51c4c16cab
   * ACTUALSTART_ : null
   * ACTUALFINISH_ : null
   * ACTUALDURATION_ : 0
   * ASSIGNMENTS_ : null
   * WBS_ : 2.3
   * CRITICAL2_ : null
   */

  private String UID_;
  private int ID_;
  private String NAME_;
  private String START_;
  private String FINISH_;
  private int DURATION_;
  private int WORK_;
  private int PERCENTCOMPLETE_;
  private int MANUAL_;
  private int WEIGHT_;
  private int CONSTRAINTTYPE_;
  private String CONSTRAINTDATE_;
  private int MILESTONE_;
  private int SUMMARY_;
  private int CRITICAL_;
  private int PRIORITY_;
  private Object NOTES_;
  private Object DEPARTMENT_;
  private Object PRINCIPAL_;
  private String PREDECESSORLINK_;
  private int FIXEDDATE_;
  private String PARENTTASKUID_;
  private String PROJECTUID_;
  private Object ACTUALSTART_;
  private Object ACTUALFINISH_;
  private int ACTUALDURATION_;
  private Object ASSIGNMENTS_;
  private String WBS_;
  private Object CRITICAL2_;

  public String getUID_() {
    return UID_;
  }

  public void setUID_(String UID_) {
    this.UID_ = UID_;
  }

  public int getID_() {
    return ID_;
  }

  public void setID_(int ID_) {
    this.ID_ = ID_;
  }

  public String getNAME_() {
    return NAME_;
  }

  public void setNAME_(String NAME_) {
    this.NAME_ = NAME_;
  }

  public String getSTART_() {
    return START_;
  }

  public void setSTART_(String START_) {
    this.START_ = START_;
  }

  public String getFINISH_() {
    return FINISH_;
  }

  public void setFINISH_(String FINISH_) {
    this.FINISH_ = FINISH_;
  }

  public int getDURATION_() {
    return DURATION_;
  }

  public void setDURATION_(int DURATION_) {
    this.DURATION_ = DURATION_;
  }

  public int getWORK_() {
    return WORK_;
  }

  public void setWORK_(int WORK_) {
    this.WORK_ = WORK_;
  }

  public int getPERCENTCOMPLETE_() {
    return PERCENTCOMPLETE_;
  }

  public void setPERCENTCOMPLETE_(int PERCENTCOMPLETE_) {
    this.PERCENTCOMPLETE_ = PERCENTCOMPLETE_;
  }

  public int getMANUAL_() {
    return MANUAL_;
  }

  public void setMANUAL_(int MANUAL_) {
    this.MANUAL_ = MANUAL_;
  }

  public int getWEIGHT_() {
    return WEIGHT_;
  }

  public void setWEIGHT_(int WEIGHT_) {
    this.WEIGHT_ = WEIGHT_;
  }

  public int getCONSTRAINTTYPE_() {
    return CONSTRAINTTYPE_;
  }

  public void setCONSTRAINTTYPE_(int CONSTRAINTTYPE_) {
    this.CONSTRAINTTYPE_ = CONSTRAINTTYPE_;
  }

  public String getCONSTRAINTDATE_() {
    return CONSTRAINTDATE_;
  }

  public void setCONSTRAINTDATE_(String CONSTRAINTDATE_) {
    this.CONSTRAINTDATE_ = CONSTRAINTDATE_;
  }

  public int getMILESTONE_() {
    return MILESTONE_;
  }

  public void setMILESTONE_(int MILESTONE_) {
    this.MILESTONE_ = MILESTONE_;
  }

  public int getSUMMARY_() {
    return SUMMARY_;
  }

  public void setSUMMARY_(int SUMMARY_) {
    this.SUMMARY_ = SUMMARY_;
  }

  public int getCRITICAL_() {
    return CRITICAL_;
  }

  public void setCRITICAL_(int CRITICAL_) {
    this.CRITICAL_ = CRITICAL_;
  }

  public int getPRIORITY_() {
    return PRIORITY_;
  }

  public void setPRIORITY_(int PRIORITY_) {
    this.PRIORITY_ = PRIORITY_;
  }

  public Object getNOTES_() {
    return NOTES_;
  }

  public void setNOTES_(Object NOTES_) {
    this.NOTES_ = NOTES_;
  }

  public Object getDEPARTMENT_() {
    return DEPARTMENT_;
  }

  public void setDEPARTMENT_(Object DEPARTMENT_) {
    this.DEPARTMENT_ = DEPARTMENT_;
  }

  public Object getPRINCIPAL_() {
    return PRINCIPAL_;
  }

  public void setPRINCIPAL_(Object PRINCIPAL_) {
    this.PRINCIPAL_ = PRINCIPAL_;
  }

  public String getPREDECESSORLINK_() {
    return PREDECESSORLINK_;
  }

  public void setPREDECESSORLINK_(String PREDECESSORLINK_) {
    this.PREDECESSORLINK_ = PREDECESSORLINK_;
  }

  public int getFIXEDDATE_() {
    return FIXEDDATE_;
  }

  public void setFIXEDDATE_(int FIXEDDATE_) {
    this.FIXEDDATE_ = FIXEDDATE_;
  }

  public String getPARENTTASKUID_() {
    return PARENTTASKUID_;
  }

  public void setPARENTTASKUID_(String PARENTTASKUID_) {
    this.PARENTTASKUID_ = PARENTTASKUID_;
  }

  public String getPROJECTUID_() {
    return PROJECTUID_;
  }

  public void setPROJECTUID_(String PROJECTUID_) {
    this.PROJECTUID_ = PROJECTUID_;
  }

  public Object getACTUALSTART_() {
    return ACTUALSTART_;
  }

  public void setACTUALSTART_(Object ACTUALSTART_) {
    this.ACTUALSTART_ = ACTUALSTART_;
  }

  public Object getACTUALFINISH_() {
    return ACTUALFINISH_;
  }

  public void setACTUALFINISH_(Object ACTUALFINISH_) {
    this.ACTUALFINISH_ = ACTUALFINISH_;
  }

  public int getACTUALDURATION_() {
    return ACTUALDURATION_;
  }

  public void setACTUALDURATION_(int ACTUALDURATION_) {
    this.ACTUALDURATION_ = ACTUALDURATION_;
  }

  public Object getASSIGNMENTS_() {
    return ASSIGNMENTS_;
  }

  public void setASSIGNMENTS_(Object ASSIGNMENTS_) {
    this.ASSIGNMENTS_ = ASSIGNMENTS_;
  }

  public String getWBS_() {
    return WBS_;
  }

  public void setWBS_(String WBS_) {
    this.WBS_ = WBS_;
  }

  public Object getCRITICAL2_() {
    return CRITICAL2_;
  }

  public void setCRITICAL2_(Object CRITICAL2_) {
    this.CRITICAL2_ = CRITICAL2_;
  }
}
