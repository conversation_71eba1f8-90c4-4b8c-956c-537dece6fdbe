package com.probim.bimenew.activity.check;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.NewCheckActivity;
import com.probim.bimenew.adapter.AllPeopleAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.CheckPeopleDto;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllPeoplerResult;
import com.probim.bimenew.result.AllUsersDto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :全部人员
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class AllPeopleListActivity extends BaseActivity implements View.OnClickListener {

    private final List<AllUsersDto.DataBean.ListBean> projectRoleList = new ArrayList<>();
    private String projectId;
    private AllPeopleAdapter allAdapter;
    private CheckController checkController;
    private EditText edtSearch;
    private boolean isDetails;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_people);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        initListener();
        loadData();
    }

    @Override
    protected void loadData() {
        getProjectDao();
        checkController = new CheckController();
        getProjectUsers("");
    }

    @Override
    protected void initView() {
        if (getIntent() != null) {
            isDetails = getIntent().getBooleanExtra("isCheckDetails", false);
        }
        TextView tvTittle = findViewById(R.id.tv_black_title);
        tvTittle.setText("指定检查人");
        TextView tvRight = findView(R.id.tv_right, this);
        LinearLayout liniBack = findView(R.id.lin_back, this);
        LinearLayout linSearch = findView(R.id.lin_search, this);
        RelativeLayout rl = findView(R.id.rl_role_user);
        rl.setVisibility(View.GONE);
        edtSearch = findView(R.id.edt_search);

        //初始化全部人员适配器
        setAllAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            projectId = bean.getProjectID();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.lin_back:

                finish();

                break;

            default:
                break;
        }
    }

    /**
     * 设置文件夹适配器
     */
    private void setAllAdapter() {
        RecyclerView rvAllPeople = findView(R.id.rv_all_people);
        allAdapter = new AllPeopleAdapter(this, projectRoleList);
        allAdapter.addRecycleItemListener(new AllPeopleAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {

            }
        });
        VerticalRecyclerView.initialize(rvAllPeople).setAdapter(allAdapter);
        allAdapter.addRecycleItemListener(new AllPeopleAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                Intent intent = null;
                if (isDetails) {
                    intent = new Intent(AllPeopleListActivity.this, CheckDetailsActivity.class);
                } else {
                    intent = new Intent(AllPeopleListActivity.this, NewCheckActivity.class);
                }
                Bundle bundle = new Bundle();
                // dataBeanList.add(projectRoleList.get(pos));
                AllUsersDto.DataBean.ListBean dataBean = projectRoleList.get(pos);
                CheckPeopleDto dto = new CheckPeopleDto(dataBean.getRealName(), dataBean.getUserId());
                bundle.putSerializable(CustomParam.StartForResult, (Serializable) dto);
                intent.putExtras(bundle);
                setResult(RESULT_OK, intent);
                finish();
            }
        });
        rvAllPeople.setAdapter(allAdapter);
    }

    /**
     * 获取项目所有人员
     */
    private void getProjectUsers(String keyWord) {

        HashMap<String, String> params = new HashMap<>();
        params.put("PageNum", "1");
        params.put("PageSize", "999");
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("OrganizeId", projectId);
        checkController.getCheckPeople(params, new CallBack<AllUsersDto>() {
            @Override
            public void onSuccess(AllUsersDto allUsersDto) {
                projectRoleList.clear();
                for (AllUsersDto.DataBean.ListBean dataBean : allUsersDto.getData().getList()) {
                    projectRoleList.add(dataBean);
                    allAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //键盘搜索键
    private void initListener() {

        edtSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView textView,
                                          int i, KeyEvent keyEvent) {
                if (i == EditorInfo.IME_ACTION_SEARCH) {
                    getProjectUsers(
                            edtSearch.getText().toString().trim());
                    return true;
                }
                return false;
            }
        });
    }
}
