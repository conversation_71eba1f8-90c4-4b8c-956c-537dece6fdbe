package com.probim.bimenew.activity.check;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.application.BaseApp;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/10/15/16:29.
 * horizontal
 */

public class GridNoItemRecyclerView {

    public static RecyclerView initialize(RecyclerView recyclerView) {


        recyclerView.setLayoutManager(new GridLayoutManager(BaseApp.getContext(),3) {
            @Override
            public RecyclerView.LayoutParams generateDefaultLayoutParams() {
                RecyclerView.LayoutParams layoutParams =
                        new RecyclerView.LayoutParams(RecyclerView.LayoutParams.MATCH_PARENT,
                                RecyclerView.LayoutParams.WRAP_CONTENT);
                return layoutParams;
            }
        });

  /*  recyclerView.addItemDecoration(
        new DividerItemDecoration(BaseApp.getContext(), DividerItemDecoration.VERTICAL));
    //动画
    recyclerView.setItemAnimator(new DefaultItemAnimator());*/
        return recyclerView;
    }
}
