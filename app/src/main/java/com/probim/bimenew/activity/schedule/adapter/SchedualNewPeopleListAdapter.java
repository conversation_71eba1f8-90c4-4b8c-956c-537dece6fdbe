package com.probim.bimenew.activity.schedule.adapter;

import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualNewPeopleDto;
import com.probim.bimenew.interfaces.IOnItemSchedualPeopleClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualNewPeopleListAdapter extends RecyclerView.Adapter<SchedualNewPeopleListAdapter.ViewHolder> {

    private List<SchedualNewPeopleDto> dtoList = new ArrayList<>();
    private IOnItemSchedualPeopleClickListener onItemClickListener;
    private Handler handler = new Handler();

    public SchedualNewPeopleListAdapter(List<SchedualNewPeopleDto> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_people, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewPeopleDto dto = dtoList.get(position);
        int xx = position + 1;
        holder.tvTitle.setText(" 人员" + xx);
        holder.tvPeopelName.setText(dto.getMobileUserDetial_type());
        if (holder.tvPeopelName.getText().equals("其他")) {
            holder.edtPeople.setVisibility(View.VISIBLE);
        } else {
            holder.edtPeople.setVisibility(View.GONE);
        }
        holder.edtPlan.setText(dto.getMobileUserDetial_PlanNum() + "");
        holder.edtChange.setText(dto.getMobileUserDetial_AdddelNum() + "");
        holder.edtLive.setText(dto.getMobileUserDetial_NewNum() + "");
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
        holder.rlSelectPeople.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onPeopleTypeClick(position, dto);
                }
            }
        });
        holder.edtChange.clearFocus();
        holder.edtChange.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        Runnable runnable = new Runnable() {
                            @Override
                            public void run() {
                                //结束后进行操作
                                //holder.edtChange.setFocusable(false);
                                onItemClickListener.showAddChange(position, editable.toString());
                            }
                        };
                        handler.postDelayed(runnable, 1600);

                    }

                }

            }
        });

        holder.edtLive.clearFocus();
        holder.edtLive.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        Runnable runnable = new Runnable() {
                            @Override
                            public void run() {
                                //结束后进行操作
                                // holder.edtChange.setFocusable(false);
                                onItemClickListener.showLiveChange(position, editable.toString());
                            }
                        };
                        handler.postDelayed(runnable, 2000);

                    }

                }


            }
        });
        holder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onDelete(position, dto);
            }
        });
        holder.edtPeople.clearFocus();
        holder.edtPeople.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showEdt(position, editable.toString());
                }


            }
        });

    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualPeopleClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_people_name)
        TextView tvPeopelName;
        @BindView(R.id.edt_plan)
        TextView edtPlan;
        @BindView(R.id.edt_change)
        TextView edtChange;
        @BindView(R.id.edt_live)
        TextView edtLive;
        @BindView(R.id.tv_people_title)
        TextView tvTitle;
        @BindView(R.id.rl_select_people)
        RelativeLayout rlSelectPeople;
        @BindView(R.id.iv_delete_item)
        ImageView ivDelete;
        @BindView(R.id.edt_people)
        EditText edtPeople;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
