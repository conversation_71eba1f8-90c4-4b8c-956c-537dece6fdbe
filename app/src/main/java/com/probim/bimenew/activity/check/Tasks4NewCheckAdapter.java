package com.probim.bimenew.activity.check;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.result.AllTaskItemResult;

import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/8/19/11:36
 */
public class Tasks4NewCheckAdapter
    extends RecyclerView.Adapter<Tasks4NewCheckAdapter.ViewHolder> {
  private Context mContex;
  private List<AllTaskItemResult.DataBean.TasksBean> mDatas;
  private OnRecycleItemListener mRecycleItemListener;

  public Tasks4NewCheckAdapter(Context mContex,
      List<AllTaskItemResult.DataBean.TasksBean> mDatas) {
    this.mContex = mContex;
    this.mDatas = mDatas;
  }

  @NonNull @Override public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
    return new ViewHolder(
        LayoutInflater.from(mContex).inflate(R.layout.item_rv_tasks, null, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
    AllTaskItemResult.DataBean.TasksBean dto = mDatas.get(i);
    viewHolder.tvTaskName.setText(dto.getNAME_());
    viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View v) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnRecycleItemClick(i, dto);
        }
      }
    });
  }

  @Override public int getItemCount() {
    return mDatas.size();
  }

  static
  class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_task_name) TextView tvTaskName;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
