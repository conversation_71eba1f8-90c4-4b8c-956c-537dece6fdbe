package com.probim.bimenew.activity.check;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;

import java.util.List;

public class NewPhoto2CheckAdapter extends RecyclerView.Adapter<NewPhoto2CheckAdapter.VHolder> {
    private final List<String> photoDtoList;
    private OnItemClickListener mItemClickListener;

    public NewPhoto2CheckAdapter(List<String> list) {
        this.photoDtoList = list;

    }


    @NonNull
    @Override
    public VHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new VHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_new_check_photo, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VHolder vHolder, int i) {
        String url = photoDtoList.get(i);
        Glide.with(vHolder.igv.getContext()).load(url).into(vHolder.igv);
        vHolder.ivDelete.setVisibility(View.GONE);
        vHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mItemClickListener.OnItemClick(i);
            }
        });

    }


    @Override
    public int getItemCount() {
        return photoDtoList.size();
    }

    public void addItemClickListener(OnItemClickListener onItemListener) {

        this.mItemClickListener = onItemListener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnItemClickListener<T> {

        void OnItemClick(int pos);
    }

    public class VHolder extends RecyclerView.ViewHolder {

        private final ImageView igv;
        private final ImageView ivDelete;

        public VHolder(@NonNull View itemView) {
            super(itemView);
            igv = itemView.findViewById(R.id.igv_check_photo);
            ivDelete = itemView.findViewById(R.id.iv_delete_photo);
        }
    }
}
