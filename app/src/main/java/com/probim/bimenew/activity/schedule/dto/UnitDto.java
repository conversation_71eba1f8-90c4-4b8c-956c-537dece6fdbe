package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class UnitDto {

    @SerializedName("Ret")
    private Integer ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private DataDTO data;

    public Integer getRet() {
        return ret;
    }

    public void setRet(Integer ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("UnitType")
        private Integer unitType;
        @SerializedName("UnitValue")
        private String unitValue;

        public Integer getUnitType() {
            return unitType;
        }

        public void setUnitType(Integer unitType) {
            this.unitType = unitType;
        }

        public String getUnitValue() {
            return unitValue;
        }

        public void setUnitValue(String unitValue) {
            this.unitValue = unitValue;
        }
    }
}
