package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.TestCommentsAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.model.Comments;
import com.probim.bimenew.model.Comments.DataBean;
import com.probim.bimenew.net.CallBack;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/5/17:38.
 */
public class TestCommentsActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.tv_right)
  TextView tvRight;
  @BindView(R.id.img_right)
  ImageView imgRight;
  @BindView(R.id.lv_comments)
  ListView lvComments;
  @BindView(R.id.iv_status)
  ImageView ivStatus;
  @BindView(R.id.tv_status)
  TextView tvStatus;
  @BindView(R.id.rv_status_layout)
  RelativeLayout rvStatusLayout;
  private IssueController issueController;
  private List<DataBean> dataBeanList = new ArrayList<>();
  private String organizeId;
  private TestCommentsAdapter testCommentsAdapter;
  private String issueId;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_test_comments);
    ButterKnife.bind(this);
    initView();
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {
    tvTitle.setText("评论");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("新增评论");
    //
    if (getIntent() != null) {

      issueId = getIntent().getStringExtra(CustomParam.IssueId);
      organizeId = getIntent().getStringExtra(CustomParam.OrganizeId);

      testCommentsAdapter = new TestCommentsAdapter(this, dataBeanList,
          organizeId);
      lvComments.setAdapter(testCommentsAdapter);

      issueController = new IssueController();
      GetCommentsSzie(issueId);

    }
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }


  /**
   * 获取评论列表
   */
  private void GetCommentsSzie(String id) {
    HashMap<String, String> params = new HashMap<>();
    params.put("IssueId", id);
    issueController.GetCommentsList(params, new CallBack<Comments>() {
      @Override
      public void onSuccess(Comments comments) {
        dataBeanList.addAll(comments.getData());
        testCommentsAdapter.notifyDataSetChanged();
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }


  @OnClick({R.id.iv_back, R.id.tv_right})
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.tv_right:
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.IssueId, issueId);
        Intent intent = new Intent(this, NewCommentsActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        break;
    }
  }
}
