package com.probim.bimenew.activity.fullview.adapter;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.model.ModelViewPointModel;

import java.util.ArrayList;
import java.util.List;

public class ModelPointAdapter extends RecyclerView.Adapter<ModelPointAdapter.ViewHolder> {

    private List<ModelViewPointModel> mDatas = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;
    private int selectPosition = -1;

    public ModelPointAdapter(List<ModelViewPointModel> mDatas) {
        this.mDatas = mDatas;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_fullview_model, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ModelViewPointModel dto = mDatas.get(position);
        if (position == getSelectPosition()) {
            holder.tvName.setTextColor(Color.parseColor("#007AFF"));
        } else {
            holder.tvName.setTextColor(Color.parseColor("#181818"));
        }
        holder.tvName.setText(dto.getName());

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                onItemClickListener.onClick(position, dto);
            }
        });

    }

    public int getSelectPosition() {
        return selectPosition;
    }

    public void setSelectPosition(int selectPosition) {
        this.selectPosition = selectPosition;

    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvName;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_label_name);
        }
    }
}
