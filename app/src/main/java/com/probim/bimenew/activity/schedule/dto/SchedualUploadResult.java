package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SchedualUploadResult {

    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        private String bf_guid;
        private String bf_md5;
        private String bf_path;
        private String bf_filename;
        @SerializedName("Progress_ProjectID")
        private String progress_ProjectID;
        @SerializedName("Progress_unittime")
        private String progress_unittime;
        @SerializedName("Progress_Userid")
        private String progress_Userid;

        public String getBf_guid() {
            return bf_guid;
        }

        public void setBf_guid(String bf_guid) {
            this.bf_guid = bf_guid;
        }

        public String getBf_md5() {
            return bf_md5;
        }

        public void setBf_md5(String bf_md5) {
            this.bf_md5 = bf_md5;
        }

        public String getBf_path() {
            return bf_path;
        }

        public void setBf_path(String bf_path) {
            this.bf_path = bf_path;
        }

        public String getBf_filename() {
            return bf_filename;
        }

        public void setBf_filename(String bf_filename) {
            this.bf_filename = bf_filename;
        }

        public String getProgress_ProjectID() {
            return progress_ProjectID;
        }

        public void setProgress_ProjectID(String progress_ProjectID) {
            this.progress_ProjectID = progress_ProjectID;
        }

        public String getProgress_unittime() {
            return progress_unittime;
        }

        public void setProgress_unittime(String progress_unittime) {
            this.progress_unittime = progress_unittime;
        }

        public String getProgress_Userid() {
            return progress_Userid;
        }

        public void setProgress_Userid(String progress_Userid) {
            this.progress_Userid = progress_Userid;
        }
    }
}
