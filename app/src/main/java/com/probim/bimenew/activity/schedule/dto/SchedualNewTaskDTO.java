package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualNewTaskDTO {

    @SerializedName("Progress_Name")
    private String progress_Name;
    @SerializedName("Progress_planstarttime")
    private String progress_planstarttime;
    @SerializedName("Progress_plannendtime")
    private String progress_plannendtime;
    @SerializedName("Progress_state")
    private String progress_state;
    @SerializedName("Progress_planratio")
    private String progress_planratio;
    @SerializedName("Progress_actualratio")
    private String progress_actualratio;
    @SerializedName("Progress_MobileTomorrowratio")
    private String progress_MobileTomorrowratio;
    @SerializedName("Progress_MobileAddratio")
    private String progress_MobileAddratio;
    @SerializedName("Progress_MobileImageID")
    private String progress_MobileImageID;
    @SerializedName("Progress_ProjectID")
    private String progress_ProjectID;
    @SerializedName("Progress_treeID")
    private String progress_treeID;
    @SerializedName("Progress_createuser")
    private String progress_createuser;
    @SerializedName("Progress_unittime")
    private String progress_unittime;
    @SerializedName("NoProgress_unittime")
    private String noProgress_unittime;
    @SerializedName("NoProgress_Username")
    private String noProgress_Username;
    @SerializedName("NoProgress_Name")
    private String noProgress_Name;
    @SerializedName("TaskType")
    private int taskType;
    private String Progress_MobileReason;

    public String getProgress_MobileReason() {
        return Progress_MobileReason;
    }

    public void setProgress_MobileReason(String progress_MobileReason) {
        Progress_MobileReason = progress_MobileReason;
    }

    public String getProgress_Name() {
        return progress_Name;
    }

    public void setProgress_Name(String progress_Name) {
        this.progress_Name = progress_Name;
    }

    public String getProgress_planstarttime() {
        return progress_planstarttime;
    }

    public void setProgress_planstarttime(String progress_planstarttime) {
        this.progress_planstarttime = progress_planstarttime;
    }

    public String getProgress_plannendtime() {
        return progress_plannendtime;
    }

    public void setProgress_plannendtime(String progress_plannendtime) {
        this.progress_plannendtime = progress_plannendtime;
    }

    public String getProgress_state() {
        return progress_state;
    }

    public void setProgress_state(String progress_state) {
        this.progress_state = progress_state;
    }

    public String getProgress_planratio() {
        return progress_planratio;
    }

    public void setProgress_planratio(String progress_planratio) {
        this.progress_planratio = progress_planratio;
    }

    public String getProgress_actualratio() {
        return progress_actualratio;
    }

    public void setProgress_actualratio(String progress_actualratio) {
        this.progress_actualratio = progress_actualratio;
    }

    public String getProgress_MobileTomorrowratio() {
        return progress_MobileTomorrowratio;
    }

    public void setProgress_MobileTomorrowratio(String progress_MobileTomorrowratio) {
        this.progress_MobileTomorrowratio = progress_MobileTomorrowratio;
    }

    public String getProgress_MobileAddratio() {
        return progress_MobileAddratio;
    }

    public void setProgress_MobileAddratio(String progress_MobileAddratio) {
        this.progress_MobileAddratio = progress_MobileAddratio;
    }

    public String getProgress_MobileImageID() {
        return progress_MobileImageID;
    }

    public void setProgress_MobileImageID(String progress_MobileImageID) {
        this.progress_MobileImageID = progress_MobileImageID;
    }

    public String getProgress_ProjectID() {
        return progress_ProjectID;
    }

    public void setProgress_ProjectID(String progress_ProjectID) {
        this.progress_ProjectID = progress_ProjectID;
    }

    public String getProgress_treeID() {
        return progress_treeID;
    }

    public void setProgress_treeID(String progress_treeID) {
        this.progress_treeID = progress_treeID;
    }

    public String getProgress_createuser() {
        return progress_createuser;
    }

    public void setProgress_createuser(String progress_createuser) {
        this.progress_createuser = progress_createuser;
    }

    public String getProgress_unittime() {
        return progress_unittime;
    }

    public void setProgress_unittime(String progress_unittime) {
        this.progress_unittime = progress_unittime;
    }

    public String getNoProgress_unittime() {
        return noProgress_unittime;
    }

    public void setNoProgress_unittime(String noProgress_unittime) {
        this.noProgress_unittime = noProgress_unittime;
    }

    public String getNoProgress_Username() {
        return noProgress_Username;
    }

    public void setNoProgress_Username(String noProgress_Username) {
        this.noProgress_Username = noProgress_Username;
    }

    public String getNoProgress_Name() {
        return noProgress_Name;
    }

    public void setNoProgress_Name(String noProgress_Name) {
        this.noProgress_Name = noProgress_Name;
    }

    public int getTaskType() {
        return taskType;
    }

    public void setTaskType(int taskType) {
        this.taskType = taskType;
    }
}
