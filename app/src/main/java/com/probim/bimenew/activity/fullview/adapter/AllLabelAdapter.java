package com.probim.bimenew.activity.fullview.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.fullview.dto.FullviewLableDTO;
import com.probim.bimenew.interfaces.IOnItemClickListener;

import java.util.ArrayList;
import java.util.List;

public class AllLabelAdapter extends RecyclerView.Adapter<AllLabelAdapter.ViewHolder> {

    private List<FullviewLableDTO.DataDTO> mDatas = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;

    public AllLabelAdapter(List<FullviewLableDTO.DataDTO> mDatas) {
        this.mDatas = mDatas;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_fullview_label, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        FullviewLableDTO.DataDTO dto = mDatas.get(position);
        holder.tvName.setText(dto.getLabelName());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onClick(position, dto);
            }
        });

    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvName;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_label_name);
        }
    }
}
