package com.probim.bimenew.activity;

import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.drawable.BitmapDrawable;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Environment;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.Photo4Adapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.BaseDto;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.ReviewCheckJson;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.ReviewRecordingService;
import com.probim.bimenew.utils.WheelView;
import com.probim.bimenew.utils.view.RecordAudioReview2Check;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Description :提交复检
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/2/20/17:16.
 */

public class ReviewCheck2Activity extends BaseActivity {
  @BindView(R.id.iv_back) ImageView ivBack;
  @BindView(R.id.tv_left) TextView tvLeft;
  @BindView(R.id.lin_back) LinearLayout linBack;
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.tv_right) TextView tvRight;
  @BindView(R.id.img_right) ImageView imgRight;
  @BindView(R.id.tv_review_result) TextView tvReviewResult;
  @BindView(R.id.lin_review_result) LinearLayout linReviewResult;
  @BindView(R.id.tv_review_time) TextView tvReviewTime;
  @BindView(R.id.lin_review_time) LinearLayout linReviewTime;
  @BindView(R.id.tv_back_photo) TextView tvBackPhoto;
  @BindView(R.id.rv_photo) RecyclerView rvPhoto;
  @BindView(R.id.tv_back_media) TextView tvBackMedia;
  @BindView(R.id.iv_play_meida) ImageView ivPlayMeida;
  @BindView(R.id.tv_intro) TextView tvIntro;
  @BindView(R.id.iv_select_photo) ImageView ivSelectPhoto;
  @BindView(R.id.iv_select_photo2) ImageView ivSelectPhoto2;
  @BindView(R.id.iv_record_media) ImageView ivRecordMedia;
  @BindView(R.id.lin_all) LinearLayout linAll;
  @BindView(R.id.rl_record_media) RelativeLayout rlRecordMedia;
  private List<String> wheelResultList = new ArrayList<>();
  private PopupWindow checkResultPop;
  private WheelView resultWheelView;
  private TextView resultrPoptvConfirm;
  private TextView tvSelecteResult;
  private String ResultSelected = "";
  private TimePickerView pvTime;
  private String dateStr;
  private Photo4Adapter photoAdapter;
  private String introResult = "";
  List<PhotoDto> photoDtoList = new ArrayList<>();
  private List<File> photoFileList = new ArrayList<>();
  private final int code_photo = 3000;
  private final int code_intro = 1000;
  private String projectID;
  private CheckController checkController;
  private List<String> upLoadPhotoList = new ArrayList<>();
  private List<String> upLoadAudioList = new ArrayList<>();
  public static boolean isRecordAudio = false;
  private String examineid;
  private MediaPlayer mediaPlayer;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_review_check);
    ButterKnife.bind(this);
    GetProjectDao();
    initView();
    initRecycleview();
  }

  @Override protected void loadData() {

  }

  @Override protected void initView() {
    if (getIntent() != null) {

      examineid = getIntent().getStringExtra(CustomParam.Examineid);
    }
    linReviewResult.setVisibility(View.GONE);
    tvLeft.setText("记录");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("提交");
    wheelResultList.add("整改");
    wheelResultList.add("复检");
    TimePicker();
    checkController = new CheckController();
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {
    //为 RecyclerView 设置布局管理器

    LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
    linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
    rvPhoto.setLayoutManager(linearLayoutManager);
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvPhoto.addItemDecoration(
        new DividerItemDecoration(this, DividerItemDecoration.HORIZONTAL));
    //动画
    rvPhoto.setItemAnimator(new DefaultItemAnimator());

    //初始化照片适配器
    photoAdapter = new Photo4Adapter(photoDtoList, this);

    photoAdapter.addItemListener(new Photo4Adapter.OnItemListener() {
      @Override public void OnItemDelete(int pos, Object o) {
        photoDtoList.remove(pos);
        photoFileList.remove(pos);
        photoAdapter.notifyDataSetChanged();
      }

      @Override public void OnItemClick(int pos, Object o) {
        if (photoDtoList.size() != 0) {
          Bundle bundle = new Bundle();
          bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
          bundle.putInt(CustomParam.PhotoPosition, pos);
          Intent intent = new Intent(ReviewCheck2Activity.this, PhotoViewActivity.class);
          intent.putExtras(bundle);
          startActivity(intent);
        }
      }
    });
    rvPhoto.setAdapter(photoAdapter);
  }

  @Override protected void initRefresh() {

  }

  @OnClick({
      R.id.lin_back, R.id.lin_review_result, R.id.lin_review_time, R.id.iv_select_photo,
      R.id.iv_select_photo2, R.id.iv_record_media, R.id.tv_right, R.id.rl_edt_text,
      R.id.rl_record_media, R.id.iv_play_meida
  }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.lin_review_result:
        showCheckResultPop();
        break;
      case R.id.lin_review_time:
        pvTime.show();
        break;
      case R.id.iv_select_photo:
      case R.id.iv_select_photo2:
        getPhoto();
        break;
      case R.id.iv_play_meida:
        playMedia();
        break;
      case R.id.iv_record_media:

        File folder = new File(Environment.getExternalStorageDirectory() + "/reviewCheck");
        if (!folder.exists()) {
          //folder /SoundRecorder doesn't exist, create the folder
          folder.mkdir();
        }

        RecordAudioReview2Check recordAudioAlertDialog =
            new RecordAudioReview2Check(ReviewCheck2Activity.this,
                new RecordAudioReview2Check.OnListener() {
                  @Override public void OnRecordDone() {
                    tvBackMedia.setVisibility(View.GONE);
                    ivPlayMeida.setVisibility(View.VISIBLE);
                  }
                });
        recordAudioAlertDialog.builder().show();
        break;
      case R.id.tv_right:
        //无照片
        if (photoFileList.isEmpty()) {

          reviewCheck();
        } else {
          //有照片
          UploadPhoto();
        }
        break;
      case R.id.rl_edt_text:

        if (!TextUtils.isEmpty(introResult)) {
          Bundle bundle = new Bundle();
          bundle.putString(CustomParam.IntroStr, introResult);
          Intent intent1 = new Intent(this, EdtIntroActivity.class);
          intent1.putExtras(bundle);
          startActivityForResult(intent1, code_intro);
        } else {
          startActivityForResult(new Intent(ReviewCheck2Activity.this, EdtIntroActivity.class),
              code_intro);
        }
        break;
      default:
        break;
    }
  }

  /**
   * 请选择 检查结果  和   整改时间
   */
  private void showCheckResultPop() {

    View view = LayoutInflater.from(ReviewCheck2Activity.this)
        .inflate(R.layout.popwindow_new_check_result, null);
    checkResultPop = new PopupWindow(ReviewCheck2Activity.this);
    checkResultPop.setContentView(view);
    checkResultPop.setFocusable(true);
    checkResultPop.setOutsideTouchable(true);
    checkResultPop.setBackgroundDrawable(new BitmapDrawable());
    checkResultPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
    checkResultPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
    setBackgroundAlpha(0.5f);
    resultWheelView = view.findViewById(R.id.result_wheel_view);
    resultWheelView.setOffset(1);

    resultrPoptvConfirm = view.findViewById(R.id.tv_confirm);
    tvSelecteResult = view.findViewById(R.id.tvSelecteResult);
    checkResultPop.setAnimationStyle(R.style.picker_view_slide_anim);
    checkResultPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
      @Override
      public void onDismiss() {
        setBackgroundAlpha(1.0f);
      }
    });

    initResultWheelView();
    resultWheelView.setItems(wheelResultList);
    checkResultPop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
  }

  /**
   * 初始化检查结果
   */
  private void initResultWheelView() {

    ResultSelected = wheelResultList.get(0);
    //StatusIdSelected = wheelStatusIDList.get(0);

    resultWheelView.setOnWheelViewListener(new WheelView.OnWheelViewListener() {
      @Override
      public void onSelected(int selectedIndex, String item) {
        ResultSelected = item;
      }
    });

    resultrPoptvConfirm.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        boolean isResultSelected = true;
        tvSelecteResult.setText(ResultSelected);
        tvReviewResult.setText(ResultSelected);
        tvReviewResult.setTextColor(getResources().getColor(R.color.black));
        checkResultPop.dismiss();
      }
    });
  }

  /**
   * 设置popwindow默认背景变灰
   */
  public void setBackgroundAlpha(float bgAlpha) {
    WindowManager.LayoutParams lp = getWindow().getAttributes();
    lp.alpha = bgAlpha;
    getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
    getWindow().setAttributes(lp);
  }

  /**
   * 时间选择器
   */
  private void TimePicker() {

    Calendar selectedDate = Calendar.getInstance();//系统当前时间

    //取消按钮文字
    //确定按钮文字颜色
    pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
      @Override
      public void onTimeSelect(Date date, View v) {

        dateStr = getDay(date);
        tvReviewTime.setText(dateStr);
        tvReviewTime.setTextColor(getResources().getColor(R.color.black));
      }
    }).setCancelText("清空")
        .setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
        .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
        .setType(new boolean[] { true, true, true, false, false, false })
        .setContentTextSize(18)
        .setLabel("", "", "", "", "", "")
        .isCenterLabel(false)
        .setLineSpacingMultiplier(3.0f)
        .setDividerColor(R.color.divider_color)
        .setDecorView(null)
        .setTitleText("请选择复检时间")
        .setDate(selectedDate)
        .setLayoutRes(R.layout.pickerview_custom_time,
            new CustomListener() {
              @Override
              public void customLayout(View v) {

                TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                tv_clear.setOnClickListener(new View.OnClickListener() {
                  @Override
                  public void onClick(View view) {
                    tvReviewTime.setText("请选择复检时间");
                    pvTime.dismiss();
                  }
                });

                TextView tvConmit = (TextView) v.findViewById(R.id.tv_finish);
                tvConmit.setOnClickListener(new View.OnClickListener() {
                  @Override
                  public void onClick(View view) {
                    pvTime.returnData();
                    pvTime.dismiss();
                  }
                });
              }
            })
        .build();
  }

  private String getDay(Date date) {//可根据需要自行截取数据显示
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
    return format.format(date);
  }

  /**
   * 选取照片
   */
  private void getPhoto() {
    Matisse.from(this)
        .choose(MimeType.ofAll())
        .countable(true)
        .capture(true)
        .captureStrategy(
            new CaptureStrategy(true, getPackageName()))
        .maxSelectable(9)
        .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
        .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
        .thumbnailScale(0.85f)
        .imageEngine(new GlideEngine())
        .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
        .forResult(code_photo);
  }

  @Override protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode != RESULT_OK) {
      return;
    }
    switch (requestCode) {

      case code_intro:
        introResult = data.getExtras()
            .getString(CustomParam.StartForResult);
        tvIntro.setText(introResult);
        tvIntro.setTextColor(getResources().getColor(R.color.black));
        break;

      case code_photo:

        for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {
          rvPhoto.setVisibility(View.VISIBLE);
          tvBackPhoto.setVisibility(View.GONE);
          File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
          File fileCompress = CompressHelper.getDefault(getApplicationContext())
              .compressToFile(fileOrigin);

          photoDtoList.add(new PhotoDto(fileCompress.getAbsolutePath()));

          photoFileList.add(fileCompress);
        }
        photoAdapter.notifyDataSetChanged();

        break;

      default:

        break;
    }
  }

  /**
   * 上传图片
   */
  private void UploadPhoto() {

    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectID);
    params.put("CreateUserID", Hawk.get(CustomParam.UserId));
    params.put("CreateUserName", Hawk.get(CustomParam.RealName));
    params.put("FileType", "Examinedata");
    checkController.UploadPhoto(photoFileList, params, new CallBack<String>() {
      @Override
      public void onSuccess(String s) {
        if (!TextUtils.isEmpty(s)) {

          if (photoFileList.size() > 1) {

            String[] fileStr = s.split(",");
            upLoadPhotoList.addAll(Arrays.asList(fileStr));
          } else {

            upLoadPhotoList.add(s);
          }
        }

        if (isRecordAudio) {

          List<File> audioFileList = new ArrayList<>();
          audioFileList.add(new File(ReviewRecordingService.RecordFilePath));
          UploadAudio(audioFileList);
        } else {

          reviewCheck();
        }
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 上传音频
   */
  private void UploadAudio(List<File> audioFileList) {
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectID);
    params.put("CreateUserID", Hawk.get(CustomParam.UserId));
    params.put("CreateUserName", Hawk.get(CustomParam.RealName));
    params.put("FileType", "Examinedata");
    checkController.UploadAudio(audioFileList, params, new CallBack<String>() {
      @Override public void onSuccess(String s) {
        upLoadAudioList.add(s);
        reviewCheck();
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 复检
   */
  private void reviewCheck() {

    String uuid = UUID.randomUUID().toString().replace("-", "");

    ReviewCheckJson jsonDto = new ReviewCheckJson();
    ReviewCheckJson.ExamineRectificationBean examineRectificationBean =
        new ReviewCheckJson.ExamineRectificationBean();
    List<ReviewCheckJson.ExamineAttachmentBean> examineAttachmentBeanList = new ArrayList<>();

    examineRectificationBean.setExamineID(examineid);
    examineRectificationBean.setRectificationID(Hawk.get(CustomParam.UserId));
    examineRectificationBean.setIsDel("0");
    examineRectificationBean.setRectificationRemark(introResult);
    examineRectificationBean.setCreateDate(getSystemDate());
    examineRectificationBean.setRectificationCheckDate(dateStr);
    examineRectificationBean.setRectificationCheckResult(ResultSelected);
    //1 检查人  2  负责人   ????
    examineRectificationBean.setRectificationOperateFlag("2");
    examineRectificationBean.setRectificationOperator(Hawk.get(CustomParam.RealName));

    for (String photoUrl : upLoadPhotoList) {
      ReviewCheckJson.ExamineAttachmentBean examineAttachmentBean =
          new ReviewCheckJson.ExamineAttachmentBean();
      examineAttachmentBean.setAttachmentName("android_image" + new Random().nextInt());
      examineAttachmentBean.setAttachmentType(".png");
      examineAttachmentBean.setAttachmentUrl("");
      examineAttachmentBean.setCheckFlag("1");
      examineAttachmentBean.setExamineAttachmentID(photoUrl);
      examineAttachmentBean.setExamineID(uuid);
      examineAttachmentBean.setIsDel("0");
      examineAttachmentBean.setUploadDate(getSystemDate());
      examineAttachmentBeanList.add(examineAttachmentBean);
    }

    if (!upLoadAudioList.isEmpty()) {
      for (String audioUrl : upLoadAudioList) {
        ReviewCheckJson.ExamineAttachmentBean examineAttachmentBean =
            new ReviewCheckJson.ExamineAttachmentBean();
        examineAttachmentBean.setAttachmentName("android_audio" + new Random().nextInt());
        examineAttachmentBean.setAttachmentType(".mp3");
        examineAttachmentBean.setAttachmentUrl("");
        examineAttachmentBean.setCheckFlag("1");
        examineAttachmentBean.setExamineAttachmentID(audioUrl);
        examineAttachmentBean.setExamineID(uuid);
        examineAttachmentBean.setIsDel("0");
        examineAttachmentBean.setUploadDate(getSystemDate());
        examineAttachmentBeanList.add(examineAttachmentBean);
      }
    }

    jsonDto.setExamineRectification(examineRectificationBean);
    jsonDto.setExamineAttachment(examineAttachmentBeanList);
    checkController.reviewCheck(JsonHelper.toJson(jsonDto), new CallBack<BaseDto>() {
      @Override public void onSuccess(BaseDto baseDto) {
        if (0 == baseDto.getErrorCode()) {

          showMsg("复检成功");
          finish();
        } else {

          showMsg("复检失败");
        }
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      projectID = bean.getProjectID();
    }
  }

  /**
   * 获取系统当前时间
   */
  private String getSystemDate() {
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd  HH:mm:ss");
    return format.format(new Date(System.currentTimeMillis()));
  }

  /**
   * 播放音频
   */
  private void playMedia() {
    Glide.with(ReviewCheck2Activity.this).load(R.drawable.recordinganimation).into(ivPlayMeida);

    mediaPlayer = new MediaPlayer();
    try {
      mediaPlayer.setDataSource(ReviewRecordingService.RecordFilePath);

      mediaPlayer.prepare();

      mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
        @Override
        public void onPrepared(MediaPlayer mp) {
          mediaPlayer.start();
        }
      });
      mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
        @Override public void onCompletion(MediaPlayer mediaPlayer) {
          stopPlayMedia();
        }
      });
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /**
   * 停止播放
   */
  private void stopPlayMedia() {

    mediaPlayer.stop();
    mediaPlayer.reset();
    mediaPlayer.release();
    mediaPlayer = null;
    Glide.with(ReviewCheck2Activity.this).load(R.mipmap.ic_recoarding_play).into(ivPlayMeida);
  }

  @Override protected void onDestroy() {
    super.onDestroy();
    isRecordAudio = false;
  }
}
