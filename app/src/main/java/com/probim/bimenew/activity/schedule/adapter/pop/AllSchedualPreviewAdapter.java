package com.probim.bimenew.activity.schedule.adapter.pop;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class AllSchedualPreviewAdapter extends RecyclerView.Adapter<AllSchedualPreviewAdapter.ViewHolder> {

    private List<SchedualPreviewListDTO.DataDTO> dtoList = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;

    public AllSchedualPreviewAdapter(List<SchedualPreviewListDTO.DataDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_schedual_preview, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SchedualPreviewListDTO.DataDTO dto = dtoList.get(position);
        holder.tvProjectName.setText(dto.getProgress_Name());
        holder.tvCreateUser.setText(dto.getProgress_createuser());
        holder.tvUnitTime.setText(dto.getProgress_unittime().split(" ")[0]);
        holder.tvSubmit.setText(TextUtils.isEmpty(dto.getProgress_actualendtime()) ? " " : dto.getProgress_actualendtime().split(" ")[0]);
        switch (dto.getAuditStatus()) {
            case 0:
                holder.tvState.setText("待提交");
                holder.tvState.setBackground(BaseApp.getContext().getDrawable(R.drawable.bg_schedual_state_daitijiao));
                break;
            case 1:
                holder.tvState.setText("待审核");
                holder.tvState.setBackground(BaseApp.getContext().getDrawable(R.drawable.bg_schedual_state_daishenhe));
                break;
            case 2:
                holder.tvState.setText("已审核");
                holder.tvState.setBackground(BaseApp.getContext().getDrawable(R.drawable.bg_schedual_state_yishenhe));
                break;
            case 3:
                holder.tvState.setText("驳回待提交");
                holder.tvState.setBackground(BaseApp.getContext().getDrawable(R.drawable.bg_schedual_state_bohui));
                break;
            default:
                break;
        }

        holder.rlContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
        holder.btnDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    onItemClickListener.OnDelete(holder.getAdapterPosition(), dto);
                }

            }
        });
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.iv_photo)
        ImageView ivPhoto;
        @BindView(R.id.tv_createUser)
        TextView tvCreateUser;
        @BindView(R.id.tv_unittime)
        TextView tvUnitTime;
        @BindView(R.id.tv_projectName)
        TextView tvProjectName;
        @BindView(R.id.tv_state)
        TextView tvState;
        @BindView(R.id.rl_container)
        RelativeLayout rlContainer;
        @BindView(R.id.btnDelete)
        Button btnDelete;
        @BindView(R.id.tv_submit)
        TextView tvSubmit;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
