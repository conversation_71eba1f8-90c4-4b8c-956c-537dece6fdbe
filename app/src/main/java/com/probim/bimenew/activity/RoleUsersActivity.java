package com.probim.bimenew.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.adapter.RoleUserAdapter;
import com.probim.bimenew.adapter.RoleUserInnerAdapter;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.controller.UserController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.UserDto;
import com.probim.bimenew.event.EventBean;
import com.probim.bimenew.event.EventBusUtils;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.RoleUsersDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description : 角色分类人员
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2021/1/4/16:09.
 */
public class RoleUsersActivity extends BaseActivity implements View.OnClickListener {

    private final List<RoleUsersDto.DataDTO.ListDTO> listDTOS = new ArrayList<>();
    private String organizeId;
    private RoleUserAdapter outAdapter;
    private RecyclerView rvRoleUser;
    private boolean isOnItem;
    private int outPos;
    private RelativeLayout rlStatus;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_roles_user);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        initRecycleview();
        loadData();
        Logger.t("RoleUsersActivity").e(getTaskId() + "");

    }

    @Override
    protected void loadData() {
        getProjectDao();

        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", organizeId);
        UserController userController = new UserController();
        userController.getRoleUsers(params, new CallBack<RoleUsersDto>() {
            @Override
            public void onSuccess(RoleUsersDto roleUsersDto) {

                if (roleUsersDto.getRet() == 1) {
                    listDTOS.addAll(roleUsersDto.getData().getList());
                    outAdapter.notifyDataSetChanged();
                }

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

    }

    @Override
    protected void initView() {
        TextView tvTitle = findView(R.id.tv_black_title);
        tvTitle.setText("人员选择");
        TextView tvRight = findView(R.id.tv_right, this);
        tvRight.setText("完成");
        LinearLayout linBack = findView(R.id.lin_back, this);


    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        rlStatus = findView(R.id.rv_status_layout);
        rvRoleUser = findView(R.id.rv_role_user);
        setOutAdapter();


    }

    /**
     * 设置外部适配器
     */

    private void setOutAdapter() {
        isOnItem = false;
        outAdapter = new RoleUserAdapter(listDTOS, this);
        VerticalNoItemRecyclerView.initialize(rvRoleUser).setAdapter(outAdapter);
        outAdapter.setOnItemClickListener(new RoleUserAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(int pos, RoleUsersDto.DataDTO.ListDTO dto) {

                //内部数据
                setInnerAdapter(pos);

                outPos = pos;


            }

            @Override
            public void onGroupClick(int pos, RoleUsersDto.DataDTO.ListDTO dto) {

                // 选中 角色


            }
        });


    }

    /**
     * 设置内部适配器
     */
    private void setInnerAdapter(int outPos) {

        isOnItem = true;
        for (RoleUsersDto.DataDTO.ListDTO.UsersDTO dto : listDTOS.get(outPos).getUsers()) {

            dto.setSelected(false);
        }

        RoleUserInnerAdapter innerAdapter = new RoleUserInnerAdapter(this, listDTOS.get(outPos).getUsers());
        VerticalRecyclerView.initialize(rvRoleUser).setAdapter(innerAdapter);
        innerAdapter.setOnItemClickListener(new RoleUserInnerAdapter.OnItemClickListener() {
            @Override
            public void OnItemClick(int pos, Object o) {

                showMsg(listDTOS.get(outPos).getUsers().get(pos).getRealName());

            }
        });

    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.lin_back:

                backToList();
                break;

            case R.id.tv_right:

                EventBusUtils.sendEvent(new EventBean(ApiConstant.CODE_TYPE_USER, getSelectedList()));
                ActivityCompat.finishAffinity(RoleUsersActivity.this);

                break;
            default:
                break;
        }
    }

    /**
     * 获取选中数据
     */
    private List<UserDto> getSelectedList() {

        List<UserDto> isSelectedAllList = new ArrayList<>();
        // 2----->>> 第二种为单体选择
        if (isOnItem && outPos != -1) {
            for (int i = 0; i < listDTOS.get(outPos).getUsers().size(); i++) {
                RoleUsersDto.DataDTO.ListDTO.UsersDTO usersDTO = listDTOS.get(outPos).getUsers().get(i);
                if (usersDTO.isSelected()) {
                    UserDto dto = new UserDto(usersDTO.getUserId(), usersDTO.getRealName());
                    isSelectedAllList.add(dto);

                }

            }

        } else {
            // 1 ---->>> 第一种为群组选中
            if (listDTOS.get(outPos).isGroupSelected()) {
                for (int i = 0; i < listDTOS.get(outPos).getUsers().size(); i++) {
                    RoleUsersDto.DataDTO.ListDTO.UsersDTO usersDTO = listDTOS.get(outPos).getUsers().get(i);
                    UserDto dto = new UserDto(usersDTO.getUserId(), usersDTO.getRealName());
                    isSelectedAllList.add(dto);

                }

            }


        }


        return isSelectedAllList;


    }

    /**
     * back to list
     */

    private void backToList() {

        if (isOnItem) {

            setOutAdapter();

        } else {

            finish();
        }


    }

}