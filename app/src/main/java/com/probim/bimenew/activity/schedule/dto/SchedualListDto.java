package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class SchedualListDto implements Serializable {

    @SerializedName("Ret")
    private Integer ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public Integer getRet() {
        return ret;
    }

    public void setRet(Integer ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO implements Serializable {
        @SerializedName("UID")
        private String uID;
        @SerializedName("Name")
        private String name;
        private List<ChildrenDTO> children;

        public String getUID() {
            return uID;
        }

        public void setUID(String uID) {
            this.uID = uID;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<ChildrenDTO> getChildren() {
            return children;
        }

        public void setChildren(List<ChildrenDTO> children) {
            this.children = children;
        }

        public static class ChildrenDTO implements Serializable {
            @SerializedName("ActualFinish")
            private Object actualFinish;
            @SerializedName("ProjectUID")
            private String projectUID;
            @SerializedName("Notes")
            private Object notes;
            @SerializedName("PercentComplete")
            private float percentComplete;
            @SerializedName("Duration")
            private Integer duration;
            @SerializedName("ParentTaskUID")
            private String parentTaskUID;
            @SerializedName("Finish")
            private String finish;
            @SerializedName("WBS")
            private String wBS;
            @SerializedName("Name")
            private String name;
            @SerializedName("Work")
            private Integer work;
            @SerializedName("Priority")
            private Integer priority;
            @SerializedName("ID")
            private Integer iD;
            @SerializedName("UID")
            private String uID;
            @SerializedName("Department")
            private Object department;
            @SerializedName("ConstraintDate")
            private Object constraintDate;
            @SerializedName("Start")
            private String start;
            @SerializedName("FixedDate")
            private Integer fixedDate;
            @SerializedName("Weight")
            private Integer weight;
            @SerializedName("Principal")
            private Object principal;
            @SerializedName("PredecessorLink")
            private List<?> predecessorLink;
            @SerializedName("ConstraintType")
            private Integer constraintType;
            @SerializedName("Critical2")
            private Object critical2;
            @SerializedName("ActualDuration")
            private Integer actualDuration;
            private List<ChildrenDTO> children;
            @SerializedName("Summary")
            private Integer summary;
            @SerializedName("Critical")
            private Integer critical;
            @SerializedName("Milestone")
            private Integer milestone;
            @SerializedName("Manual")
            private Integer manual;
            @SerializedName("ActualStart")
            private String actualStart;
            private boolean isSelected;

            public boolean isSelected() {
                return isSelected;
            }

            public void setSelected(boolean selected) {
                isSelected = selected;
            }

            public Object getActualFinish() {
                return actualFinish;
            }

            public void setActualFinish(Object actualFinish) {
                this.actualFinish = actualFinish;
            }

            public String getProjectUID() {
                return projectUID;
            }

            public void setProjectUID(String projectUID) {
                this.projectUID = projectUID;
            }

            public Object getNotes() {
                return notes;
            }

            public void setNotes(Object notes) {
                this.notes = notes;
            }

            public float getPercentComplete() {
                return percentComplete;
            }

            public void setPercentComplete(float percentComplete) {
                this.percentComplete = percentComplete;
            }

            public Integer getDuration() {
                return duration;
            }

            public void setDuration(Integer duration) {
                this.duration = duration;
            }

            public String getParentTaskUID() {
                return parentTaskUID;
            }

            public void setParentTaskUID(String parentTaskUID) {
                this.parentTaskUID = parentTaskUID;
            }

            public String getFinish() {
                return finish;
            }

            public void setFinish(String finish) {
                this.finish = finish;
            }

            public String getWBS() {
                return wBS;
            }

            public void setWBS(String wBS) {
                this.wBS = wBS;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public Integer getWork() {
                return work;
            }

            public void setWork(Integer work) {
                this.work = work;
            }

            public Integer getPriority() {
                return priority;
            }

            public void setPriority(Integer priority) {
                this.priority = priority;
            }

            public Integer getID() {
                return iD;
            }

            public void setID(Integer iD) {
                this.iD = iD;
            }

            public String getUID() {
                return uID;
            }

            public void setUID(String uID) {
                this.uID = uID;
            }

            public Object getDepartment() {
                return department;
            }

            public void setDepartment(Object department) {
                this.department = department;
            }

            public Object getConstraintDate() {
                return constraintDate;
            }

            public void setConstraintDate(Object constraintDate) {
                this.constraintDate = constraintDate;
            }

            public String getStart() {
                return start;
            }

            public void setStart(String start) {
                this.start = start;
            }

            public Integer getFixedDate() {
                return fixedDate;
            }

            public void setFixedDate(Integer fixedDate) {
                this.fixedDate = fixedDate;
            }

            public Integer getWeight() {
                return weight;
            }

            public void setWeight(Integer weight) {
                this.weight = weight;
            }

            public Object getPrincipal() {
                return principal;
            }

            public void setPrincipal(Object principal) {
                this.principal = principal;
            }

            public List<?> getPredecessorLink() {
                return predecessorLink;
            }

            public void setPredecessorLink(List<?> predecessorLink) {
                this.predecessorLink = predecessorLink;
            }

            public Integer getConstraintType() {
                return constraintType;
            }

            public void setConstraintType(Integer constraintType) {
                this.constraintType = constraintType;
            }

            public Object getCritical2() {
                return critical2;
            }

            public void setCritical2(Object critical2) {
                this.critical2 = critical2;
            }

            public Integer getActualDuration() {
                return actualDuration;
            }

            public void setActualDuration(Integer actualDuration) {
                this.actualDuration = actualDuration;
            }

            public List<ChildrenDTO> getChildren() {
                return children;
            }

            public void setChildren(List<ChildrenDTO> children) {
                this.children = children;
            }

            public Integer getSummary() {
                return summary;
            }

            public void setSummary(Integer summary) {
                this.summary = summary;
            }

            public Integer getCritical() {
                return critical;
            }

            public void setCritical(Integer critical) {
                this.critical = critical;
            }

            public Integer getMilestone() {
                return milestone;
            }

            public void setMilestone(Integer milestone) {
                this.milestone = milestone;
            }

            public Integer getManual() {
                return manual;
            }

            public void setManual(Integer manual) {
                this.manual = manual;
            }

            public String getActualStart() {
                return actualStart;
            }

            public void setActualStart(String actualStart) {
                this.actualStart = actualStart;
            }

        }
    }
}
