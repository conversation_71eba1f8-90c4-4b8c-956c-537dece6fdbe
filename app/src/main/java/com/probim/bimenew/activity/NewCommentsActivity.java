package com.probim.bimenew.activity;

import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

import com.google.gson.reflect.TypeToken;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.activity.check.HorizontalRecyclerView;
import com.probim.bimenew.activity.check.UserJoinerAdapter;
import com.probim.bimenew.activity.schedule.adapter.SpinnerAdapter;
import com.probim.bimenew.activity.schedule.adapter.SpinnerCommentAdapter;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.PhotoAdapter;
import com.probim.bimenew.adapter.PhotoAdapter.OnItemDeleteListener;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AddNewCommente;
import com.probim.bimenew.result.IssueDetailResult;
import com.probim.bimenew.result.UploadHideResult;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.view.MyGridView;
import com.yyx.beautifylib.model.BLPickerParam;
import com.yyx.beautifylib.model.BLResultParam;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;
import java.io.File;
import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * Description :新增评论
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/6/13:42.
 */
public class NewCommentsActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.tv_right)
  TextView tvRight;
  @BindView(R.id.img_right)
  ImageView imgRight;
  @BindView(R.id.edt_text)
  EditText edtText;
  @BindView(R.id.grid_photo)
  MyGridView gridPhoto;
  private PhotoAdapter photoAdapter;
  private final int code_photo = 3000;
  List<PhotoDto> photoDtoList = new ArrayList<>();
  private List<File> photoFileList = new ArrayList<>();
  private IssueController issueController;
  private String organizeId;
  private String commnetsStr;
  private String issueId;
  private String talkId = "";
  private List<IssueDetailResult.DataBean.JoinersBean> joinersBeanList = new ArrayList<>();
  private Spinner spinner;
  private String userId;
  private Set<IssueDetailResult.DataBean.JoinersBean> selectUserIds =new HashSet<>();
  private IssueDetailResult.DataBean.JoinersBean tempData;
  private List<IssueDetailResult.DataBean.JoinersBean> beans;


  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_new_comments);
    ButterKnife.bind(this);
    initView();
    loadData();
  }

  @Override
  protected void loadData() {
    issueController = new IssueController();
    GetProjectDao();
  }

  @Override
  protected void initView() {
    RecyclerView rvUsers = findView(R.id.rv_users);
    tvTitle.setText("新建评论");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("完成");
    if (getIntent() != null) {
      issueId = getIntent().getStringExtra(CustomParam.IssueId);
      talkId = getIntent().getStringExtra(CustomParam.CommentsTalkId);
      String joins = getIntent().getStringExtra(CustomParam.JoinUser);
      tempData = new IssueDetailResult.DataBean.JoinersBean();
      tempData.setRealName("无");
      tempData.setOl_guid("0");
      joinersBeanList.add(tempData);
      Type listType = new TypeToken<List<IssueDetailResult.DataBean.JoinersBean>>(){}.getType();
      joinersBeanList.addAll(JsonHelper.fromJson(joins,listType));
    }
    edtText.setFocusable(true);
    edtText.setFocusableInTouchMode(true);
    edtText.requestFocus();
    //显示软键盘
    getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
    //初始化照片适配器
    photoAdapter = new PhotoAdapter(photoDtoList, this);
    photoAdapter.addItemDeleteListener(new OnItemDeleteListener() {
      @Override
      public void OnDelete(int pos, Object o) {
        photoDtoList.remove(pos);
        photoFileList.remove(pos);
        photoAdapter.notifyDataSetChanged();
      }
    });
    gridPhoto.setAdapter(photoAdapter);
    gridPhoto.setOnItemClickListener(new OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        if (photoDtoList.size() != 0) {
          Bundle bundle = new Bundle();
          bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
          bundle.putInt(CustomParam.PhotoPosition, i);
          Intent intent = new Intent(NewCommentsActivity.this, PhotoViewActivity.class);
          intent.putExtras(bundle);
          startActivity(intent);
        }
      }
    });
    spinner = (Spinner) findViewById(R.id.spinner_show_people);
    // 创建适配器对象并设置数据源
   SpinnerCommentAdapter spinnerAdapter = new SpinnerCommentAdapter(joinersBeanList);
    // 将适配器设置到 Spinner
    spinner.setAdapter(spinnerAdapter);
    spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
      @Override
      public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
        if (joinersBeanList.get(i).getRealName().equals("无")){
          selectUserIds.clear();
        }else {
          selectUserIds.add(joinersBeanList.get(i));
          selectUserIds.remove(tempData);
        }
        beans = new ArrayList<>(selectUserIds);
        UserJoinerAdapter userJoinerAdapter = new UserJoinerAdapter(NewCommentsActivity.this,beans);
        userJoinerAdapter.addRecycleItemListener(new UserJoinerAdapter.OnRecycleItemListener() {
          @Override
          public void OnRecycleItemClick(int pos, Object o) {
            selectUserIds.remove(beans.get(pos));
            beans.remove(pos);
            userJoinerAdapter.notifyDataSetChanged();
          }
        });
        HorizontalRecyclerView.initialize(rvUsers).setAdapter(userJoinerAdapter);
        Logger.t("dsdssd").e(JsonHelper.toJson(beans));
      }

      @Override
      public void onNothingSelected(AdapterView<?> adapterView) {

      }
    });
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({ R.id.tv_left, R.id.lin_back, R.id.tv_right, R.id.tv_select_photo })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.tv_left:
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        commnetsStr = edtText.getText().toString().trim();
        //只上传评论文字
        //newComments();
        if (TextUtils.isEmpty(commnetsStr)) {
          showMsg("评论文字不能为空");
          return;
        }
        addComments();

        break;
      case R.id.tv_select_photo:
        getPhoto();
//        gotoPickPhoto();
        break;
    }
  }

  /**
   * 选取照片
   */
  private void getPhoto() {
    Matisse.from(this)
        .choose(MimeType.ofAll(), false)
        .countable(true)
        .capture(true)
        .captureStrategy(
            new CaptureStrategy(true, getPackageName()))
        .maxSelectable(9)
        .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
        .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
        .thumbnailScale(0.85f)
        .imageEngine(new GlideEngine())
        .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
        .forResult(code_photo);
  }

  /**
   * 选取照片并编辑
   */
  private void gotoPickPhoto() {

    BLPickerParam.startActivity(NewCommentsActivity.this);
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);

    if (resultCode != RESULT_OK) {
      return;
    }
    switch (requestCode) {

      case code_photo:

        for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {

          File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
          File fileCompress = CompressHelper.getDefault(getApplicationContext())
              .compressToFile(fileOrigin);

          photoDtoList.add(new PhotoDto(fileCompress.getAbsolutePath()));

          photoFileList.add(fileCompress);
        }
        if (!photoFileList.isEmpty()) {

          UploadPhoto();
        }
        break;

      case BLPickerParam.REQUEST_CODE_PHOTO_PICKER:

        BLResultParam blResultParam = data.getParcelableExtra(BLResultParam.KEY);
        for (int i = 0; i < blResultParam.getImageList().size(); i++) {

          File fileOrigin = FileUtil.getFileByPath(blResultParam.getImageList().get(i));
          File fileCompress = CompressHelper.getDefault(getApplicationContext())
              .compressToFile(fileOrigin);

          photoDtoList.add(new PhotoDto(fileCompress.getAbsolutePath()));

          photoFileList.add(fileCompress);
        }

        if (!photoFileList.isEmpty()) {

          UploadPhoto();
        }

        break;

      default:
        break;
    }
  }

  /**
   * 上传图片
   */
  private void UploadPhoto() {
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", organizeId);
    params.put("CreateUserID", Hawk.get(CustomParam.UserId));
    params.put("CreateUserName", Hawk.get(CustomParam.RealName));
    params.put("FileType", "Issue");

    issueController.uploadIssueCommentsPhoto(photoFileList, params,
        new CallBack<UploadHideResult>() {

          @Override public void onSuccess(UploadHideResult uploadHideResult) {
            addCommentsWithPhoto(uploadHideResult.getFileId(), uploadHideResult.getFileName());
          }

          @Override
          public void onFail(String erroMsg) {

          }
        });
  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      organizeId = bean.getProjectID();
      String bimProjectId = bean.getBimProjectId();
    }
  }

  /**
   * 新增图片评论
   */
  private void addCommentsWithPhoto(String id, String name) {

    HashMap<String, String> params = new HashMap<>();
    params.put("IssueId", issueId);
    params.put("Token", Hawk.get(CustomParam.Token));
    params.put("Name", name);
    params.put("Id", id);

    issueController.addCommentsWithPhoto(params, new CallBack<AddNewCommente>() {

      @Override public void onSuccess(AddNewCommente addNewCommente) {
        if (addNewCommente.getRet() == 1) {
          showMsg("提交评论成功");
          finish();
        } else {
          showMsg("提交评论失败");
        }
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  /***
   * 提交新评论
   *
   */
  private void addComments() {
    HashMap<String, String> params = new HashMap<>();
    for (IssueDetailResult.DataBean.JoinersBean bean : beans) {
      userId = bean.getId() +",";
    }
    params.put("AtUserIds",userId);
    params.put("Token", Hawk.get(CustomParam.Token));
    params.put("RelationIssueId", issueId);
    params.put("Title", commnetsStr);
    params.put("organizeId", organizeId);
    issueController.addComments(params, new CallBack<AddNewCommente>() {

      @Override public void onSuccess(AddNewCommente addNewCommente) {
        if (addNewCommente.getRet() == 1) {
          showMsg("提交评论成功");
          finish();
        } else {
          showMsg("提交评论失败");
        }
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }
}
