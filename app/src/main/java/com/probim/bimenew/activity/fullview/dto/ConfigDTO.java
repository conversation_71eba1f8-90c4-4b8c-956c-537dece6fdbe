package com.probim.bimenew.activity.fullview.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ConfigDTO {

    @SerializedName("ProjectDictData")
    private List<ProjectDictDataDTO> projectDictData;


    public List<ProjectDictDataDTO> getProjectDictData() {
        return projectDictData;
    }

    public void setProjectDictData(List<ProjectDictDataDTO> projectDictData) {
        this.projectDictData = projectDictData;
    }


    public static class ProjectDictDataDTO {
        private String name;
        private String value;
        private List<ChildrenDTO> children;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public List<ChildrenDTO> getChildren() {
            return children;
        }

        public void setChildren(List<ChildrenDTO> children) {
            this.children = children;
        }

        public static class ChildrenDTO {
            private String name;
            private String value;
            private String sort;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }

            public String getSort() {
                return sort;
            }

            public void setSort(String sort) {
                this.sort = sort;
            }
        }
    }

}
