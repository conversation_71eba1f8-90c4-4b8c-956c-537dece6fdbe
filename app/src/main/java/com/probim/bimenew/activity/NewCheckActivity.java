package com.probim.bimenew.activity;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.webkit.MimeTypeMap;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.camera.CameraFragment;
import com.probim.bimenew.activity.camera.VideoActivity;
import com.probim.bimenew.activity.check.AllMaterialListActivity;
import com.probim.bimenew.activity.check.AllPeopleListActivity;
import com.probim.bimenew.activity.check.AllPeopleMultiSelectListActivity;
import com.probim.bimenew.activity.check.AllTaskListActivity;
import com.probim.bimenew.activity.check.HorizontalNoItemRecyclerView;
import com.probim.bimenew.activity.check.Materials4NewCheckAdapter;
import com.probim.bimenew.activity.check.NewCheckPhotoAdapter;
import com.probim.bimenew.activity.check.People4NewCheckAdapter;
import com.probim.bimenew.activity.check.Tasks4NewCheckAdapter;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.CheckPeopleDto;
import com.probim.bimenew.dto.UserDto;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllPeoplerResult;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.result.CheckResult;
import com.probim.bimenew.result.CheckTypeResult;
import com.probim.bimenew.result.IssueUploadPhotoResult;
import com.probim.bimenew.result.MateriaOriginListBean;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.StringUtils;
import com.probim.bimenew.utils.flowlayout.FlowLayout;
import com.probim.bimenew.utils.flowlayout.TagAdapter;
import com.probim.bimenew.utils.flowlayout.TagFlowLayout;
import com.probim.bimenew.utils.popwindow.CheckTypePopWindow;
import com.probim.bimenew.utils.view.fontview.FontEdtTextView;
import com.probim.bimenew.utils.view.fontview.FontTextView;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Description : 新建质量安全
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/10/26/16:52
 */
public class NewCheckActivity extends BaseActivity implements View.OnClickListener, CompoundButton.OnCheckedChangeListener {

    private final int code_select_materials = 1;
    private final int code_select_people = 2;
    private final int code_select_task = 3;
    private final int code_photo = 1000;
    private final int code_select_zgr = 2000;
    private final int code_select_ysr = 3000;
    private final int code_camera = 4000;
    private final List<MateriaOriginListBean.DataBean.ListBean> materialList = new ArrayList<>();
    private final List<AllTaskItemResult.DataBean.TasksBean> tasklList = new ArrayList<>();
    private final List<AllPeoplerResult.DataBean> allPeopleList = new ArrayList<>();
    private final List<File> photoFileList = new ArrayList<>();
    private final List<File> photoList = new ArrayList<>();
    private final List<String> bannerList = new ArrayList<>();
    private final List<UserDto> zgrList = new ArrayList<>();
    private final List<UserDto> ysrList = new ArrayList<>();
    private View viewPop;
    private CheckController checkController;
    private String projectID;
    private String organizeId;
    private TimePickerView pvTime;
    private FontTextView tvSelectStartTime;
    private TextView tvSelectEndTime;
    private TextView tvSelectMaterialAttention;
    private Materials4NewCheckAdapter materialsAdapter;
    private Tasks4NewCheckAdapter tasksAdapter;
    private People4NewCheckAdapter peopleAdapter;
    private RecyclerView rvSelectMaterial;
    private RecyclerView rvSelectTask;
    private TextView tvSelectPeopleAttention;
    private RecyclerView rvSelectPeople;
    private TextView tvSelectTaskAttention;
    private FontEdtTextView edtWriteTittle;
    private String startTimeStr;
    private String endTimeStr;
    private String typeIDSelected;
    private String checkerIdStr;
    private TextView tvCheckType;
    private LinearLayout linCheckType;
    private RelativeLayout rlCheckPhoto;
    private FrameLayout flSelectMoreFile;
    private LinearLayout linCheckAttention;
    private RelativeLayout rlPhoto;
    private NewCheckPhotoAdapter checkPhotoAdapter;
    private RecyclerView rvPhoto;
    private String uploadFileId = "";
    private CheckBox checkBox;
    private LinearLayout linIsAdmin;
    private EditText edtTittle;
    private String ResultSelected = "";
    private String LevelSelected = "一般";
    private List<String> ysrIDList = new ArrayList<>();
    private List<String> zgrIdList = new ArrayList<>();
    private boolean isZgrClick;
    private boolean isYsrClick;
    private TextView tvSelectZgrAttention;
    private TextView tvSelectYsrAttention;
    private TagFlowLayout ysrFlowLayout;
    private TagFlowLayout zgrFlowLayout;
    private TagAdapter<List<UserDto>> zgrAdapter;
    private TagAdapter<List<UserDto>> ysrAdapter;
    private EditText edtCheck;
    private ImageView ivDeleteCheckVideo;
    private RelativeLayout rlCheckVideo;
    private ImageView ivVideo;
    private RelativeLayout linContainer;
    private String fileVideoUrl;
    private LinearLayout linSelectCheckLevel;
    private LinearLayout linSelectCheckPeople;
    private String linkType;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_new_check);

        TranslucentUtils.setTRANSLUCENT(this);
        //获取导航栏高度
        getNavgationHeight();
        //初始化控件
        initView();
        //初始化适配器
        initRecycleview();
        //获取网络数据
        loadData();
    /*ThreadUtils.getInstance().execute(new Runnable() {
      @Override public void run() {
        Logger.e("haha");
      }
    });*/
    }

    @Override
    protected void loadData() {

        checkController = new CheckController();

        //获取数据库数据
        getProjectDao();
        //获取检查分类
        getCheckType();
    }

    @Override
    protected void initView() {
        linkType = getIntent().getStringExtra("LinkType");
        ivDeleteCheckVideo = findView(R.id.iv_delete_video, this);
        rlCheckVideo = findView(R.id.rl_check_video);
        ivVideo = findView(R.id.igv_check_video);
        viewPop = findView(R.id.view_pop);
        RelativeLayout relativeLayout = findView(R.id.rl_edt_tittle, this);
        tvSelectStartTime = findView(R.id.tv_select_start_time, this);
        tvSelectEndTime = findView(R.id.tv_select_end_time, this);
        LinearLayout linSelectMaterail = findView(R.id.lin_select_material, this);
        LinearLayout linSelectTask = findView(R.id.lin_select_task, this);
        linContainer = findView(R.id.lin_container);
        tvSelectMaterialAttention = findView(R.id.tv_select_matrial_attention);
        tvSelectPeopleAttention = findView(R.id.tv_select_people_attention);
        tvSelectTaskAttention = findView(R.id.tv_select_task_attention);
        LinearLayout linSelectPeople = findView(R.id.lin_select_people, this);
        edtWriteTittle = findView(R.id.edt_write_title);
        FontTextView fontButton = findView(R.id.btn_confirm, this);
        tvCheckType = findView(R.id.tv_check_type);
        View viewBack = findView(R.id.view_back, this);
        linCheckType = findView(R.id.lin_check_type);
        flSelectMoreFile = findView(R.id.fra_select_more_file, this);
        rlCheckPhoto = findView(R.id.rl_check_photo);
        linCheckAttention = findView(R.id.lin_check_photo, this);
        rlPhoto = findView(R.id.rl_photo);
        rvPhoto = findView(R.id.rv_check_img);
        checkBox = findView(R.id.cb_is_admin);
        checkBox.setOnCheckedChangeListener(this);
        linIsAdmin = findView(R.id.lin_is_admin);
        edtTittle = findView(R.id.edt_write_title);
        LinearLayout linZgr = findView(R.id.lin_select_zgr, this);
        LinearLayout linYsr = findView(R.id.lin_select_ysr, this);
        linSelectCheckLevel = findView(R.id.lin_select_check_level);
        linSelectCheckPeople = findView(R.id.lin_select_check_people);

        tvSelectZgrAttention = findView(R.id.tv_select_zgr_attention);
        tvSelectYsrAttention = findView(R.id.tv_select_ysr_attention);
        edtCheck = findView(R.id.edt_check_title);
        RadioGroup rgSL = findView(R.id.rg_s_l);
        rgSL.check(R.id.rg_yiban);
        CardView cardView = findView(R.id.card_view, this);
        rgSL.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                closeKeybord();
                switch (i) {
                    case R.id.rg_yiban:
                        LevelSelected = "一般";
                        break;
                    case R.id.rg_yanzhong:
                        LevelSelected = "严重";
                        break;
                    case R.id.rg_feiyanzhong:
                        LevelSelected = "非常严重";
                        break;
                }
            }
        });
        RadioGroup rgResult = findView(R.id.rg_status);
        rgResult.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                closeKeybord();
                switch (i) {
                    case R.id.rg_hege:
                        // 如果选择了合格 隐藏整改人 验收人 严重等级
                        ResultSelected = "合格";
                        linSelectCheckPeople.setVisibility(View.GONE);
                        linSelectCheckLevel.setVisibility(View.GONE);
                        break;
                    case R.id.rg_buhege:
                        ResultSelected = "不合格";
                        linSelectCheckPeople.setVisibility(View.VISIBLE);
                        linSelectCheckLevel.setVisibility(View.VISIBLE);
                        break;
                }
            }
        });

        initZgrFlow();
        initYsrFlow();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

        checkPhotoAdapter = new NewCheckPhotoAdapter(this, bannerList);
        HorizontalNoItemRecyclerView.initialize(rvPhoto).setAdapter(checkPhotoAdapter);
        checkPhotoAdapter.setOnItemDeteleListener(new NewCheckPhotoAdapter.onItemDeleteListener() {
            @Override
            public void onItemDeleteClick(int position, String url) {
                bannerList.remove(position);
                photoFileList.remove(position);
                checkPhotoAdapter.notifyDataSetChanged();
                //删除视频文件 恢复初始布局
                Logger.t("文件数量").e(photoFileList.size() + "");
                if (photoFileList.isEmpty()) {
                    rlCheckPhoto.setVisibility(View.VISIBLE);
                    flSelectMoreFile.setVisibility(View.GONE);
                    rlPhoto.setVisibility(View.GONE);
                    linCheckAttention.setVisibility(View.VISIBLE);
                }
            }
        });

        // 初始化 任务列表
        rvSelectTask = findView(R.id.rv_task);
        tasksAdapter = new Tasks4NewCheckAdapter(this, tasklList);
        tasksAdapter.addRecycleItemListener(new Tasks4NewCheckAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                Intent intent = new Intent(NewCheckActivity.this, AllTaskListActivity.class);
            }
        });
        VerticalNoItemRecyclerView.initialize(rvSelectTask).setAdapter(tasksAdapter);

        // 初始化 构件列表
        rvSelectMaterial = findView(R.id.rv_materials);
        materialsAdapter = new Materials4NewCheckAdapter(this, materialList);
        materialsAdapter.addRecycleItemListener(new Materials4NewCheckAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                Intent intent = new Intent(NewCheckActivity.this, AllMaterialListActivity.class);
            }
        });
        VerticalNoItemRecyclerView.initialize(rvSelectMaterial).setAdapter(materialsAdapter);

        // 初始化 人员列表
        rvSelectPeople = findView(R.id.rv_people);
        peopleAdapter = new People4NewCheckAdapter(this, allPeopleList);
        peopleAdapter.addRecycleItemListener(new People4NewCheckAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                Intent intent = new Intent(NewCheckActivity.this, AllPeopleListActivity.class);
            }
        });
        VerticalNoItemRecyclerView.initialize(rvSelectPeople).setAdapter(peopleAdapter);
    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 获检查分类
     */
    private void getCheckType() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", projectID);
        if (linkType.equals("1")) {
            params.put("aedtType", "quality");
        } else {
            params.put("aedtType", "security");
        }
        checkController.getCheckType(params, new CallBack<CheckTypeResult>() {
            @Override
            public void onSuccess(CheckTypeResult checkTypeResult) {
                if (checkTypeResult.getRet() == 1) {
                    if (!checkTypeResult.getData().getList().isEmpty()) {
                        typeIDSelected = checkTypeResult.getData().getList().get(0).getAedtGuid();
                        tvCheckType.setText(checkTypeResult.getData().getList().get(0).getAedtName());
                        linCheckType.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                CheckTypePopWindow.getInstance().showPopWindow(NewCheckActivity.this, viewPop, checkTypeResult.getData().getList(), new CheckTypePopWindow.onSelectListener() {
                                    @Override
                                    public void OnSelected(int postion) {

                                        typeIDSelected = checkTypeResult.getData().getList().get(postion).getAedtGuid();
                                        tvCheckType.setText(checkTypeResult.getData().getList().get(postion).getAedtName());
                                    }
                                });
                            }
                        });
                    }

                }

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    public void onClick(View v) {
        Intent intent = null;
        closeKeybord();
        switch (v.getId()) {

            case R.id.tv_select_end_time:
                getEndTime();
                break;
            case R.id.tv_select_start_time:
                getStartTime();
                break;
            case R.id.lin_select_material:
                intent = new Intent(this, AllMaterialListActivity.class);
                startActivityForResult(intent, code_select_materials);
                break;
            case R.id.lin_select_task:
                intent = new Intent(this, AllTaskListActivity.class);
                startActivityForResult(intent, code_select_task);
                break;
            case R.id.lin_select_people:
                intent = new Intent(this, AllPeopleListActivity.class);
                intent.putExtra("isCheckDetails", false);
                startActivityForResult(intent, code_select_people);
                break;
            case R.id.btn_confirm:
                if (TextUtils.isEmpty(typeIDSelected)) {
                    showMsg("类型不能为空");
                    return;
                }
                if (TextUtils.isEmpty(edtWriteTittle.getText().toString().trim())) {
                    showMsg("标题不能为空");
                    return;
                }
                if (TextUtils.isEmpty(startTimeStr)) {
                    showMsg("开始日期不能为空");
                    return;
                }
                if (TextUtils.isEmpty(endTimeStr)) {
                    showMsg("截止日期不能为空");
                    return;
                }
                if (TextUtils.isEmpty(checkerIdStr)) {
                    showMsg("检查人不能为空");
                    return;
                } else if (photoFileList.isEmpty() && photoFileList.size() == 0) {
                    addMission();
                    return;
                }

                uploadPhoto();

                break;
            case R.id.view_back:
                finish();
                break;
            case R.id.fra_select_more_file:
                // 选择更多文件
                getPhoto();

                break;
            case R.id.lin_check_photo:
                takePop();

//                getPhoto();
                break;
            case R.id.lin_select_zgr:
                isZgrClick = true;
                intent = new Intent(this, AllPeopleMultiSelectListActivity.class);
                startActivityForResult(intent, code_select_zgr);
                break;
            case R.id.lin_select_ysr:
                isYsrClick = true;
                intent = new Intent(this, AllPeopleMultiSelectListActivity.class);
                startActivityForResult(intent, code_select_ysr);
                break;
            case R.id.iv_delete_video:
                //删除视频文件 恢复初始布局
                photoFileList.clear();
                rlCheckVideo.setVisibility(View.GONE);
                rlCheckPhoto.setVisibility(View.VISIBLE);

                break;
            case R.id.card_view:
                intent = new Intent(this, VideoActivity.class);
                intent.putExtra(CustomParam.VIDEOURL, photoFileList.get(0).getPath());
                startActivity(intent);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {

                case code_select_materials:

                    List<MateriaOriginListBean.DataBean.ListBean> materialListData = (List<MateriaOriginListBean.DataBean.ListBean>) data.getExtras().getSerializable(CustomParam.StartForResult);
                    //构件数据
                    if (!materialListData.isEmpty()) {
                        String str = "已关联 " + materialListData.size() + " 个";
                        tvSelectMaterialAttention.setTextColor(getColor(R.color.black));
                        tvSelectMaterialAttention.setText(str);
                        rvSelectMaterial.setVisibility(View.VISIBLE);
                        materialList.clear();
                        materialList.addAll(materialListData);
                        materialsAdapter.notifyDataSetChanged();
                    } else {
                        tvSelectMaterialAttention.setText("请选择关联构件");
                        tvSelectMaterialAttention.setTextColor(Color.parseColor("#CCCCCC"));
                        rvSelectMaterial.setVisibility(View.GONE);
                        materialList.clear();
                        materialsAdapter.notifyDataSetChanged();
                    }

                    break;

                case code_select_task:

                    List<AllTaskItemResult.DataBean.TasksBean> taskListData = (List<AllTaskItemResult.DataBean.TasksBean>) data.getExtras().getSerializable(CustomParam.StartForResult);
                    //任务数据
                    if (!taskListData.isEmpty()) {
                        String str = "已关联 " + taskListData.size() + " 个";
                        tvSelectTaskAttention.setTextColor(getColor(R.color.black));
                        tvSelectTaskAttention.setText(str);
                        rvSelectTask.setVisibility(View.VISIBLE);
                        tasklList.clear();
                        tasklList.addAll(taskListData);
                        tasksAdapter.notifyDataSetChanged();
                    } else {
                        tvSelectTaskAttention.setText("请选择关联任务");
                        tvSelectTaskAttention.setTextColor(Color.parseColor("#CCCCCC"));
                        rvSelectTask.setVisibility(View.GONE);
                        tasklList.clear();
                        tasksAdapter.notifyDataSetChanged();
                    }

                    break;

                case code_select_people:

                    CheckPeopleDto checkPeopleDto = (CheckPeopleDto) data.getExtras().getSerializable(CustomParam.StartForResult);
                    //检查人 唯一
                    if (!checkPeopleDto.getName().isEmpty() && !checkPeopleDto.getUserId().isEmpty()) {
                        checkerIdStr = checkPeopleDto.getUserId();
                        tvSelectPeopleAttention.setText(checkPeopleDto.getName());
                        tvSelectPeopleAttention.setTextColor(getColor(R.color.black));
                    } else {
                        tvSelectPeopleAttention.setText("请指定检查人  ;");
                        tvSelectPeopleAttention.setTextColor(Color.parseColor("#CCCCCC"));
                    }
                    break;

                case code_photo:

                    if (!Matisse.obtainPathResult(data).isEmpty()) {
                        File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(0));
                        String fileExt = MimeTypeMap.getFileExtensionFromUrl(fileOrigin.toString());
                        if (judeFileExt(fileExt)) {
                            // 视频布局
                            photoFileList.add(fileOrigin);
                            rlCheckPhoto.setVisibility(View.GONE);
                            rlCheckVideo.setVisibility(View.VISIBLE);
                            Glide.with(this).load(fileOrigin).into(ivVideo);
                            return;
                        } else {
                            // 照片布局

                        }
                    }
                    for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {
                        File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
                        File fileCompress = CompressHelper.getDefault(getApplicationContext()).compressToFile(fileOrigin);
                        photoFileList.add(fileCompress);
                        bannerList.add(fileCompress.getAbsolutePath());

                    }
                    if (bannerList.size() > 0) {
                        rlCheckPhoto.setVisibility(View.VISIBLE);
                        flSelectMoreFile.setVisibility(View.VISIBLE);
                        linCheckAttention.setVisibility(View.GONE);
                        rlPhoto.setVisibility(View.VISIBLE);
                        checkPhotoAdapter.notifyDataSetChanged();
                    }
                    break;
                case code_select_zgr:

                    List<UserDto> zgrData = (List<UserDto>) data.getExtras().getSerializable(CustomParam.StartForResult);
                    //人员数据
                    if (!zgrData.isEmpty()) {

                        for (UserDto dto : zgrData) {

                            zgrIdList.add(dto.getUserId());

                        }
                        String str = "已选择 " + zgrData.size() + "人";
                        tvSelectZgrAttention.setText(str);
                        tvSelectZgrAttention.setTextColor(getColor(R.color.black));
                        zgrFlowLayout.setVisibility(View.VISIBLE);
                        zgrList.clear();
                        zgrList.addAll(zgrData);
                        zgrAdapter.notifyDataChanged();

                    } else {
                        tvSelectZgrAttention.setText("请选择");
                        tvSelectZgrAttention.setTextColor(Color.parseColor("#CCCCCC"));
                        zgrFlowLayout.setVisibility(View.GONE);
                        zgrList.clear();

                    }
                    break;

                case code_select_ysr:

                    List<UserDto> ysrData = (List<UserDto>) data.getExtras().getSerializable(CustomParam.StartForResult);
                    //人员数据
                    if (!ysrData.isEmpty()) {

                        for (UserDto dto : ysrData) {

                            ysrIDList.add(dto.getUserId());

                        }
                        String str = "已选择 " + ysrData.size() + "人";
                        tvSelectYsrAttention.setText(str);
                        tvSelectYsrAttention.setTextColor(getColor(R.color.black));
                        ysrFlowLayout.setVisibility(View.VISIBLE);
                        ysrList.clear();
                        ysrList.addAll(ysrData);
                        ysrAdapter.notifyDataChanged();

                    } else {
                        tvSelectYsrAttention.setText("请选择");
                        tvSelectYsrAttention.setTextColor(Color.parseColor("#CCCCCC"));
                        ysrFlowLayout.setVisibility(View.GONE);
                        ysrList.clear();

                    }
                    break;
                case code_camera:
                    String filePhotoUrl = data.getStringExtra(CustomParam.CameraPhoto);
                    fileVideoUrl = data.getStringExtra(CustomParam.CameraVideo);
                    File filePhotoOrigin = FileUtil.getFileByPath(filePhotoUrl);
                    File fileVideoOrigin = FileUtil.getFileByPath(fileVideoUrl);
                    if (!TextUtils.isEmpty(filePhotoUrl)) {
                        bannerList.add(filePhotoUrl);
                        photoFileList.add(filePhotoOrigin);
                        if (bannerList.size() > 0) {
                            rlCheckPhoto.setVisibility(View.VISIBLE);
                            flSelectMoreFile.setVisibility(View.VISIBLE);
                            linCheckAttention.setVisibility(View.GONE);
                            rlPhoto.setVisibility(View.VISIBLE);
                            checkPhotoAdapter.notifyDataSetChanged();
                        }
                    } else if (!TextUtils.isEmpty(fileVideoUrl)) {
//                        showMsg(fileVideoUrl);
                        photoFileList.add(fileVideoOrigin);
                        rlCheckPhoto.setVisibility(View.GONE);
                        rlCheckVideo.setVisibility(View.VISIBLE);
                        Glide.with(this).load(fileVideoUrl).into(ivVideo);
                    }

                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            projectID = bean.getProjectID();
            organizeId = bean.getBimProjectId();
        }
    }

    /**
     * 获取开始日期
     */
    private void getStartTime() {
        TimePicker(true);
    }

    /**
     * 获取截止日期
     */
    private void getEndTime() {
        TimePicker(false);
    }

    /**
     * 时间选择器
     */
    private void TimePicker(boolean type) {


        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);
        //确定按钮文字颜色
        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {

                if (type) {
                    startTimeStr = getDay(date);
                    tvSelectStartTime.setText(startTimeStr);
                    tvSelectStartTime.setTextColor(getResources().getColor(R.color.black));
                } else {
                    endTimeStr = getDay(date);
                    tvSelectEndTime.setText(endTimeStr);
                    tvSelectEndTime.setTextColor(getResources().getColor(R.color.black));
                }
            }
        }).setCancelText("清空").setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false}).setContentTextSize(16).setLabel("", "", "", "", "", "").isCenterLabel(true).setLineSpacingMultiplier(3.0f).setDividerColor(R.color.divider_color).setDecorView(null)
//                .setRangDate(startDate, endDate)
                .setTextColorCenter(Color.parseColor("#283A4F")).setTextColorOut(Color.parseColor("#A6AEB6"))
                //容器
                //.setDecorView(linContainer)
                .setLayoutRes(R.layout.pickerview_newcheck, new CustomListener() {
                    @Override
                    public void customLayout(View v) {

                        TextView tvDateTittle = v.findViewById(R.id.tv_date_title);
                        if (type) {
                            tvDateTittle.setText("请选择开始日期");
                        } else {
                            tvDateTittle.setText("请选择截止日期");
                        }

                        TextView btnConfirm = v.findViewById(R.id.btn_confirm);
                        btnConfirm.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });

                        TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                        tv_clear.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                if (type) {
                                    // tvStartTime.setText("请选择时间");
                                } else {
                                    // tvEndTime.setText("请选择时间");
                                }
                                pvTime.dismiss();
                            }
                        });

                        ImageView ivClose = (ImageView) v.findViewById(R.id.iv_close);
                        ivClose.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                //pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });
                    }
                }).build();
        pvTime.show();
    }

    private String getDay(Date date) {//可根据需要自行截取数据显示
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    /***
     *
     * 新建现场任务
     */

    public void addMission() {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        //检查类别guid
        params.put("Aedt_guid", typeIDSelected);
        //项目ID
        params.put("organizeId", projectID);
        //标题
        String title = edtWriteTittle.getText().toString().trim();
        params.put("ExamineRemark", title);
        //检查人ID
        params.put("CheckerUserId", checkerIdStr);
        //任务
        params.put("Rel_taskjson", JsonHelper.toJson(tasklList));
        //构件
        params.put("Rel_materialjson", JsonHelper.toJson(materialList));
        //开始日期
        params.put("ExamineDate", startTimeStr);
        //结束日期
        params.put("RectificateDate", endTimeStr);

        params.put("ImageIds", uploadFileId);
        params.put("LinkType", linkType);
        checkController.addMission(params, new CallBack<CheckResult>() {
            @Override
            public void onSuccess(CheckResult checkResult) {
                if (checkResult.getRet() == 1) {

                    if (checkBox.isChecked()) {
                        // 监理方选中 手动进行检查操作
                        if (!TextUtils.isEmpty(checkResult.getData())) {
                            checkData(checkResult.getData());
                        }

                    } else {
                        showMsg("新建检查成功");
                        finish();
                    }
                } else {
                    showMsg("新建检查失败");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

        mLoading.dismiss();
    }

    /**
     * 对PC创建的任务进行检查操作
     * 【ExamineID】:哪个检查任务,
     * 【Token】:口令，
     * 【RectificationRemark】:填写的检查的标题,
     * 【aede_severitylevel】:一般，严重，非常严重,
     * 【IsPassed】:1为合格，否则为不合格，
     * 【RelationMemberID】:指定的整改人，英文逗号分隔userId不可为空,
     * 【PrincipalID】 指定的验收人（英文逗号分隔UserId，为空时，使用Token代表的人）
     * 【OrganizeId】项目ID
     * 【文件】
     */
    private void checkData(String examineId) {
        mLoading.show();
        HashMap p = new HashMap<String, String>();
        if (TextUtils.isEmpty(edtCheck.getText().toString().trim())) {
            mLoading.dismiss();
            showMsg("检查内容为空");
            return;
        }
        p.put("RectificationRemark", edtCheck.getText().toString().trim());

        String IsPassed;
        if (ResultSelected.equals("合格")) {
            IsPassed = "1";
        } else {
            IsPassed = "0";
        }
        p.put("IsPassed", IsPassed);
        if (!ResultSelected.equals("合格")) {
            if (TextUtils.isEmpty(LevelSelected)) {
                mLoading.dismiss();
                showMsg("严重等级为空");
                return;
            }
            p.put("aede_severitylevel", LevelSelected);
            if (zgrIdList.isEmpty()) {
                mLoading.dismiss();
                showMsg("整改人为空");
                return;
            }
        }
        p.put("RelationMemberID", StringUtils.dataToString(zgrIdList));
        p.put("ExamineID", examineId);
        p.put("Token", Hawk.get(CustomParam.Token));
        p.put("PrincipalID", StringUtils.dataToString(ysrIDList));
        p.put("OrganizeId", organizeId);
        checkController.check(photoList, p, new CallBack<CheckResult>() {
            @Override
            public void onSuccess(CheckResult checkResult) {
                if (checkResult.getRet() == 1) {

                    finish();
                } else {
                    showMsg(checkResult.getMsg());
                    mLoading.dismiss();

                }
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });
    }


    private void getNavgationHeight() {

        Resources resources = getResources();
        int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
        int height = resources.getDimensionPixelSize(resourceId);
        Logger.t("navHeight").e(height + "");
    }

    /**
     * 选取照片
     */
    private void getGallry() {

        Set<MimeType> set = new HashSet();
        set.add(MimeType.JPEG);
        set.add(MimeType.PNG);
        set.add(MimeType.MP4);
        Matisse.from(this).choose(set).countable(true).capture(true).captureStrategy(new CaptureStrategy(true, getPackageName())).maxSelectable(9).addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K)).gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size)).thumbnailScale(0.85f).imageEngine(new GlideEngine()).showSingleMediaType(true).restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT).forResult(code_photo);
    }

    /**
     * 选取照片
     */
    private void getPhoto() {

        Set<MimeType> set = new HashSet();
        set.add(MimeType.JPEG);
        set.add(MimeType.PNG);
        Matisse.from(this).choose(set).countable(true).capture(true).captureStrategy(new CaptureStrategy(true, getPackageName())).maxSelectable(9).addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K)).gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size)).thumbnailScale(0.85f).imageEngine(new GlideEngine()).showSingleMediaType(true).restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT).forResult(code_photo);
    }

    static final int REQUEST_IMAGE_CAPTURE = 1;

    private void dispatchTakePictureIntent() {

        Intent takePictureIntent = new Intent(MediaStore.ACTION_VIDEO_CAPTURE);
        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
            startActivityForResult(takePictureIntent, code_photo);
        }
    }


    /**
     * 自动关闭软键盘
     */
    public void closeKeybord() {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(getWindow().getDecorView().getWindowToken(), 0);
        }
    }

    /**
     * 上传图片
     */
    private void uploadPhoto() {

        HashMap<String, String> params = new HashMap<>();
        params.put(CustomParam.Token, Hawk.get(CustomParam.Token));
        checkController.uploadPhoto(photoFileList, params, new CallBack<IssueUploadPhotoResult>() {
            @Override
            public void onSuccess(IssueUploadPhotoResult issueUploadPhotoResult) {
                if (issueUploadPhotoResult.getRet() == 1) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (IssueUploadPhotoResult.DataBean dataBean : issueUploadPhotoResult.getData()) {
                        stringBuilder.append(dataBean.getBf_guid());
                        stringBuilder.append(",");
                    }
                    uploadFileId = stringBuilder.toString();
                    addMission();
                } else {
                    showMsg(issueUploadPhotoResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

        if (isChecked) {
            linIsAdmin.setVisibility(View.VISIBLE);
            String userName = Hawk.get(CustomParam.RealName);
            String userId = Hawk.get(CustomParam.UserId);
            tvSelectPeopleAttention.setText(userName);
            tvSelectPeopleAttention.setTextColor(getColor(R.color.black));
            checkerIdStr = userId;
        } else {
            linIsAdmin.setVisibility(View.GONE);
            tvSelectPeopleAttention.setText("请指定检查人");
            tvSelectPeopleAttention.setTextColor(getColor(R.color.xxxx));
            checkerIdStr = "";
        }
    }

    /**
     * 初始化整改人数据
     */
    private void initZgrFlow() {

        zgrFlowLayout = findViewById(R.id.flow_zgr);
        zgrAdapter = new TagAdapter(zgrList) {
            @Override
            public View getView(FlowLayout parent, int position, Object o) {
                TextView tv = (TextView) LayoutInflater.from(parent.getContext()).inflate(R.layout.item_check_flowlayout, parent, false);
                tv.setText(zgrList.get(position).getUserName());
                return tv;
            }
        };

        zgrFlowLayout.setAdapter(zgrAdapter);

        zgrFlowLayout.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {

                return true;
            }
        });
    }

    /**
     * 初始化验收人数据
     */
    private void initYsrFlow() {

        ysrFlowLayout = findViewById(R.id.flow_ysr);
        ysrAdapter = new TagAdapter(ysrList) {
            @Override
            public View getView(FlowLayout parent, int position, Object o) {
                TextView tv = (TextView) LayoutInflater.from(parent.getContext()).inflate(R.layout.item_check_flowlayout, parent, false);
                tv.setText(ysrList.get(position).getUserName());
                return tv;
            }
        };

        ysrFlowLayout.setAdapter(ysrAdapter);

        ysrFlowLayout.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {

                return true;
            }
        });
    }

    /**
     * 选择照片或视频文件
     */
    private void takePop() {
        View view = LayoutInflater.from(NewCheckActivity.this).inflate(R.layout.popwindow_photo, null);
        setBackgroundAlpha(0.5f);
        PopupWindow mPop = new PopupWindow(NewCheckActivity.this);
        mPop.setContentView(view);
        mPop.setFocusable(true);
        mPop.setOutsideTouchable(true);
        mPop.setBackgroundDrawable(new ColorDrawable());
        mPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        mPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        view.findViewById(R.id.rl_take_photo).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(NewCheckActivity.this, CameraFragment.class);
                startActivityForResult(intent, code_camera);
                mPop.dismiss();
            }
        });
        view.findViewById(R.id.rl_chose_file).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getGallry();
                mPop.dismiss();
            }
        });
        view.findViewById(R.id.rl_cancle).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPop.dismiss();
            }
        });
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });
        mPop.setAnimationStyle(R.style.picker_view_slide_anim);
        mPop.showAtLocation((ViewGroup) findViewById(android.R.id.content), Gravity.BOTTOM, 0, 0);
    }

    /**
     * 设置popwindow默认背景变灰
     */
    public void setBackgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha;
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        getWindow().setAttributes(lp);
    }

    /**
     * 文件类型
     *
     * @param fileExt
     * @return
     */
    private boolean judeFileExt(String fileExt) {

        switch (fileExt) {
            case "mp4":
                return true;
            default:
                return false;
        }
    }
}
