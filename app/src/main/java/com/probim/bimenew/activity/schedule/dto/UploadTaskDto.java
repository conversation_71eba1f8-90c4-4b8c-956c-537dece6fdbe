package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class UploadTaskDto {

    private String token;
    @SerializedName("ProgressIds")
    private List<String> progressIds;
    @SerializedName("AuditType")
    private Integer auditType;
    private String AuditDescription;

    public String getAuditDescription() {
        return AuditDescription;
    }

    public void setAuditDescription(String auditDescription) {
        AuditDescription = auditDescription;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public List<String> getProgressIds() {
        return progressIds;
    }

    public void setProgressIds(List<String> progressIds) {
        this.progressIds = progressIds;
    }

    public Integer getAuditType() {
        return auditType;
    }

    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }
}
