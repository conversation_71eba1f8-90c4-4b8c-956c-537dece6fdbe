package com.probim.bimenew.activity;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup.LayoutParams;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.PopupWindow.OnDismissListener;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.IssueJoinerAdapter;
import com.probim.bimenew.adapter.Photo2Adapter;
import com.probim.bimenew.adapter.ProblemJoinAdapter;
import com.probim.bimenew.adapter.ProblemMemberRvAdapter;
import com.probim.bimenew.adapter.ProblemPopWindowHeadAdapter;
import com.probim.bimenew.adapter.ProblemPopWindowJoinAdapter;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.ModelPointViewDetails;
import com.probim.bimenew.model.PointViewDescModel;
import com.probim.bimenew.model.ProjectUser.UsersBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.IssueJoinerResult;
import com.probim.bimenew.result.IssueTypeDto;
import com.probim.bimenew.result.IssueUploadPhotoResult;
import com.probim.bimenew.result.NewIssueResult;
import com.probim.bimenew.utils.Base64Utils;
import com.probim.bimenew.utils.DataHolder;
import com.probim.bimenew.utils.DeepCopyUtils;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.PermissionUtils;
import com.probim.bimenew.utils.StringUtils;
import com.probim.bimenew.utils.WheelView;
import com.probim.bimenew.utils.WheelView.OnWheelViewListener;
import com.probim.bimenew.utils.popwindow.QuestionPopWindow;
import com.probim.bimenew.utils.view.MyGridView;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.UnrecoverableKeyException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Description :问题详情页面
 * Author : Gary
 * Email  : <EMAIL> Date   :
 * 2018/8/10/15:36.
 */
public class NewIssueActivity extends BaseActivity {

    public static boolean viewPointOrTwoDis = false;
    public static boolean isFromWhere = false;
    private final int code_title = 1000;
    private final int code_intro = 2000;
    private final int code_photo = 3000;
    @BindView(R.id.rv_photo)
    RecyclerView rvPhoto;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_right)
    TextView tvRight;
    @BindView(R.id.tv_problem_title)
    TextView tvProblemTitle;
    @BindView(R.id.tv_problem_intro)
    TextView tvProblemIntro;
    List<PhotoDto> photoDtoList = new ArrayList<>();
    List<PhotoDto> viewPointOrNtationphotoList = new ArrayList<>();
    List<String> headList = new ArrayList<>();
    List<String> joinList = new ArrayList<>();
    List<String> joinIDList = new ArrayList<>();
    List<String> headIdList = new ArrayList<>();
    List<String> headCacheList = new ArrayList<>();
    List<String> joinCacheList = new ArrayList<>();
    @BindView(R.id.grid_head)
    MyGridView gridHead;
    @BindView(R.id.grid_join)
    MyGridView gridJoin;
    @BindView(R.id.tv_date)
    TextView tvDate;
    @BindView(R.id.tv_tv_photo_size)
    TextView tvTvPhotoSize;
    @BindView(R.id.lin_all)
    LinearLayout linAll;
    @BindView(R.id.tv_type)
    TextView tvType;
    @BindView(R.id.lin_view)
    LinearLayout linView;
    @BindView(R.id.img_view)
    ImageView imgView;
    @BindView(R.id.tv_view)
    TextView tvView;
    @BindView(R.id.tv_view_intro)
    TextView tvViewIntro;
    @BindView(R.id.lin_no_select_photo)
    LinearLayout linNoSelectPhoto;
    @BindView(R.id.lin_select_photo_new_question)
    LinearLayout linSelectPhotoNewQuestion;
    @BindView(R.id.rl_select_viewpoint)
    RelativeLayout rlSelectViewpoint;
    @BindView(R.id.img_drawings)
    ImageView imgDrawings;
    @BindView(R.id.tv_drawings_name)
    TextView tvDrawingsName;
    @BindView(R.id.rl_select_drawings)
    RelativeLayout rlSelectDrawings;
    @BindView(R.id.line_photo)
    View linePhoto;
    @BindView(R.id.tv_no_head)
    TextView tvNoHead;
    @BindView(R.id.tv_no_join)
    TextView tvNoJoin;
    private Photo2Adapter photoAdapter;
    private ProblemJoinAdapter problemJoinAdapter;
    private TimePickerView pvTime;
    private ProblemPopWindowHeadAdapter problemPopWindowHeadAdapter;
    private ProblemPopWindowJoinAdapter problemPopWindowJoinAdapter;
    private GridView joinOrHead;
    private WheelView typeWheelView;
    private TextView tvConfirm;
    private String TypeSelected = "";
    private String TypeIdSelected = "";
    private TextView tv_title_select;
    private ArrayList<String> wheelStatusList = new ArrayList<>();
    private List<String> wheelStatusIDList = new ArrayList<>();
    private List<String> wheelTypeIDList = new ArrayList<>();
    private ArrayList<String> wheelTypeList = new ArrayList<>();
    private TextView tv_type;
    private View line_type;
    private String organizeId = "";
    private List<IssueJoinerResult.DataBean> projectRoleList = new ArrayList<>();
    private List<UsersBean> projectMemberList = new ArrayList<>();
    private ProblemMemberRvAdapter problemMemberRvAdapter;
    private boolean isHeadPop = true;
    private boolean isTypeSelected = false;
    private boolean isStatusSelected = false;
    private PopupWindow typePop;
    private List<File> photoFileList = new ArrayList<>();
    private IssueController issueController;
    private String bimProjectId = "";
    private String titleResult;
    private String introResult = "";
    private String uploadFileId = "";
    private String issueDate = "";
    private String modelId = "";
    private String viewpointId = "";
    private String new_question_type;
    private boolean isViewPointSelected = false;
    private boolean isDrawingSelected = false;
    private RecyclerView rvJoiner;
    private String viewPointPositon;
    //    private String viewPointImgBase64;
    protected static String viewBase64;
    private String[] perms = new String[] {
            Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_new_issue_back);
        ButterKnife.bind(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
        issueController = new IssueController();
        //获取数据库数据
        GetProjectDao();
        //获取参与人数据
        GetProjectUser();
        //获取状态分类数据
        GetFilter();
    }

    /**
     * byte数组转bitmap
     */
    private void byteToBitmap(String imageBase64) {
        byte[] byte64 = Base64.decode(imageBase64.split(",")[1], Base64.DEFAULT);
        Bitmap bitmap = BitmapFactory.decodeByteArray(byte64, 0, byte64.length);
        imgView.setImageBitmap(bitmap);
    }

    @Override
    protected void initView() {

        if (getIntent() != null) {
            //获取相机位置 从相机发起问题
            viewPointPositon = getIntent().getStringExtra(CustomParam.JSVIEWPOISTION);
            //获取相机位置图片
            // viewPointImgBase64 = getIntent().getStringExtra(CustomParam.JSVIEWPOISTIONIMG);
            //模型ID
            modelId = getIntent().getStringExtra(CustomParam.ModelId);
            //Logger.t("新建问题--->>>>").e(viewPointImgBase64);
            //Logger.t("截取后字符串").e(viewPointImgBase64.substring(6, viewPointImgBase64.length()));
            if (!TextUtils.isEmpty(viewBase64)) {
                Logger.e(viewBase64);
                linView.setVisibility(View.VISIBLE);
                byteToBitmap(viewBase64);
            }
        }

        tvTitle.setText("新建问题");
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("保存");
        tvLeft.setText("取消");
        //tvTvPhotoSize.setText("0");
        TimePicker();
        //初始化参与人适配器
        problemJoinAdapter = new ProblemJoinAdapter(joinList, this, true);
        gridJoin.setAdapter(problemJoinAdapter);
        gridJoin.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {

                showJoinPop(DeepCopyUtils.deepCopy(joinList), DeepCopyUtils.deepCopy(joinIDList));
            }
        });

        //为 RecyclerView 设置布局管理器

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rvPhoto.setLayoutManager(linearLayoutManager);
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvPhoto.addItemDecoration(
                new DividerItemDecoration(this, DividerItemDecoration.HORIZONTAL));
        //动画
        rvPhoto.setItemAnimator(new DefaultItemAnimator());

        //初始化照片适配器
        photoAdapter = new Photo2Adapter(photoDtoList, this);

        photoAdapter.addItemListener(new Photo2Adapter.OnItemListener() {
            @Override
            public void OnItemDelete(int pos, Object o) {
                photoDtoList.remove(pos);
                photoFileList.remove(pos);
                tvTvPhotoSize.setText(photoDtoList.size() + "");
                photoAdapter.notifyDataSetChanged();
                if (photoDtoList.size() == 0) {
                    linePhoto.setVisibility(View.GONE);
                }
            }

            @Override
            public void OnItemClick(int pos, Object o) {
                if (photoDtoList.size() != 0) {
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
                    bundle.putInt(CustomParam.PhotoPosition, pos);
                    Intent intent = new Intent(NewIssueActivity.this, PhotoViewActivity.class);
                    intent.putExtras(bundle);
                    startActivity(intent);
                }
            }
        });
        rvPhoto.setAdapter(photoAdapter);
        //初始化popwindow中选择人适配器

    }

    @Override
    protected void initData() {

    }

    /**
     * 初始化选择人 recycleview
     */
    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Nullable
    @OnClick({
            R.id.lin_click_title, R.id.lin_click_initro, R.id.lin_back, R.id.tv_right,
            R.id.lin_status, R.id.lin_select_time, R.id.tv_add_photo, R.id.lin_type, R.id.lin_select_join,
            R.id.lin_select_head, R.id.tv_load_view, R.id.lin_no_select_photo, R.id.img_view,
            R.id.tv_view_more, R.id.tv_drawings_more, R.id.tv_open_drawings
    })
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.lin_click_title:

                if (!TextUtils.isEmpty(titleResult)) {
                    Bundle bundle = new Bundle();
                    bundle.putString(CustomParam.TitleStr, titleResult);
                    Intent intent = new Intent(this, EdtTitleActivity.class);
                    intent.putExtras(bundle);
                    startActivityForResult(intent, code_title);
                } else {
                    startActivityForResult(new Intent(NewIssueActivity.this, EdtTitleActivity.class),
                            code_title);
                }
                break;
            case R.id.lin_click_initro:

                if (!TextUtils.isEmpty(introResult)) {
                    Bundle bundle1 = new Bundle();
                    bundle1.putString(CustomParam.IntroStr, introResult);
                    Intent intent1 = new Intent(this, EdtIntroActivity.class);
                    intent1.putExtras(bundle1);
                    startActivityForResult(intent1, code_intro);
                } else {
                    startActivityForResult(new Intent(NewIssueActivity.this, EdtIntroActivity.class),
                            code_intro);
                }
                break;
            case R.id.lin_back:
                finish();
                break;
            case R.id.tv_right:
                if (TextUtils.isEmpty(titleResult)) {
                    showMsg("请输入标题");
                    return;
                } else if (TextUtils.isEmpty(TypeIdSelected)) {
                    showMsg("请选择类型");
                    return;
                } /*else if (joinIDList.isEmpty() && headIdList.size() == 0) {
                    showMsg("请选择参与人");
                    return;
                }*/ else if (TextUtils.isEmpty(issueDate)) {
                    showMsg("请选择截止日期");
                    return;
                } else if (photoFileList.isEmpty() && photoFileList.size() == 0) {
                    SaveIssue();
                    return;
                }
                //新增问题追踪
                UploadPhoto();
                break;
            case R.id.lin_select_time:
                pvTime.show();
                break;
            case R.id.tv_add_photo:
            case R.id.lin_no_select_photo:
                requestCameraPermission();
                break;
            case R.id.lin_type:
                showTypePop();
                break;
            case R.id.lin_select_join:
                showJoinPop(DeepCopyUtils.deepCopy(joinList), DeepCopyUtils.deepCopy(joinIDList));
                break;
            case R.id.tv_load_view:
                Bundle bundle1 = new Bundle();
                bundle1.putString(CustomParam.ModelId, modelId);
                bundle1.putString(CustomParam.ViewPointId, viewpointId);
                bundle1.putString(CustomParam.TvLeft, "问题");
                Intent intent1 = new Intent(this, LoadModelViewActivity.class);
                intent1.putExtras(bundle1);
                startActivity(intent1);
                break;
            case R.id.img_view:

                /*//分类加载
                if ("视点".equalsIgnoreCase(new_question_type)) {
                    Bundle bundle2 = new Bundle();
                    bundle2
                            .putSerializable(CustomParam.StartPhoto, (Serializable) viewPointOrNtationphotoList);
                    Intent intent2 = new Intent(NewIssueActivityBack.this,
                            PhotoViewForPointViewActivity.class);
                    intent2.putExtras(bundle2);
                    startActivity(intent2);
                } else {

                    startActivity(NewQuestionPhotoActivity.class);
                }*/

                break;
            case R.id.tv_view_more:
                //选择视点
                if (isViewPointSelected) {
                    QuestionPopWindow.getInstance().showPopWindwin(NewIssueActivity.this, true,
                            new QuestionPopWindow.onSelectListener() {
                                @Override
                                public void OnDelete() {
                                    rlSelectViewpoint.setVisibility(View.GONE);
                                    SelectModelViewActivity.ViewId = "";
                                    viewpointId = " ";
                                    ApiConstant.viewPointOrTwoDis = false;
                                    isViewPointSelected = false;
                                    SelectModelViewActivity.IsCan = false;
                                }

                                @Override
                                public void OnSelect() {
                                    startActivity(SelectModelForNewQuestionActivity.class);
                                    ApiConstant.viewPointOrTwoDis = true;
                                }

                                @Override
                                public void OnCancle() {

                                }
                            });
                } else {
                    startActivity(SelectModelForNewQuestionActivity.class);
                    ApiConstant.viewPointOrTwoDis = true;
                }
                break;
            case R.id.tv_drawings_more:
                //选择图纸
                ApiConstant.isCanTap = true;
                if (isDrawingSelected) {
                    QuestionPopWindow.getInstance().showPopWindwin(NewIssueActivity.this, false,
                            new QuestionPopWindow.onSelectListener() {
                                @Override
                                public void OnDelete() {
                                    rlSelectDrawings.setVisibility(View.GONE);
                                    MarkActivity.img2d_pos = " ";
                                    MarkActivity.img2dId = " ";
                                    MarkActivity.img2d_ModelId = " ";
                                    MarkActivity.img_url = "";
                                    MarkActivity.imgName = "";
                                    MarkActivity.isCan = false;
                                    isDrawingSelected = false;
                                }

                                @Override
                                public void OnSelect() {
                                    startActivity(SelectModelForNewQuestionActivity.class);
                                    ApiConstant.viewPointOrTwoDis = false;
                                    isFromWhere = false;
                                }

                                @Override
                                public void OnCancle() {

                                }
                            });
                } else {

                    startActivity(SelectModelForNewQuestionActivity.class);
                    ApiConstant.viewPointOrTwoDis = false;
                    isFromWhere = false;
                }
                break;
            case R.id.tv_open_drawings:
                //打开图纸
                isFromWhere = true;
                ApiConstant.isCanTap = false;
                Bundle bundle = new Bundle();
                bundle.putString(CustomParam.TwoDimensId, MarkActivity.img2dId);
                bundle.putString(CustomParam.ProjectId, organizeId);
                bundle.putString(CustomParam.ModelId, MarkActivity.img2d_ModelId);
                bundle.putString(CustomParam.DrawingPosition, MarkActivity.img2d_pos);
                Intent intent = new Intent(this, MarkActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode != RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case code_title:
                titleResult = data.getExtras()
                        .getString(CustomParam.StartForResult);
                tvProblemTitle.setText(titleResult);
                tvProblemTitle.setTextColor(getResources().getColor(R.color.black));
                break;
            case code_intro:
                introResult = data.getExtras()
                        .getString(CustomParam.StartForResult);
                tvProblemIntro.setText(introResult);
                tvProblemIntro.setTextColor(getResources().getColor(R.color.black));
                break;
            case code_photo:

                for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {
                    linePhoto.setVisibility(View.VISIBLE);
                    File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
                    File fileCompress = CompressHelper.getDefault(getApplicationContext())
                            .compressToFile(fileOrigin);

                    photoDtoList.add(new PhotoDto(fileCompress.getAbsolutePath()));

                    photoFileList.add(fileCompress);
                }
                linNoSelectPhoto.setVisibility(View.GONE);
                linSelectPhotoNewQuestion.setVisibility(View.VISIBLE);
                tvTvPhotoSize.setText(photoDtoList.size() + "");
                photoAdapter.notifyDataSetChanged();

                break;
            default:
                tvProblemTitle.setText("");
                tvProblemIntro.setText("");
                break;
        }
    }

    private String getDay(Date date) {//可根据需要自行截取数据显示
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    /**
     * 时间选择器
     */
    private void TimePicker() {

        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);

        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                issueDate = getDay(date);
                tvDate.setText(getDay(date));
            }
        }).setCancelText("清空").setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false}).setContentTextSize(18)
                .setLabel("", "", "", "", "", "")
                .isCenterLabel(false).setLineSpacingMultiplier(3.0f)
                .setDividerColor(R.color.divider_color)
                .setDecorView(null)
                .setTitleText("请选择截止日期").setRangDate(startDate, endDate).setLayoutRes(R.layout.pickerview_custom_time,
                        new CustomListener() {
                            @Override
                            public void customLayout(View v) {

                                TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                                tv_clear.setOnClickListener(new OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        tvDate.setText("请选择截止日期");
                                        pvTime.dismiss();
                                    }
                                });

                                TextView tvConmit = (TextView) v.findViewById(R.id.tv_finish);
                                tvConmit.setOnClickListener(new OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        pvTime.returnData();
                                        pvTime.dismiss();
                                    }
                                });
                            }
                        })
                .build();
    }

    /**
     * 选取照片
     */
    private void GetPhoto() {
        Set<MimeType> mimeTypeSet = new HashSet<>();
        mimeTypeSet.add(MimeType.JPEG);
        mimeTypeSet.add(MimeType.PNG);
        Matisse.from(this)
                .choose(mimeTypeSet)
                .countable(true)
                .capture(true)
                .captureStrategy(
                        new CaptureStrategy(true, getPackageName()))
                .maxSelectable(9)
                .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
                .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
                .thumbnailScale(0.85f)
                .imageEngine(new GlideEngine())
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                .forResult(code_photo);
    }

    /**
     * 显示权限申请理由对话框
     */
    private void showPermissionRationale(String title, String message, Runnable onConfirm) {
        new AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("确定", (dialog, which) -> onConfirm.run())
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 显示跳转设置页面的对话框
     */
    private void showGoToSettingsDialog(String message) {
        new AlertDialog.Builder(this)
                .setTitle("权限设置")
                .setMessage(message)
                .setPositiveButton("去设置", (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", getPackageName(), null);
                    intent.setData(uri);
                    startActivity(intent);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 处理权限被拒绝的情况
     */
    private void handlePermissionDenied(String permissionName, boolean neverAskAgain) {
        if (neverAskAgain) {
            showGoToSettingsDialog(permissionName + "被永久拒绝，请在设置中手动开启");
        } else {
            Toast.makeText(this, permissionName + "被拒绝", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 申请相机权限示例
     */
    private void requestCameraPermission() {
        // 先检查权限是否已授予
        if (PermissionUtils.hasPermissions(this, perms)) {
            GetPhoto();
            return;
        }

        // 检查是否需要显示权限说明
        if (PermissionUtils.shouldShowRequestPermissionRationale(this, PermissionUtils.Permission.CAMERA)) {
            showPermissionRationale("相机权限", "需要相机权限才能拍照", () -> {
                PermissionUtils.requestPermissions(this, perms, new PermissionUtils.PermissionCallback() {
                    @Override
                    public void onPermissionGranted() {
                        GetPhoto();
                    }

                    @Override
                    public void onPermissionDenied(List<String> deniedPermissions, boolean neverAskAgain) {
                        handlePermissionDenied("相机权限", neverAskAgain);
                    }
                });
            });
        } else {
            // 直接申请权限
            PermissionUtils.requestPermissions(this, perms, new PermissionUtils.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    GetPhoto();
                }

                @Override
                public void onPermissionDenied(List<String> deniedPermissions, boolean neverAskAgain) {
                    handlePermissionDenied("相机权限", neverAskAgain);
                }
            });
        }
    }

    /**
     * 参加人popwindow
     */
    private void showJoinPop(List<String> copyJoinList, List<String> copyJoinIdList) {
        isHeadPop = false;
        View view = LayoutInflater.from(NewIssueActivity.this)
                .inflate(R.layout.popwindow_issue_joiner, null);
        PopupWindow mPeoplePop = new PopupWindow(NewIssueActivity.this);
        mPeoplePop.setContentView(view);
        mPeoplePop.setFocusable(true);
        mPeoplePop.setOutsideTouchable(true);
        mPeoplePop.setBackgroundDrawable(new BitmapDrawable());
        joinOrHead = view.findViewById(R.id.grid_joinOrhead);
        problemPopWindowJoinAdapter = new ProblemPopWindowJoinAdapter(copyJoinList, this);
        joinOrHead.setAdapter(problemPopWindowJoinAdapter);
        joinOrHead.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                copyJoinList.remove(i);
                copyJoinIdList.remove(i);
                problemPopWindowJoinAdapter.notifyDataSetChanged();
            }
        });
        rvJoiner = view.findViewById(R.id.rv_member);
        TextView tv_select_all = view.findViewById(R.id.tv_select_all);
        TextView tv_confirm = view.findViewById(R.id.tv_confirm);
        TextView tv_title = view.findViewById(R.id.tv_select_title);
        tv_title.setText("请选择参与人");
        setBackgroundAlpha(0.5f);
        initRecycleview();

        //成员列表

        rvJoiner.setLayoutManager(new LinearLayoutManager(this));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvJoiner.addItemDecoration(
                new DividerItemDecoration((NewIssueActivity.this), DividerItemDecoration.VERTICAL));
        //动画
        rvJoiner.setItemAnimator(new DefaultItemAnimator());

        IssueJoinerAdapter joinerAdapter = new IssueJoinerAdapter(this, projectRoleList);

        joinerAdapter.addRecycleItemListener(new IssueJoinerAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                IssueJoinerResult.DataBean usersBean = (IssueJoinerResult.DataBean) o;
                //弹出负责人pop
                copyJoinList.add(usersBean.getRealName());
                copyJoinIdList.add(usersBean.getUserId());
                removeStringListDupli(copyJoinList);
                removeStringListDupli(copyJoinIdList);
                problemPopWindowJoinAdapter.notifyDataSetChanged();
            }
        });
        rvJoiner.setAdapter(joinerAdapter);

        mPeoplePop.setHeight(LayoutParams.WRAP_CONTENT);
        mPeoplePop.setWidth(LayoutParams.MATCH_PARENT);
        tv_select_all.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {

                for (UsersBean name : projectMemberList) {

                    copyJoinList.add(name.getRealName());
                    copyJoinIdList.add(name.getUserId());
                    removeStringListDupli(copyJoinList);
                    removeStringListDupli(copyJoinIdList);
                }

                problemPopWindowJoinAdapter.notifyDataSetChanged();
            }
        });

        tv_confirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                joinList.clear();
                joinIDList.clear();
                joinList.addAll(copyJoinList);
                joinIDList.addAll(copyJoinIdList);

                problemJoinAdapter.notifyDataSetChanged();
                mPeoplePop.dismiss();
                if (joinList.isEmpty()) {
                    tvNoJoin.setVisibility(View.VISIBLE);
                } else {
                    tvNoJoin.setVisibility(View.GONE);
                }
            }
        });
        mPeoplePop.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });
        mPeoplePop.setAnimationStyle(R.style.picker_view_slide_anim);
        mPeoplePop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
    }

    /**
     * 设置popwindow默认背景变灰
     */
    public void setBackgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha;
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        getWindow().setAttributes(lp);
    }

    /**
     * 初始化弹窗数据
     */

    /**
     * 状态和分类popwindow
     */

    private void showTypePop() {
        View view = LayoutInflater.from(NewIssueActivity.this)
                .inflate(R.layout.popwindow_new_issue, null);
        typePop = new PopupWindow(NewIssueActivity.this);
        typePop.setContentView(view);
        typePop.setFocusable(true);
        typePop.setOutsideTouchable(true);
        typePop.setBackgroundDrawable(new BitmapDrawable());
        typePop.setHeight(LayoutParams.WRAP_CONTENT);
        typePop.setWidth(LayoutParams.MATCH_PARENT);
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = 0.5f;
        getWindow().setAttributes(lp);
        typeWheelView = view.findViewById(R.id.wheel_type);
        typeWheelView.setOffset(1);
        typeWheelView.setItems(wheelTypeList);

        tv_type = view.findViewById(R.id.tv_type);
        tvConfirm = view.findViewById(R.id.tv_confirm);
        tv_title_select = view.findViewById(R.id.tv_title_select);
        typePop.setAnimationStyle(R.style.picker_view_slide_anim);
        typePop.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });

        typePop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
        //初始化分类
        initTypeWheelView();
    }

    /**
     * 初始化分类轮滑
     */
    private void initTypeWheelView() {
        TypeSelected = wheelTypeList.get(0);
        TypeIdSelected = wheelTypeIDList.get(0);

        typeWheelView.setOnWheelViewListener(new OnWheelViewListener() {
            @Override
            public void onSelected(int selectedIndex, String item) {
                TypeSelected = item;
                TypeIdSelected = wheelTypeIDList.get(selectedIndex - 1);
            }
        });

        tvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                tv_type.setText(TypeSelected);
                tvType.setText(TypeSelected);
                tvType.setTextColor(getResources().getColor(R.color.black));
                typePop.dismiss();
            }
        });
    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getProjectID();
            bimProjectId = bean.getBimProjectId();
        }
    }

    /**
     * 获取可添加的问题参与人
     */
    private void GetProjectUser() {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ProjectID", organizeId);
        params.put("RoleId", "-1");
        IssueController controller = new IssueController();
        controller.getIssueJoiner(params, new CallBack<IssueJoinerResult>() {

            @Override
            public void onSuccess(IssueJoinerResult issueJoinerResult) {
                projectRoleList.clear();
                for (IssueJoinerResult.DataBean dataBean : issueJoinerResult.getData()) {
                    projectRoleList.add(dataBean);
                }
                problemJoinAdapter.notifyDataSetChanged();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 去除重复数据
     */
    public List<String> removeStringListDupli(List<String> stringList) {
        Set<String> set = new LinkedHashSet<>();
        set.addAll(stringList);

        stringList.clear();

        stringList.addAll(set);
        return stringList;
    }

    /**
     * 上传图片
     */
    private void UploadPhoto() {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        issueController.uploadIssuePhoto(photoFileList, params, new CallBack<IssueUploadPhotoResult>() {
            @Override
            public void onSuccess(IssueUploadPhotoResult issueUploadPhotoResult) {
                if (issueUploadPhotoResult.getRet() == 1) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (IssueUploadPhotoResult.DataBean dataBean : issueUploadPhotoResult.getData()) {
                        stringBuilder.append(dataBean.getBf_guid());
                        stringBuilder.append(",");
                    }
                    uploadFileId = stringBuilder.toString();
                    SaveIssue();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 新增问题
     */
    private void SaveIssue() {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("Title", titleResult);
        params.put("IssueTypeID", TypeIdSelected);
        params.put("RUserIDStr", StringUtils.dataToString(headIdList));
        params.put("JoinerIds", StringUtils.dataToString(joinIDList));
        params.put("EndDateStr", issueDate);
        params.put("Content", introResult);
        params.put("IsPublic", "");
        params.put("RealName", Hawk.get(CustomParam.RealName));

        if (!TextUtils.isEmpty(viewPointPositon)) {
            params.put("ModelId", modelId);
            params.put("ViewpointID", viewPointPositon);
            params.put("ImageUrl", viewBase64);
        }

        params.put("OrganizeId", organizeId);
        params.put("ImageIds", uploadFileId);

        issueController.SaveIssue(params, new CallBack<NewIssueResult>() {

            @Override
            public void onSuccess(NewIssueResult newIssueResult) {
                mLoading.dismiss();
                if (newIssueResult.getRet() == 1) {
                    showMsg("新建问题成功");
                    finish();
                } else {
                    showMsg("新建问题失败");
                    finish();
                }
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });
    }

    /**
     * 获取筛选条件
     */
    private void GetFilter() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", bimProjectId);
        params.put("token", Hawk.get(CustomParam.Token));
        issueController.getProblemType(params, new CallBack<IssueTypeDto>() {

            @Override
            public void onSuccess(IssueTypeDto issueTypeDto) {
                if (issueTypeDto.getRet() == 1) {
                    //分类数据
                    for (int i = 0; i < issueTypeDto.getData().size(); i++) {
                        wheelTypeList.add(issueTypeDto.getData().get(i).getItemName());
                        wheelTypeIDList.add(issueTypeDto.getData().get(i).getItemDetailId());
                    }
                }

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //获取视点信息
    private void getViewPoint(String vpId) {

        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", organizeId);
        params.put("ViewpointID", vpId);
        ModelController controller = new ModelController();
        controller.GetViewPointDetails(params, new CallBack<ModelPointViewDetails>() {
            @Override
            public void onSuccess(ModelPointViewDetails modelPointViewDetails) {
                if (!TextUtils.isEmpty(modelPointViewDetails.getLinks().get(0).getContent())
                        && modelPointViewDetails.getLinks().get(0).getContent()
                        .contains("base64")) {

                    Glide.with(NewIssueActivity.this)
                            .load(Base64Utils.decode(
                                    modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]))
                            .into(imgView);
                } else {

                    Glide.with(NewIssueActivity.this)
                            .load(R.mipmap.img_bg_project_list)
                            .into(imgView);
                }

                tvView.setText(modelPointViewDetails.getName());
                if (TextUtils.isEmpty(modelPointViewDetails.getTag().getDescription())) {
                    tvViewIntro.setText("<无描述>");
                } else {
                    tvViewIntro.setText(modelPointViewDetails.getTag().getDescription());
                }

                viewPointOrNtationphotoList.add(new PhotoDto(
                        modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]));
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //获取批注信息
    private void getNtation(String vpId) {

        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", organizeId);
        params.put("ViewpointID", vpId);
        ModelController controller = new ModelController();
        controller.GetNtationDetails(params, new CallBack<String>() {
            @Override
            public void onSuccess(String s) {
                displayResult(s);
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    private void displayResult(String s) {

        try {

            //将得到json数据转换为一个json对象
            JSONObject jsonObject = new JSONObject(s);
            //获取"Links"的json对象,并将其转换为一个json数组
            JSONArray jsonArray = jsonObject.getJSONArray("Links");
            //通过循环获取数据,并放入list集合中
            String img_url = jsonArray.getJSONObject(0).getString("Content");

            if (!TextUtils.isEmpty(img_url)
                    && img_url
                    .contains("base64")) {

                Glide.with(getApplicationContext())
                        .load(Base64Utils.decode(
                                img_url.split("base64,")[1]))
                        .into(imgView);

                viewPointOrNtationphotoList.add(new PhotoDto(img_url.split("base64,")[1]));
                DataHolder.getInstance().setData(viewPointOrNtationphotoList);
            } else {

                Glide.with(getApplicationContext())
                        .load(R.mipmap.img_bg_project_list)
                        .into(imgView);
            }

            PointViewDescModel model = new Gson()
                    .fromJson(jsonObject.getString("Tag"), PointViewDescModel.class);
            if (TextUtils.isEmpty(model.getDescription())) {
                tvViewIntro.setText("<无描述>");
            } else {
                tvViewIntro.setText(model.getDescription());
            }

            tvView.setText(jsonObject.getString("Name"));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //退出 置空
        MarkActivity.imgName = "";
        MarkActivity.img_url = "";
        MarkActivity.img2d_pos = "";
        MarkActivity.img2dId = "";
        MarkActivity.img2d_ModelId = "";
        MarkActivity.isCan = false;

        SelectModelViewActivity.IsCan = false;
        SelectModelViewActivity.ViewId = "";
        SelectModelViewActivity.ViewType = "";
        SelectModelViewActivity.ModelId = "";

        isViewPointSelected = false;
        isDrawingSelected = false;
        isFromWhere = false;
        viewBase64 = null;
    }

    public SSLContext getSSLContext() {
        try {
            // 生成SSLContext对象
            SSLContext sslContext = SSLContext.getInstance("TLS");
            // 从assets中加载证书
            InputStream inStream = getAssets().open("STAR_probim_cn.cer");

            // 证书工厂this
            CertificateFactory cerFactory = CertificateFactory.getInstance("X.509");
            Certificate cer = cerFactory.generateCertificate(inStream);

            // 密钥库
            KeyStore kStore = KeyStore.getInstance("PKCS12");//如果是运行在PC端，这里需要将PKCS12替换成JKS
            kStore.load(null, null);
            kStore.setCertificateEntry("trust", cer);// 加载证书到密钥库中

            // 密钥管理器
            KeyManagerFactory keyFactory =
                    KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyFactory.init(kStore, null);// 加载密钥库到管理器

            // 信任管理器
            TrustManagerFactory tFactory =
                    TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tFactory.init(kStore);// 加载密钥库到信任管理器

            // 初始化
            sslContext.init(keyFactory.getKeyManagers(), tFactory.getTrustManagers(), new SecureRandom());
            return sslContext;
        } catch (UnrecoverableKeyException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (CertificateException e) {
            e.printStackTrace();
        } catch (KeyStoreException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public void testOkHttp() {
        try {
            String path = "https://enzo.com";
            // 新建一个URL对象
            URL url = new URL(path);

            // 打开一个OkHttpClient连接
            SSLContext sc = getSSLContext();
            List<Protocol> protocols = new ArrayList<Protocol>();
            protocols.add(Protocol.HTTP_1_1);
            protocols.add(Protocol.HTTP_2);
            OkHttpClient client = new OkHttpClient.Builder().protocols(protocols)
                    .sslSocketFactory(sc.getSocketFactory())
                    .build();

            Request request = new Request.Builder().url(url).build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                Log.i("---wyf---", "Get方式请求成功，返回数据如下：");
                Log.i("---wyf---", response.body().string());
                Log.d("---wyf---", "Protocol: " + response.protocol());
            } else {
                Log.i("---wyf---", "Get方式请求失败:" + response);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
