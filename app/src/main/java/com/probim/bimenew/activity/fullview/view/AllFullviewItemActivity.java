package com.probim.bimenew.activity.fullview.view;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.fullview.adapter.AllFullviewItemAdapter;
import com.probim.bimenew.activity.fullview.controller.FullviewController;
import com.probim.bimenew.activity.fullview.dto.AllFullViewItemDTO;
import com.probim.bimenew.activity.pano.LoadPanoActivity;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.net.CallBack;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 全景图子集列表
 */
public class AllFullviewItemActivity extends BaseActivity implements View.OnClickListener, OnRefreshListener {
    List<AllFullViewItemDTO.DataDTO> allList = new ArrayList<>();
    private RecyclerView rvAllFullview;
    private AllFullviewItemAdapter allFullviewItemAdapter;
    private FullviewController fullviewController = new FullviewController();
    private EditText edtSearch;
    private String pb_guid;
    private String pb_name;
    private String pb_url;
    private String pb_organizeId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_fullview_item);
        initView();
        initRecycleview();

    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        if (getIntent() != null) {
            pb_name = getIntent().getStringExtra("pb_name");
            pb_guid = getIntent().getStringExtra("pb_guid");
            pb_url = getIntent().getStringExtra("pb_url");
            pb_organizeId = getIntent().getStringExtra("pb_organizeId");

        }
        SmartRefreshLayout smartRefreshLayout = findView(R.id.smartRefreshLayout);
        smartRefreshLayout.setOnRefreshListener(this);
        LinearLayout linBack = findView(R.id.lin_back, this);
        TextView tvTitle = findView(R.id.tv_title);
        tvTitle.setText(pb_name);
        rvAllFullview = findView(R.id.rv_all_fullview);
        edtSearch = findView(R.id.edt_search);
        edtSearch.addTextChangedListener(new MyTextWatcher());
        ImageView ivAddFullview = findView(R.id.iv_add_fullview, this);


    }

    @Override
    protected void initData() {
    }

    @Override
    protected void onResume() {
        super.onResume();
        getFullviewItemList();
    }

    @Override
    protected void initRecycleview() {
        allFullviewItemAdapter = new AllFullviewItemAdapter(allList, pb_name, pb_url);
        allFullviewItemAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                AllFullViewItemDTO.DataDTO dto = (AllFullViewItemDTO.DataDTO) o;
                Intent intent = new Intent(AllFullviewItemActivity.this, LoadPanoActivity.class);
                intent.putExtra(CustomParam.TvLeft, dto.getPchChname());
                intent.putExtra(CustomParam.LoadPano, pb_url);
                intent.putExtra(CustomParam.ProjectId, pb_organizeId);
                intent.putExtra(CustomParam.SceneId, dto.getPsScenename());
                intent.putExtra("isAll", false);
                startActivity(intent);
            }

            @Override
            public void OnDelete(int pos, Object o) {
                if (allList.size() == 1) {
                    showMsg("请在全景图集中删除！");
                    return;
                }
                //  删除单挑数据
                AllFullViewItemDTO.DataDTO dto = (AllFullViewItemDTO.DataDTO) o;
                deleteFullview(dto.getPbGuid(), dto.getPsScenename(), pos);
            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalNoItemRecyclerView.initialize(rvAllFullview).setAdapter(allFullviewItemAdapter);


    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.iv_add_fullview:
                Intent intent = new Intent(AllFullviewItemActivity.this, NewFullviewActivity.class);
                intent.putExtra("isAll", false);
                intent.putExtra("pbguid", pb_guid);
                startActivity(intent);
                break;
            default:
                break;
        }
    }

    /**
     * 删除全景图
     */
    private void deleteFullview(String id, String scenename, int pos) {

        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", Hawk.get(CustomParam.Token));
        parmas.put("pbguid", id);
        parmas.put("scenename", "scene_" + scenename);
        fullviewController.deleteItem(parmas, new CallBack<SchedualBaseResult>() {
            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                if (schedualBaseResult.getRet() == 1) {
                    allList.remove(pos);
                    allFullviewItemAdapter.notifyItemRemoved(pos);
                } else {
                    showMsg(schedualBaseResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /*
     * 获取子集列表 查询
     */
    private void getFullviewItemList() {
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", Hawk.get(CustomParam.Token));
        parmas.put("pbGuid", pb_guid);
        fullviewController.getFullviewItemList(parmas, new CallBack<AllFullViewItemDTO>() {
                    @Override
                    public void onSuccess(AllFullViewItemDTO allFullViewItemDTO) {
                        if (allFullViewItemDTO.getRet() == 1) {
                            if (!allList.isEmpty()) {
                                allList.clear();
                            }
                            if (!allFullViewItemDTO.getData().isEmpty()) {
                                allList.addAll(allFullViewItemDTO.getData());
                                allFullviewItemAdapter.notifyDataSetChanged();
                            }

                        } else {
                            showMsg(allFullViewItemDTO.getMsg());
                        }

                    }

                    @Override
                    public void onFail(String erroMsg) {

                    }
                }


        );
    }

    @Override
    public void onRefresh(RefreshLayout refreshLayout) {
        getFullviewItemList();
        refreshLayout.finishRefresh();
    }


    private class MyTextWatcher implements TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            //  pbName = s.toString();
            getFullviewItemList();

        }
    }
}