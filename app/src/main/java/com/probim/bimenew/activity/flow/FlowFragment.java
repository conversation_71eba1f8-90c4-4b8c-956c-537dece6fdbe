package com.probim.bimenew.activity.flow;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.AllFlowDto;
import com.probim.bimenew.dto.BIMeBaseDto;
import com.probim.bimenew.dto.HandleFlowDataDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.JsonHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FlowFragment extends Fragment {

    private RecyclerView recyclerView;
    private String projectID;
    private AllFlowAdapter cofigAdapter;
    private String type;

    private List<HandleFlowDataDto> allList = new ArrayList<>();
    private TextView tvNoData;

    public static FlowFragment newInstance(String type) {
        Bundle args = new Bundle();
        args.putString("type", type);
        FlowFragment fragment = new FlowFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_flow, container, false);
        recyclerView = view.findViewById(R.id.rv_config);
        tvNoData = view.findViewById(R.id.tv_no_data);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (getArguments() != null) {
            type = getArguments().getString("type");
        }
        initRecyclerView();
        getProjectDao();
        getCCFlow();
    }

    private void initRecyclerView() {
        cofigAdapter = new AllFlowAdapter(allList);
        cofigAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                HandleFlowDataDto dto = (HandleFlowDataDto) o;
                Intent intent = new Intent(getActivity(), AllFlowItemActivity.class);
                intent.putExtra("AllFlowDto", dto);
                startActivity(intent);
            }

            @Override
            public void OnDelete(int pos, Object o) {

            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalNoItemRecyclerView.initialize(recyclerView).setAdapter(cofigAdapter);
    }

    /**
     * 流程登录
     */
    private void getCCFlow() {
        FlowController controller = new FlowController();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("organizeId", projectID);
        controller.getCCFlow(params, new CallBack<BIMeBaseDto>() {
            @Override
            public void onSuccess(BIMeBaseDto biMeBaseDto) {
                if (biMeBaseDto.getRet() == 1) {
                    Hawk.put(CustomParam.CCToken, biMeBaseDto.getData());
                    getFlowList(biMeBaseDto.getData());
                }

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取流程列表
     */
    private void getFlowList(String ccToken) {
        FlowController controller = new FlowController();
        HashMap<String, String> params = new HashMap<>();
        if (type.equals("Todolist_Init")) {
            params.put("Token", ccToken);
            params.put("organizeId", projectID);
            params.put("FK_Dept", projectID);
        } else {
            params.put("Token", ccToken);
            params.put("FK_Dept", projectID);
        }
        controller.getFlowList(type, params, new CallBack<List<AllFlowDto>>() {
            @Override
            public void onSuccess(List<AllFlowDto> allFlowDtos) {
                handleData(allFlowDtos);
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 处理分组数据
     *
     * @param list
     */
    private void handleData(List<AllFlowDto> list) {
        Map<String, List<AllFlowDto>> mapData = new HashMap<>();

        if (!list.isEmpty()) {

            for (AllFlowDto dto : list) {
                AllFlowDto temp = dto;
                if (type.equals("Todolist_Init")) {
                    if (mapData.containsKey(temp.getNodeName())) {
                        List<AllFlowDto> list1 = mapData.get(temp.getNodeName());
                        list1.add(temp);
                        mapData.replace(temp.getNodeName(), list1);
                    } else {
                        List<AllFlowDto> list1 = new ArrayList<>();
                        list1.add(temp);
                        mapData.put(temp.getNodeName(), list1);
                    }
                } else {
                    if (mapData.containsKey(temp.getFlowName())) {
                        List<AllFlowDto> list1 = mapData.get(temp.getFlowName());
                        list1.add(temp);
                        mapData.replace(temp.getFlowName(), list1);
                    } else {
                        List<AllFlowDto> list1 = new ArrayList<>();
                        list1.add(temp);
                        mapData.put(temp.getFlowName(), list1);
                    }
                }

            }

            for (Map.Entry<String, List<AllFlowDto>> entry : mapData.entrySet()) {
                HandleFlowDataDto dto = new HandleFlowDataDto();
                dto.setName(entry.getKey());
                dto.setType(type);
                dto.setData(entry.getValue());
                allList.add(dto);
            }
            if (allList.isEmpty()) {
                tvNoData.setVisibility(View.VISIBLE);
            }
            cofigAdapter.notifyDataSetChanged();
        }
    }


    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            projectID = bean.getProjectID();
            // schedualOrganizeId = bean.getBimProjectId();

        }
    }
}
