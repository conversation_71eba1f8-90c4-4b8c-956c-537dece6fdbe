package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.Comments2RvAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.model.Comments2;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.SpacesItemDecoration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :评论界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/27/16:48.
 */
public class CommentsActivity3 extends BaseActivity {

  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.img_right)
  ImageView imgRight;
  @BindView(R.id.rv_comments)
  RecyclerView rvComments;
  @BindView(R.id.iv_status)
  ImageView ivStatus;
  @BindView(R.id.tv_status)
  TextView tvStatus;
  @BindView(R.id.rv_status_layout)
  RelativeLayout rvStatusLayout;
  @BindView(R.id.tv_right)
  TextView tvRight;
  private IssueController issueController;
  private List<Comments2.DataBean> dataBeanList = new ArrayList<>();
  private Comments2RvAdapter commentsRvAdapter;
  private String organizeId;
  private String issueId;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_comments);
    ButterKnife.bind(this);
    initView();
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {
    tvTitle.setText("评论");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("新增评论");
    tvStatus.setText("暂无评论");
    ivStatus.setImageResource(R.mipmap.ic_comments_empty);
    //
    if (getIntent() != null) {
      issueId = getIntent().getStringExtra(CustomParam.IssueId);
      organizeId = getIntent().getStringExtra(CustomParam.OrganizeId);
      initRecycleview();
      issueController = new IssueController();
    }
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {
    //为 RecyclerView 设置布局管理器
    rvComments.setLayoutManager(new LinearLayoutManager(this));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    //rvComments.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    //动画
    rvComments.setItemAnimator(new DefaultItemAnimator());

    rvComments.addItemDecoration(new SpacesItemDecoration(20));
    //    rvComments.getRecycledViewPool().setMaxRecycledViews(VIEWHOLDERTYPE, 0);
    commentsRvAdapter = new Comments2RvAdapter(this, dataBeanList, organizeId);

    commentsRvAdapter.addRecycleItemListener(new Comments2RvAdapter.OnRecycleItemListener() {
      @Override public void OnItemReply(int pos, Object o) {
        String talkId = (String) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.IssueId, issueId);
        bundle.putString(CustomParam.CommentsTalkId, talkId);
        Intent intent = new Intent(CommentsActivity3.this, NewCommentsActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
      }

      @Override public void OnInnerItemReply(int pos, Object o) {
        String talkId = (String) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.IssueId, issueId);
        bundle.putString(CustomParam.CommentsTalkId, talkId);
        Intent intent = new Intent(CommentsActivity3.this, NewCommentsActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
      }

      @Override public void OnItemDelete(int pos, Object o) {

        String messageId = (String) o;
        deleteComments(messageId, pos);
      }

      @Override public void OnInnerItemDelete(int pos, Object o) {
        String messageId = (String) o;
        deleteComments(messageId, pos);
      }
    });

    rvComments.setAdapter(commentsRvAdapter);
  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({ R.id.lin_back, R.id.tv_right })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.IssueId, issueId);
        Intent intent = new Intent(this, NewCommentsActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        break;
    }
  }

  /**
   * 获取评论列表
   */
  private void GetCommentsSzie(String id) {
    HashMap<String, String> params = new HashMap<>();
    params.put("IssueId", id);
    issueController.GetCommentsList2(params, new CallBack<Comments2>() {
      @Override public void onSuccess(Comments2 comments2) {
        dataBeanList.addAll(comments2.getData());
        commentsRvAdapter.notifyDataSetChanged();
        if (dataBeanList.isEmpty()) {
          rvStatusLayout.setVisibility(View.VISIBLE);
        }
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 删除评论
   */
  private void deleteComments(String messageId, int position) {
    HashMap<String, String> params = new HashMap<>();
    params.put("MessageID", messageId);
    params.put("userId", Hawk.get(CustomParam.UserId));
    params.put("issueId", issueId);
    /*issueController.DeleteComments(params, new CallBack<String>() {
      @Override
      public void onSuccess(String s) {
        if ("Success".equals(s)) {
          showMsg("删除评论成功");
          dataBeanList.remove(position);
          commentsRvAdapter.notifyItemRemoved(position);
        } else {
          showMsg("删除评论失败");
        }
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });*/
  }

  @Override
  protected void onResume() {
    super.onResume();
    dataBeanList.clear();
    GetCommentsSzie(issueId);
  }
}
