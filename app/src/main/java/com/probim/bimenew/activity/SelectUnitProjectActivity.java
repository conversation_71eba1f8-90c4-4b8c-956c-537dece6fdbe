package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.SelectUnitProjectAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.model.NewCheckConfig;
import com.probim.bimenew.net.CallBack;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :单位工程
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/10:57.
 */

public class SelectUnitProjectActivity extends BaseActivity {
  @BindView(R.id.iv_back) ImageView ivBack;
  @BindView(R.id.tv_left) TextView tvLeft;
  @BindView(R.id.lin_back) LinearLayout linBack;
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.tv_right) TextView tvRight;
  @BindView(R.id.img_right) ImageView imgRight;
  @BindView(R.id.rv_check_record) RecyclerView rvCheckRecord;
  @BindView(R.id.iv_status) ImageView ivStatus;
  @BindView(R.id.tv_status) TextView tvStatus;
  @BindView(R.id.rv_status_layout) RelativeLayout rvStatusLayout;
  private String projectID;
  private List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX> list =
      new ArrayList<>();
  private SelectUnitProjectAdapter adapter;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_uinit_project);
    ButterKnife.bind(this);
    initRecycleview();
    initView();
  }

  @Override protected void loadData() {

  }

  @Override protected void initView() {
    if (getIntent() != null) {
      GetProjectDao();
      tvTitle.setText("单位工程");
      tvLeft.setText("返回");
      getCheckConfig();
    }
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {
    rvCheckRecord.setLayoutManager(new LinearLayoutManager(this));

    DividerItemDecoration itemDecoration =
        new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
    rvCheckRecord.addItemDecoration(itemDecoration);

    rvCheckRecord.setItemAnimator(new DefaultItemAnimator());

    adapter = new SelectUnitProjectAdapter(this, list);

    adapter.addRecycleItemListener(new SelectUnitProjectAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        //保存单位工程下面的数据
        List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX>
            childrenBeanXXX =
            (List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX>) o;

        Hawk.put(CustomParam.DanWeiSelected, childrenBeanXXX);


        //传递数据---->单位工程名字
        Intent intent = new Intent();
        intent.putExtra(CustomParam.StartForResult, list.get(pos));
        setResult(RESULT_OK, intent);
        finish();
      }
    });
    rvCheckRecord.setAdapter(adapter);
  }

  @Override protected void initRefresh() {

  }

  @OnClick({ R.id.lin_back, R.id.img_right }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.img_right:
        break;
    }
  }

  /**
   * 获取新建数据
   */
  private void getCheckConfig() {
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectID);
    CheckController checkController = new CheckController();
    checkController.getNewCheckConfig(params, new CallBack<NewCheckConfig>() {
      @Override public void onSuccess(NewCheckConfig newCheckConfig) {
        for (int i = 0; i < newCheckConfig.getQualitysecurity().size(); i++) {
          if (newCheckConfig.getQualitysecurity().get(i).getTitle().equalsIgnoreCase("工程组织")) {

            list.addAll(newCheckConfig.getQualitysecurity().get(i).getChildren().get(0).getItems());
          }
        }
        adapter.notifyDataSetChanged();
        Hawk.put(CustomParam.CheckConfig, newCheckConfig.getQualitysecurity());
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }
  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      projectID = bean.getProjectID();
    }
  }

}
