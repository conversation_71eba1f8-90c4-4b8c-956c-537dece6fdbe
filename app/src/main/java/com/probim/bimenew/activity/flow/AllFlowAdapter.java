package com.probim.bimenew.activity.flow;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.dto.AllFlowDto;
import com.probim.bimenew.dto.HandleFlowDataDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.model.ModelStages;

import java.util.ArrayList;
import java.util.List;

public class AllFlowAdapter extends RecyclerView.Adapter<AllFlowAdapter.ViewHolder> {

    private List<HandleFlowDataDto> mDatas = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;

    public AllFlowAdapter(List<HandleFlowDataDto> mDatas) {
        this.mDatas = mDatas;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_flow, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        HandleFlowDataDto dto = mDatas.get(position);
        holder.tvName.setText(dto.getName());
        holder.tvSize.setText(dto.getData().size() + "");
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onClick(position, dto);
            }
        });

    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvName;
        private final TextView tvSize;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tv_flow_name);
            tvSize = itemView.findViewById(R.id.tv_size);
        }
    }
}
