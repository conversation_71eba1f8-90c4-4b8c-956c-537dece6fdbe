package com.probim.bimenew.activity.fullview.view.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentResultListener;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.JsonParser;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.fullview.adapter.ModelPointAdapter;
import com.probim.bimenew.activity.fullview.dto.OriginViewPointDTO;
import com.probim.bimenew.activity.fullview.dto.ViewPointDTO;
import com.probim.bimenew.activity.fullview.inters.ViewpointClickListener;
import com.probim.bimenew.activity.fullview.view.NewFullviewActivity;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.model.ModelViewPointModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.JsonHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ModelPointFragment extends Fragment {

    private String projectID;
    private String modelId;
    private RecyclerView recyclerView;
    private List<ModelViewPointModel> beanList = new ArrayList<>();
    private ModelPointAdapter modelPointAdapter;
    private ViewpointClickListener viewpointClickListener;

    public static ModelPointFragment newInstance(String label) {
        Bundle args = new Bundle();
        args.putString("label", label);
        ModelPointFragment fragment = new ModelPointFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_config, container, false);
        recyclerView = view.findViewById(R.id.rv_config);
        initRecyclerView();
        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getParentFragmentManager().setFragmentResultListener("model", this, new FragmentResultListener() {
            @Override
            public void onFragmentResult(@NonNull String key, @NonNull Bundle bundle) {
                // We use a String here, but any type that can be put in a Bundle is supported
                NewFullviewActivity.repalce(2);
                modelId = bundle.getString("modelId");
                Logger.e(modelId);
                // Do something with the result...
                viewpointClickListener.onSelectViewpoint("", "");
                getViewPoint();
            }
        });
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        getProjectDao();

    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        viewpointClickListener = (ViewpointClickListener) context;
    }

    private void initRecyclerView() {
        modelPointAdapter = new ModelPointAdapter(beanList);
        modelPointAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                ModelViewPointModel dto = (ModelViewPointModel) o;
                String view_point = JsonParser.parseString(dto.getOverride()).toString();
                OriginViewPointDTO originViewPointDTO = new Gson().fromJson(view_point, OriginViewPointDTO.class);
                ViewPointDTO viewPointDTO = new ViewPointDTO();
                viewPointDTO.setPos(new ViewPointDTO.PosDTO(originViewPointDTO.getCameraPosition().getX(), originViewPointDTO.getCameraPosition().getY(), originViewPointDTO.getCameraPosition().getZ()));
                viewPointDTO.setTarget(new ViewPointDTO.TargetDTO(originViewPointDTO.getControlPostion().getX(), originViewPointDTO.getControlPostion().getY(), originViewPointDTO.getControlPostion().getZ()));
                viewPointDTO.setFocalOffset(new ViewPointDTO.FocalOffsetDTO(originViewPointDTO.getFocalOffset().getX(), originViewPointDTO.getFocalOffset().getY(), originViewPointDTO.getFocalOffset().getZ()));
                viewpointClickListener.onSelectViewpoint(dto.getName(), JsonHelper.toJson(viewPointDTO));
                modelPointAdapter.setSelectPosition(pos);
                modelPointAdapter.notifyDataSetChanged();
            }

            @Override
            public void OnDelete(int pos, Object o) {

            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalNoItemRecyclerView.initialize(recyclerView).setAdapter(modelPointAdapter);
    }

    /**
     * 获取视点列表
     */
    private void getViewPoint() {
        ModelController controller = new ModelController();
        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", projectID);
        params.put("ModelID", modelId);
        controller.GetViewPoint(params, new CallBack<List<ModelViewPointModel>>() {
            @Override
            public void onSuccess(List<ModelViewPointModel> modelViewPointModels) {
                if (!beanList.isEmpty()) {
                    beanList.clear();
                }

               /* //遍历list
                for (int i = 0; i < modelViewPointModels.size(); i++) {
                    //type==0 视点, 1是批注
                    if (modelViewPointModels.get(i).getType() == 1) {
                        beanList.add(modelViewPointModels.get(i));
                    }
                }*/
                beanList.addAll(modelViewPointModels);
                modelPointAdapter.notifyDataSetChanged();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            projectID = bean.getProjectID();
            // schedualOrganizeId = bean.getBimProjectId();

        }
    }
}
