package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.webkit.WebView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/10/9:53.
 */
public class BaseWebActivity extends BaseActivity {

  @BindView(R.id.tv_black_title)
  TextView tvTitle;
  @BindView(R.id.web_x)
  WebView webX;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_web);
    ButterKnife.bind(this);
    StatusBarUtils.transparencyBar(this);
    initView();
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {
    if (getIntent() != null) {
      tvTitle.setText("BIMe用户协议");
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      //      String doc_url = bundle.getString(CustomParam.DocUrl);
      String agreement = bundle.getString(CustomParam.WebAgreemrntUrl);
      webX.loadUrl(agreement);
    }
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick(R.id.lin_back)
  public void onViewClicked() {
    finish();
  }
}
