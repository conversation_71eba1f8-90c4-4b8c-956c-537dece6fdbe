package com.probim.bimenew.activity.fullview.pop;

import android.app.Activity;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;

public class PopWindowUtils {

    private static Activity mActivity;
    private static int layoutID;
    private static PopupWindow mPop;


    public PopWindowUtils(Activity mActivity, int layoutID) {
        this.mActivity = mActivity;
        this.layoutID = layoutID;
    }

    public static void show() {
        View view = LayoutInflater.from(mActivity)
                .inflate(R.layout.fragment_fullview_label, null);
        setBackgroundAlpha(0.5f);
        if (mPop == null) {
            mPop = new PopupWindow(mActivity);
        }
        if (!mPop.isShowing()) {
            mPop.setContentView(view);
            mPop.setFocusable(true);
            mPop.setOutsideTouchable(true);
            mPop.setBackgroundDrawable(new ColorDrawable());
            mPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
            mPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
            RecyclerView rvPeopleType = view.findViewById(R.id.rv_label);
            // VerticalRecyclerView.initialize(rvPeopleType).setAdapter(allLabelAdapter);
            mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    setBackgroundAlpha(1.0f);
                }
            });
            mPop.setAnimationStyle(R.style.picker_view_slide_anim);
            mPop.showAtLocation((ViewGroup) mActivity.findViewById(android.R.id.content), Gravity.BOTTOM, 0, 0);
        }
    }


    /**
     * 设置popwindow默认背景变灰
     */
    public static void setBackgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = mActivity.getWindow().getAttributes();
        lp.alpha = bgAlpha;
        mActivity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        mActivity.getWindow().setAttributes(lp);
    }

}
