package com.probim.bimenew.activity.flow;


import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.text.InputType;
import android.util.Log;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.AllFlowDto;
import com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback;
import com.tencent.smtt.export.external.interfaces.JsPromptResult;
import com.tencent.smtt.export.external.interfaces.JsResult;
import com.tencent.smtt.export.external.interfaces.WebResourceRequest;
import com.tencent.smtt.export.external.interfaces.WebResourceResponse;
import com.tencent.smtt.sdk.ValueCallback;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebSettings;
import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;

import pub.devrel.easypermissions.EasyPermissions;

public class X5WebViewActivity extends AppCompatActivity implements EasyPermissions.PermissionCallbacks {
    private final String flowUrl = Hawk.get(CustomParam.Flow_URL);
    private WebView mWebView;
    public static final String TAG = X5WebViewActivity.class.getSimpleName();
    private String[] permissons = new String[]{
            Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_x5webview);
//        getPermission();
        initWebView();
    }

    /**
     * 自定义初始化WebView设置，此处为默认 BaseWebViewActivity 初始化
     * 可通过继承该 Activity Override 该方法做自己的实现
     */
    protected void initWebView() {
        TextView tvTittle = findViewById(R.id.tv_title);
        LinearLayout linBack = findViewById(R.id.lin_back);
        linBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mWebView = findViewById(R.id.x5_webView);
        mWebView.getX5WebViewExtension().setHorizontalScrollBarEnabled(true);
        //启用或禁用水平滚动条
//        mWebView.getX5WebViewExtension().setHorizontalScrollBarEnabled (true);
        //启用或禁用竖直滚动条
//        mWebView.getX5WebViewExtension().setVerticalScrollBarEnabled (true);
        WebSettings webSetting = mWebView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setAllowFileAccess(true);
        webSetting.setSupportZoom(true);
        webSetting.setDatabaseEnabled(true);
        webSetting.setAllowFileAccess(true);
        webSetting.setDomStorageEnabled(true);
        initWebViewClient();
        initWebChromeClient();
        initJavaScriptInterface();
//        String url = "http://workflow-api.probim.cn/WF/MyFlowGener.htm?WorkID=1040&NodeID=6502&FK_Node=6502&FID=0&UserNo=lfd001&Token=03e49a37-28e3-4fed-aa66-3edbba26b61e&FK_Flow=065&PWorkID=0&IsRead=1&Paras=1&IsCanBatch=0&LastTruckID=229548527&ThreadCount=0&ScripNodeID=0&ScripMsg=&HungupSta=-1&HungupCheckMsg=&PageName=MyFlowGener";
        //参数1：Java对象名 参数2：Javascript对象名
        //通过addJavascriptInterface() AJavaScriptInterface类对象映射到JS的mjs对象
        mWebView.addJavascriptInterface(this, "android");
        // http://***********:8080
        if (getIntent() != null) {
            //读取数据
            AllFlowDto dto = (AllFlowDto) getIntent().getSerializableExtra("AllFlowDto");
            tvTittle.setText(dto.getTitle());
            String atPara = dto.getAtPara().replaceAll("@", "&");
            String token = Hawk.get(CustomParam.CCToken);
            String account = Hawk.get(CustomParam.ACCOUNT);
            String url = flowUrl + "/WF/MyFlowGener.htm?WorkID=" + dto.getWorkID() + "&NodeID=" + dto.getFK_Node() + "&FK_Node=" + dto.getFK_Node() + "&FID=" + dto.getFID() + "&UserNo=" + account + "&Token=" + token + "&FK_Flow=" + dto.getFK_Flow() + "&PWorkID=" + dto.getPWorkID() + "&IsRead=" + "0" + "&Paras=1" + atPara + "&PageName=MyFlowGener";
            mWebView.loadUrl(url);
            Logger.t("流程地址").e(url);

        }
    }

    // 定义JS需要调用的方法，被JS调用的方法必须加入@JavascriptInterface注解
    @JavascriptInterface
    public void rotateScreen() {
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
//        Toast.makeText(this,"开始旋转屏幕", Toast.LENGTH_LONG).show();
    }

    @JavascriptInterface
    public void cancelRotateScreen() {
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
//        Toast.makeText(this,"取消旋转屏幕", Toast.LENGTH_LONG).show();
    }

    private void initWebViewClient() {
        mWebView.setWebViewClient(new WebViewClient() {

            /**
             * 具体接口使用细节请参考文档：
             * https://x5.tencent.com/docs/webview.html
             * 或 Android WebKit 官方：
             * https://developer.android.com/reference/android/webkit/WebChromeClient
             */

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                Log.i(TAG, "onPageStarted, view:" + view + ", url:" + url);
//                mUrlEditText.setText(url);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                Log.i(TAG, "onPageFinished, view:" + view + ", url:" + url);
//                changGoForwardButton(view);
            }

            @Override
            public void onReceivedError(WebView webView, int errorCode, String description, String failingUrl) {
                Log.e(TAG, "onReceivedError: " + errorCode
                        + ", description: " + description
                        + ", url: " + failingUrl);
            }

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest webResourceRequest) {
                if (webResourceRequest.getUrl().toString().contains("debugdebug")) {
                    InputStream in = null;
                    Log.i("AterDebug", "shouldInterceptRequest");
                    try {
                        in = new FileInputStream(new File("/sdcard/1.png"));
                    } catch (Exception e) {

                    }

                    return new WebResourceResponse("image/*", "utf-8", in);
                } else {
                    return super.shouldInterceptRequest(webView, webResourceRequest);
                }

            }
        });
    }

    private void initWebChromeClient() {
        final Context context = this;
        final Activity activity = this;
        mWebView.setWebChromeClient(new WebChromeClient() {
            /**
             * 具体接口使用细节请参考文档：
             * https://x5.tencent.com/docs/webview.html
             * 或 Android WebKit 官方：
             * https://developer.android.com/reference/android/webkit/WebChromeClient
             */

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                Log.i(TAG, "onProgressChanged, newProgress:" + newProgress + ", view:" + view);
//                changGoForwardButton(view);
            }

            @Override
            public boolean onJsAlert(WebView webView, String url, String message, JsResult result) {
                new AlertDialog.Builder(context).setTitle("JS弹窗Override")
                        .setMessage(message)
                        .setPositiveButton("OK", (dialogInterface, i) -> result.confirm())
                        .setCancelable(false)
                        .show();
                return true;
            }

            @Override
            public boolean onJsConfirm(WebView webView, String url, String message, JsResult result) {
                new AlertDialog.Builder(context).setTitle("JS弹窗Override")
                        .setMessage(message)
                        .setPositiveButton("OK", (dialogInterface, i) -> result.confirm())
                        .setNegativeButton("Cancel", (dialogInterface, i) -> result.cancel())
                        .setCancelable(false)
                        .show();
                return true;
            }

            @Override
            public boolean onJsBeforeUnload(WebView webView, String url, String message, JsResult result) {
                new AlertDialog.Builder(context).setTitle("页面即将跳转")
                        .setMessage(message)
                        .setPositiveButton("OK", (dialogInterface, i) -> result.confirm())
                        .setNegativeButton("Cancel", (dialogInterface, i) -> result.cancel())
                        .setCancelable(false)
                        .show();
                return true;
            }

            @Override
            public boolean onJsPrompt(WebView webView, String url, String message, String defaultValue, JsPromptResult result) {
                final EditText input = new EditText(context);
                input.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
                new AlertDialog.Builder(context).setTitle("JS弹窗Override")
                        .setMessage(message)
                        .setView(input)
                        .setPositiveButton("OK", (dialogInterface, i) -> result.confirm(input.getText().toString()))
                        .setCancelable(false)
                        .show();
                return true;
            }

            /**
             * Return value usage see FILE_CHOOSE_REQUEST in
             * {@link (int, int, Intent)}
             */
            @Override
            public boolean onShowFileChooser(WebView webView,
                                             ValueCallback<Uri[]> filePathCallback,
                                             FileChooserParams fileChooserParams) {
                return true;
            }

            @Override
            public void onGeolocationPermissionsShowPrompt(String origin,
                                                           GeolocationPermissionsCallback geolocationPermissionsCallback) {

            }
        });
    }

    private void initJavaScriptInterface() {
        final Activity context = this;
        mWebView.addJavascriptInterface(new WebViewJavaScriptFunction() {
            @Override
            public void onJsFunctionCalled(String tag) {

            }

            @JavascriptInterface
            public void openQRCodeScan() {
//                new IntentIntegrator(context).initiateScan();
            }

            @JavascriptInterface
            public void openDebugX5() {
//                mWebView.loadUrl("http://debugx5.qq.com");
            }

            @JavascriptInterface
            public void openWebkit() {
//                startActivity(new Intent(context, SystemWebViewActivity.class));
            }


        }, "Android");
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    private void getPermission() {

        if (EasyPermissions.hasPermissions(this, permissons)) {

            initWebView();
        } else {

            EasyPermissions.requestPermissions(this, "权限申请",
                    0, permissons);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {

        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }

    //下面两个方法是实现EasyPermissions的EasyPermissions.PermissionCallbacks接口
    //分别返回授权成功和失败的权限
    @Override
    public void onPermissionsGranted(int requestCode, List<String> perms) {
        initWebView();
    }

    @Override
    public void onPermissionsDenied(int requestCode, List<String> perms) {

    }
}
