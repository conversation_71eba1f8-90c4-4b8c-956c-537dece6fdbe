package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class MobileJson {

    @SerializedName("Plan_json")
    private List<SchedualNewTaskPlanDTO> plan_json;
    @SerializedName("NoPlan_json")
    private List<SchedualNewTaskNoPlanDTO> noPlan_json;

    public MobileJson() {
    }

    public List<SchedualNewTaskPlanDTO> getPlan_json() {
        return plan_json;
    }

    public void setPlan_json(List<SchedualNewTaskPlanDTO> plan_json) {
        this.plan_json = plan_json;
    }

    public List<SchedualNewTaskNoPlanDTO> getNoPlan_json() {
        return noPlan_json;
    }

    public void setNoPlan_json(List<SchedualNewTaskNoPlanDTO> noPlan_json) {
        this.noPlan_json = noPlan_json;
    }
}
