package com.probim.bimenew.activity.schedule.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :工程结构适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class SchedualTaskAdapter
        extends RecyclerView.Adapter<SchedualTaskAdapter.ViewHolder> {

    private List<SchedualListDto.DataDTO.ChildrenDTO> dtoList = new ArrayList<>();
    private OnRecycleItemListener mRecycleItemListener;
    private List<Boolean> isClicks = new ArrayList<>();

    public SchedualTaskAdapter() {

    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_item_task, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SchedualListDto.DataDTO.ChildrenDTO bean = dtoList.get(position);
        holder.tvName.setText(bean.getName());
        if (bean.getSummary() == 1) {
            holder.ivSelected.setVisibility(View.INVISIBLE);
            holder.ivNext.setVisibility(View.VISIBLE);
        } else {
            holder.ivSelected.setVisibility(View.VISIBLE);
            holder.ivNext.setVisibility(View.INVISIBLE);
        }
        boolean bl = isClicks.get(position);
        if (bl) {
            Glide.with(BaseApp.getContext()).load(R.mipmap.select_circle).into(holder.ivSelected);
        } else {
            Glide.with(BaseApp.getContext()).load(R.mipmap.no_select_circle).into(holder.ivSelected);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    boolean bl = isClicks.get(position);
                    if (bl) {
                        // 如果已经选中 取消选中
                        isClicks.set(position, false);
                        Glide.with(BaseApp.getContext()).load(R.mipmap.no_select_circle).into(holder.ivSelected);
                        notifyDataSetChanged();
                    } else {
                        // 未选中的状态 重置所有数据为false
                        for (int i = 0; i < isClicks.size(); i++) {
                            isClicks.set(i, false);
                        }
                        isClicks.set(position, true);
                        Glide.with(BaseApp.getContext()).load(R.mipmap.select_circle).into(holder.ivSelected);
                        notifyDataSetChanged();
                    }

                    mRecycleItemListener.OnRecycleItemClick(position, bean, isClicks.get(position));
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void addDatas(List<SchedualListDto.DataDTO.ChildrenDTO> list) {
        isClicks.clear();
        this.dtoList = list;
        for (int i = 0; i < dtoList.size(); i++) {
            isClicks.add(false);
        }
        notifyDataSetChanged();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o, boolean bl);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_material)
        TextView tvName;
        @BindView(R.id.tv_code)
        TextView tvCode;
        @BindView(R.id.ivSelected)
        ImageView ivSelected;
        @BindView(R.id.iv_next)
        ImageView ivNext;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
