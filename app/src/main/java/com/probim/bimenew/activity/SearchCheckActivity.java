package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.CheckDetailsActivity;
import com.probim.bimenew.adapter.CheckAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.CheckListBean;
import com.probim.bimenew.result.CheckListResult;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/3/1/14:49.
 */

public class SearchCheckActivity extends BaseActivity {
  @BindView(R.id.rv_check) RecyclerView rvCheck;
  @BindView(R.id.lin_rv_status_layout) LinearLayout linRvStatusLayout;
  @BindView(R.id.edt_search) EditText edtSearch;
  private CheckController checkController;
  private String organizeId;
  private CheckAdapter checkAdapter;
  private List<CheckListBean.DataBean.ListBean> dataList = new ArrayList<>();

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_search_check);
    ButterKnife.bind(this);
    initView();
    initRecycleview();
  }

  @Override protected void loadData() {
  }

  @Override protected void initView() {
    edtSearch.setHint("  请输入现场数据名称");
    edtSearch.setFocusable(true);
    edtSearch.setFocusableInTouchMode(true);
    edtSearch.requestFocus();
    //显示软键盘
    getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
    GetProjectDao();

    checkController = new CheckController();

    setListener();
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

    // 为 RecyclerView 设置布局管理器
    rvCheck.setLayoutManager(new LinearLayoutManager(this));
    // 为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    DividerItemDecoration divider =
        new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
    divider.setDrawable(
        ContextCompat.getDrawable((SearchCheckActivity.this), R.drawable.custom_divider));
    rvCheck.addItemDecoration(divider);
    // 动画
    rvCheck.setItemAnimator(new DefaultItemAnimator());

    checkAdapter = new CheckAdapter(this, dataList);

    rvCheck.setAdapter(checkAdapter);

    checkAdapter.addRecycleItemListener(new CheckAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        Intent intent = new Intent(SearchCheckActivity.this, CheckDetailsActivity.class);
        intent.putExtra(CustomParam.ExamineTittle,
            checkAdapter.getData().get(pos).getExamineRemark());
        intent.putExtra(CustomParam.Examineid, checkAdapter.getData().get(pos).getExamineID());
        intent.putExtra(CustomParam.ExaminerId, checkAdapter.getData().get(pos).getExaminerID());
        startActivity(intent);
      }

      @Override public void OnDelete(int pos, Object o) {
        CheckListResult.DataBean.ListBean dataBean = (CheckListResult.DataBean.ListBean) o;
        if (dataBean.isExamineMgr()) {
          deleteListItem(checkAdapter.getData().get(pos).getExamineID(), pos);
        } else {
          showMsg("暂无权限删除");
        }
      }

      @Override
      public void OnClose(int pos, Object o) {

      }
    });
  }

  @Override protected void initRefresh() {

  }

  private void searchCheck(String str) {
    checkAdapter.clear();
    HashMap<String, String> params = new HashMap<>();
    params.put("BIMComposerId", organizeId);
    params.put("KeyWord", str);
    params.put("Token", Hawk.get(CustomParam.Token));
    params.put("ExamineResult", "");
    params.put("aede_examinetype", "");
    params.put("aede_severitylevel", "");
    checkController.getCheckList(params, new CallBack<CheckListBean>() {

      @Override public void onSuccess(CheckListBean checkListResult) {
        if (!checkListResult.getData().getList().isEmpty()) {
          linRvStatusLayout.setVisibility(View.GONE);
          checkAdapter.setData(checkListResult.getData().getList());
        } else {
          linRvStatusLayout.setVisibility(View.VISIBLE);
        }
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 删除检查
   */
  private void deleteListItem(String id, int position) {
    HashMap<String, String> params = new HashMap<>();
    params.put("Ids", id);
    params.put("Token", Hawk.get(CustomParam.Token));

  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      organizeId = bean.getProjectID();
    }
  }

  //添加输入框监听以及键盘搜索按键监听
  private void setListener() {

    //键盘搜索键
    edtSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
      @Override
      public boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
        if (i == EditorInfo.IME_ACTION_SEARCH) {

          searchCheck(edtSearch.getText().toString().trim());
          return true;
        }
        return false;
      }
    });
  }

  @OnClick(R.id.tv_cancle) public void onViewClicked() {
    finish();
  }
}
