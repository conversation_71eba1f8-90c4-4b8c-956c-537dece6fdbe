package com.probim.bimenew.activity.fullview.view.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentResultListener;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.fullview.adapter.ModelAdapter;
import com.probim.bimenew.activity.fullview.inters.ViewpointClickListener;
import com.probim.bimenew.activity.fullview.view.NewFullviewActivity;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.net.CallBack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ModelFragment extends Fragment {

    private String projectID;
    private String result;
    private RecyclerView recyclerView;
    private List<ModelSatgeListModel> beanList = new ArrayList<>();
    private ModelAdapter modelAdapter;
    private ViewpointClickListener viewpointClickListener;

    public static ModelFragment newInstance(String label) {
        Bundle args = new Bundle();
        args.putString("label", label);
        ModelFragment fragment = new ModelFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_config, container, false);
        recyclerView = view.findViewById(R.id.rv_config);
        initRecyclerView();
        return view;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getParentFragmentManager().setFragmentResultListener("config", this, new FragmentResultListener() {
            @Override
            public void onFragmentResult(@NonNull String key, @NonNull Bundle bundle) {
                // We use a String here, but any type that can be put in a Bundle is supported
                NewFullviewActivity.repalce(1);
                result = bundle.getString("configId");
                // Do something with the result...
                viewpointClickListener.onSelectModel("","");
                getModelList();
            }
        });
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        getProjectDao();

    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        viewpointClickListener = (ViewpointClickListener) context;
    }

    private void initRecyclerView() {
        modelAdapter = new ModelAdapter(beanList);
        modelAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                ModelSatgeListModel dto = (ModelSatgeListModel) o;
                modelAdapter.setSelectPosition(pos);
                modelAdapter.notifyDataSetChanged();
                viewpointClickListener.onSelectModel(dto.getFeatureName(),dto.getFeatureID());
/*
                ModelSatgeListModel dto = (ModelSatgeListModel) o;
                Bundle result = new Bundle();
                result.putString("configId", dto.getValue());
                getParentFragmentManager().setFragmentResult("config", result);*/

                Bundle result = new Bundle();
                result.putString("modelId", dto.getFeatureID());
                getParentFragmentManager().setFragmentResult("model", result);
            }

            @Override
            public void OnDelete(int pos, Object o) {

            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalNoItemRecyclerView.initialize(recyclerView).setAdapter(modelAdapter);
    }

    /**
     * 获取模型列表
     */
    private void getModelList() {
        ModelController controller = new ModelController();
        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", projectID);
        params.put("Phase", result);
        controller.GetStageList(params, new CallBack<List<ModelSatgeListModel>>() {
            @Override
            public void onSuccess(List<ModelSatgeListModel> modelSatgeListModels) {
                if (!beanList.isEmpty()) {
                    beanList.clear();
                }
                beanList.addAll(modelSatgeListModels);
                modelAdapter.notifyDataSetChanged();

            }

            @Override
            public void onFail(String erroMsg) {
            }
        });

    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            projectID = bean.getProjectID();
            // schedualOrganizeId = bean.getBimProjectId();

        }
    }
}
