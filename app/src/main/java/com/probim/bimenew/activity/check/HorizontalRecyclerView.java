package com.probim.bimenew.activity.check;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.probim.bimenew.application.BaseApp;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/10/15/16:29.
 * horizontal
 */

public class HorizontalRecyclerView {

  public static RecyclerView initialize(RecyclerView recyclerView) {


    recyclerView.setLayoutManager(new LinearLayoutManager(BaseApp.getContext(),0,false) {
      @Override public RecyclerView.LayoutParams generateDefaultLayoutParams() {
        RecyclerView.LayoutParams layoutParams =
            new RecyclerView.LayoutParams(RecyclerView.LayoutParams.WRAP_CONTENT,
                RecyclerView.LayoutParams.MATCH_PARENT);
        return layoutParams;
      }
    });

    //recyclerView.addItemDecoration(
    //    new DividerItemDecoration(BaseApp.getContext(), DividerItemDecoration.HORIZONTAL));
    //动画
    recyclerView.setItemAnimator(new DefaultItemAnimator());
    return recyclerView;
  }
}
