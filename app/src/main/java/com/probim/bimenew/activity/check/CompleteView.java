package com.probim.bimenew.activity.check;

import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import com.dueeeke.videoplayer.controller.ControlWrapper;
import com.dueeeke.videoplayer.controller.IControlComponent;
import com.dueeeke.videoplayer.player.VideoView;
import com.dueeeke.videoplayer.util.PlayerUtils;

/**
 * 自动播放完成界面
 */
public class CompleteView extends FrameLayout implements IControlComponent {

  private ControlWrapper mControlWrapper;

  private ImageView mStopFullscreen;

  public CompleteView(@NonNull Context context) {
    super(context);
  }

  public CompleteView(@NonNull Context context, @Nullable AttributeSet attrs) {
    super(context, attrs);
  }

  public CompleteView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
  }

  {
    setVisibility(GONE);
    LayoutInflater.from(getContext())
        .inflate(com.dueeeke.videocontroller.R.layout.dkplayer_layout_complete_view, this, true);
    findViewById(
        com.dueeeke.videocontroller.R.id.iv_replay).setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View v) {
        mControlWrapper.replay(true);
      }
    });
    mStopFullscreen = findViewById(com.dueeeke.videocontroller.R.id.stop_fullscreen);
    mStopFullscreen.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View v) {
        if (mControlWrapper.isFullScreen()) {
          Activity activity = PlayerUtils.scanForActivity(getContext());
          if (activity != null && !activity.isFinishing()) {
            activity.finish();
          }
        }
      }
    });
    setClickable(true);
  }

  @Override
  public void attach(@NonNull ControlWrapper controlWrapper) {
    mControlWrapper = controlWrapper;
  }

  @Override
  public View getView() {
    return this;
  }

  @Override
  public void onVisibilityChanged(boolean isVisible, Animation anim) {

  }

  @Override
  public void onPlayStateChanged(int playState) {
    if (playState == VideoView.STATE_PLAYBACK_COMPLETED) {
      setVisibility(VISIBLE);
      mStopFullscreen.setVisibility(mControlWrapper.isFullScreen() ? VISIBLE : GONE);
      bringToFront();
    } else {
      setVisibility(GONE);
    }
  }

  @Override
  public void onPlayerStateChanged(int playerState) {
    if (playerState == VideoView.PLAYER_FULL_SCREEN) {
      mStopFullscreen.setVisibility(VISIBLE);
    } else if (playerState == VideoView.PLAYER_NORMAL) {
      mStopFullscreen.setVisibility(GONE);
    }

    Activity activity = PlayerUtils.scanForActivity(getContext());
    if (activity != null && mControlWrapper.hasCutout()) {
      int orientation = activity.getRequestedOrientation();
      int cutoutHeight = mControlWrapper.getCutoutHeight();
      LayoutParams sflp = (LayoutParams) mStopFullscreen.getLayoutParams();
      if (orientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
        sflp.setMargins(0, 0, 0, 0);
      } else if (orientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
        sflp.setMargins(cutoutHeight, 0, 0, 0);
      } else if (orientation == ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE) {
        sflp.setMargins(0, 0, 0, 0);
      }
    }
  }

  @Override
  public void setProgress(int duration, int position) {

  }

  @Override
  public void onLockStateChanged(boolean isLock) {

  }
}
