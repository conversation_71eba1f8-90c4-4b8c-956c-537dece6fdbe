package com.probim.bimenew.activity.fullview.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class FullviewLableDTO {

    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("LabelId")
        private String labelId;
        @SerializedName("CreateTime")
        private String createTime;
        @SerializedName("ModifyTime")
        private String modifyTime;
        @SerializedName("DelMark")
        private boolean delMark;
        @SerializedName("EnableMark")
        private boolean enableMark;
        @SerializedName("OrganizeId")
        private String organizeId;
        @SerializedName("BackGroundColor")
        private String backGroundColor;
        @SerializedName("FontColor")
        private String fontColor;
        @SerializedName("LabelName")
        private String labelName;
        @SerializedName("LabelSort")
        private int labelSort;

        public String getLabelId() {
            return labelId;
        }

        public void setLabelId(String labelId) {
            this.labelId = labelId;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getModifyTime() {
            return modifyTime;
        }

        public void setModifyTime(String modifyTime) {
            this.modifyTime = modifyTime;
        }

        public boolean isDelMark() {
            return delMark;
        }

        public void setDelMark(boolean delMark) {
            this.delMark = delMark;
        }

        public boolean isEnableMark() {
            return enableMark;
        }

        public void setEnableMark(boolean enableMark) {
            this.enableMark = enableMark;
        }

        public String getOrganizeId() {
            return organizeId;
        }

        public void setOrganizeId(String organizeId) {
            this.organizeId = organizeId;
        }

        public String getBackGroundColor() {
            return backGroundColor;
        }

        public void setBackGroundColor(String backGroundColor) {
            this.backGroundColor = backGroundColor;
        }

        public String getFontColor() {
            return fontColor;
        }

        public void setFontColor(String fontColor) {
            this.fontColor = fontColor;
        }

        public String getLabelName() {
            return labelName;
        }

        public void setLabelName(String labelName) {
            this.labelName = labelName;
        }

        public int getLabelSort() {
            return labelSort;
        }

        public void setLabelSort(int labelSort) {
            this.labelSort = labelSort;
        }
    }
}
