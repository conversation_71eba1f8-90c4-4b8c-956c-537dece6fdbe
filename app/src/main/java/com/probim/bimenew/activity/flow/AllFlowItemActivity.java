package com.probim.bimenew.activity.flow;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.dto.AllFlowDto;
import com.probim.bimenew.dto.HandleFlowDataDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;

import java.util.List;

public class AllFlowItemActivity extends BaseActivity implements View.OnClickListener {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_flow_item);
        initView();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        TextView tvTittle = findView(R.id.tv_title);
        LinearLayout linBack = findView(R.id.lin_back, this);
        RecyclerView rvItem = findView(R.id.rv_all_flow_item);
        if (getIntent() != null) {
            HandleFlowDataDto dto = (HandleFlowDataDto) getIntent().getSerializableExtra("AllFlowDto");
            tvTittle.setText(dto.getName());
            List<AllFlowDto> allFlowDtos = dto.getData();
            AllFlowItemAdapter allFlowItemAdapter = new AllFlowItemAdapter(allFlowDtos);
            allFlowItemAdapter.setOnItemClickListener(new IOnItemClickListener() {
                @Override
                public void onClick(int pos, Object o) {
                    AllFlowDto dto = (AllFlowDto) o;
                    Intent intent = new Intent(AllFlowItemActivity.this, LoadFlowActivity.class);
                    intent.putExtra("AllFlowDto", dto);
                    startActivity(intent);
                }

                @Override
                public void OnDelete(int pos, Object o) {

                }

                @Override
                public void OnClose(int pos, Object o) {

                }
            });
            VerticalNoItemRecyclerView.initialize(rvItem).setAdapter(allFlowItemAdapter);
        }
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        finish();
    }
}
