package com.probim.bimenew.activity.schedule.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.ShowTypeDto;

import java.util.List;

public class SpinnerStringAdapter extends BaseAdapter {
    private List<ShowTypeDto> dataList;


    public SpinnerStringAdapter(List<ShowTypeDto> dataList) {
        this.dataList = dataList;
    }

    @Override
    public int getCount() {
        return dataList.size();
    }

    @Override
    public Object getItem(int i) {
        return dataList.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(parent.getContext()).inflate(R.layout.spinner_plan, parent, false);
        }
        ShowTypeDto dto  = dataList.get(position);
        TextView textView = convertView.findViewById(R.id.tv_plan_name);
        textView.setText(dto.getName());
        return convertView;
    }
}
