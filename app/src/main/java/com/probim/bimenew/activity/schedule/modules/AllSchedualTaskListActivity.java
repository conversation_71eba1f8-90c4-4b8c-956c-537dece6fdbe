package com.probim.bimenew.activity.schedule.modules;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;


import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.schedule.adapter.SchedualTaskAdapter;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.api.CustomParam;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :任务列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class AllSchedualTaskListActivity extends BaseActivity implements View.OnClickListener {


    private RecyclerView rvTask;
    private List<SchedualListDto.DataDTO.ChildrenDTO> dtoList = new ArrayList<>();
    private SchedualListDto.DataDTO.ChildrenDTO selectedDto;
    private StringBuilder clickItemStr = new StringBuilder();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_project_structure);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        initRecycleview();
        loadData();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        SchedualListDto.DataDTO schedualPlanDto = (SchedualListDto.DataDTO) getIntent().getSerializableExtra("plan-children");
        dtoList.addAll(schedualPlanDto.getChildren());
        TextView tvTittle = findViewById(R.id.tv_black_title);
        tvTittle.setText("任务列表");
        TextView tvRight = findView(R.id.tv_right, this);
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("完成");
        LinearLayout liniBack = findView(R.id.lin_back, this);
        rvTask = findView(R.id.rv_project_structure);
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

        SchedualTaskAdapter schedualTaskAdapter = new SchedualTaskAdapter();
        schedualTaskAdapter.addDatas(dtoList);
        schedualTaskAdapter.addRecycleItemListener(new SchedualTaskAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o, boolean bl) {
                if (dtoList.get(pos).getChildren() != null) {
                    // 数据有子数据
                    if (!dtoList.get(pos).getChildren().isEmpty()) {
                        clickItemStr.append(dtoList.get(pos).getName());
                        clickItemStr.append("——>");
                        List<SchedualListDto.DataDTO.ChildrenDTO> tempTasks = new ArrayList<>();
                        tempTasks.addAll(dtoList.get(pos).getChildren());
                        dtoList.clear();
                        dtoList.addAll(tempTasks);
                        schedualTaskAdapter.addDatas(dtoList);
                    }

                } else {
                    // 数据无子数据 点击的数据回传
                    if (bl) {
                        selectedDto = dtoList.get(pos);
                        clickItemStr.append(selectedDto.getName());

                    }
                }
            }

        });
        VerticalRecyclerView.initialize(rvTask).setAdapter(schedualTaskAdapter);

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.lin_back:
                finish();
                //backStepToList();

                break;
            case R.id.tv_right:
                if (selectedDto != null) {
                    Intent intent = new Intent(AllSchedualTaskListActivity.this, NewSchedualTaskActivty.class);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomParam.StartForResult, (Serializable) selectedDto);
                    bundle.putString(CustomParam.PLAN_ITEM_TOTAL, clickItemStr.toString());
                    intent.putExtras(bundle);
                    setResult(RESULT_OK, intent);
//                    Logger.t("层级数据").e(clickItemStr.toString());
                    finish();
                } else {
                    showMsg("请选择一个任务");
                }
                break;
        }
    }


}
