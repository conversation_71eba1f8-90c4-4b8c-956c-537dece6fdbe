package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SchedualTaskDetailsDTO {

    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private DataDTO data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("Image_Json")
        private List<SchedualNewPhotoDto> image_Json;
        @SerializedName("Plan_json")
        private List<SchedualNewTaskPlanDTO> plan_json;
        @SerializedName("NoPlan_json")
        private List<SchedualNewTaskNoPlanDTO> noPlan_json;

        public List<SchedualNewTaskPlanDTO> getPlan_json() {
            return plan_json;
        }

        public void setPlan_json(List<SchedualNewTaskPlanDTO> plan_json) {
            this.plan_json = plan_json;
        }

        public List<SchedualNewTaskNoPlanDTO> getNoPlan_json() {
            return noPlan_json;
        }

        public void setNoPlan_json(List<SchedualNewTaskNoPlanDTO> noPlan_json) {
            this.noPlan_json = noPlan_json;
        }

        public List<SchedualNewPhotoDto> getImage_Json() {
            return image_Json;
        }

        public void setImage_Json(List<SchedualNewPhotoDto> image_Json) {
            this.image_Json = image_Json;
        }
    }


}
