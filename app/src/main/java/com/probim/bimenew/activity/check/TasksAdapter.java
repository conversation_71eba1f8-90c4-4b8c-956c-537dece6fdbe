package com.probim.bimenew.activity.check;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;

import com.probim.bimenew.R;
import com.probim.bimenew.result.CheckDetailsDto;

import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/8/19/11:36
 */
public class TasksAdapter extends RecyclerView.Adapter<TasksAdapter.ViewHolder> {
    private Context mContex;
    private List<CheckDetailsDto.DataBean.ListBean.TasksBean> mDatas;

    private OnItemDeleteListener mRecycleItemListener;

    public TasksAdapter(Context mContex, List<CheckDetailsDto.DataBean.ListBean.TasksBean> mDatas) {
        this.mContex = mContex;
        this.mDatas = mDatas;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ViewHolder(
                LayoutInflater.from(mContex).inflate(R.layout.item_rv_tasks, null, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        CheckDetailsDto.DataBean.ListBean.TasksBean dto = mDatas.get(i);
        viewHolder.tvTaskName.setText(dto.getNAME_());
        viewHolder.ivDelete.setVisibility(View.VISIBLE);
        viewHolder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    mRecycleItemListener.OnDeleteClick(i, dto);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_task_name)
        TextView tvTaskName;
        @BindView(R.id.iv_delete)
        ImageView ivDelete;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnItemDeleteListener<T> {

        void OnDeleteClick(int pos, T o);
    }

    public void addRecycleItemDeleteListener(OnItemDeleteListener listener) {
        this.mRecycleItemListener = listener;
    }
}
