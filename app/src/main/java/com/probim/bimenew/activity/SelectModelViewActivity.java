package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.ModelPointViewDetails;
import com.probim.bimenew.model.PointViewDescModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.Base64Utils;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Description :模型--->视点详情
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/16/15:11.
 */
public class SelectModelViewActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.tv_view_name)
  TextView tvViewName;
  @BindView(R.id.tv_view_intro)
  TextView tvViewIntro;
  @BindView(R.id.tv_view_create)
  TextView tvViewCreate;
  @BindView(R.id.tv_view_update)
  TextView tvViewUpdate;
  @BindView(R.id.btn_loading)
  Button btnLoading;
  @BindView(R.id.img)
  ImageView img;
  @BindView(R.id.img_right)
  ImageView imgRight;
  @BindView(R.id.tv_default)
  TextView tvDefault;
  private String projectId;
  public String viewId = "";
  public String modelId = "";
  private String moldeViewId;
  List<PhotoDto> photoDtoList = new ArrayList<>();
  private String title;
  private String type;
  public static String ModelId = "";
  public static String ViewId = "";
  public static String ViewType = "";
  public static boolean IsCan = false;

  @Override

  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_model_point_view);
    ButterKnife.bind(this);
    loadData();
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      projectId = bundle.getString(CustomParam.ProjectId);
      viewId = bundle.getString(CustomParam.ViewId);
      modelId = bundle.getString(CustomParam.ModelId);

      title = bundle.getString(CustomParam.ViewPointName);
      tvTitle.setText(title);
      btnLoading.setText("确认");
      type = bundle.getString(CustomParam.WhoType);

      if (type.equalsIgnoreCase(0 + "")) {
        //视点
        displayViewPointResult();
      } else if (type.equalsIgnoreCase("" + 1)) {
        //批注
        displayNtationResult();
      }
    }
  }

  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({ R.id.iv_back, R.id.lin_back, R.id.btn_loading, R.id.img_right, R.id.img })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.btn_loading:
        //确认返货新建界面

        if (type.equals("0")) {
          ViewType = "视点";
        } else {

          ViewType = "批注";
        }
        ModelId = modelId;
        ViewId = viewId;
        IsCan = true;
       /* Hawk.put(CustomParam.SelectViewName, title);
        Hawk.put(CustomParam.SelectedModelIdForNewQuestion, modelId);
        Hawk.put(CustomParam.SelectedViewIdForNewQuestion, viewId);
        if (type.equals("0")) {
          Hawk.put(CustomParam.SelectedType, "视点");
        }*/
       CustomParam.WhereModifyCheck = false;
        Hawk.put(CustomParam.SelectViewName,title);
        Hawk.put(CustomParam.SelectedModelIdForNewQuestion, modelId);
        Hawk.put(CustomParam.SelectedViewIdForNewQuestion, viewId);
        finish();
        break;
      case R.id.img_right:

        break;
      case R.id.img:
        Bundle bundle = new Bundle();
        bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
        Intent intent =
            new Intent(SelectModelViewActivity.this, PhotoViewForPointViewActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        break;
    }
  }

  /**
   * 批注详情
   */
  private void displayNtationResult() {

    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectId);
    params.put("ViewpointID", viewId);
    ModelController controller = new ModelController();

    controller.GetNtationDetails(params, new CallBack<String>() {
      @Override
      public void onSuccess(String s) {
        try {

          //将得到json数据转换为一个json对象
          JSONObject jsonObject = new JSONObject(s);
          //获取"Links"的json对象,并将其转换为一个json数组
          JSONArray jsonArray = jsonObject.getJSONArray("Links");
          //通过循环获取数据,并放入list集合中
          String img_url = jsonArray.getJSONObject(0).getString("Content");

          if (!TextUtils.isEmpty(img_url)
              && img_url
              .contains("base64")) {

            Glide.with(SelectModelViewActivity.this)
                .load(Base64Utils.decode(
                    img_url.split("base64,")[1]))
                .into(img);
          } else {

            Glide.with(SelectModelViewActivity.this)
                .load(R.mipmap.img_bg_project_list)
                .into(img);
          }
          PointViewDescModel model = new Gson()
              .fromJson(jsonObject.getString("Tag"), PointViewDescModel.class);
          if (TextUtils.isEmpty(model.getDescription())) {
            tvViewIntro.setText("<无描述>");
          } else {
            tvViewIntro.setText(model.getDescription());
          }

          tvViewName.setText(jsonObject.getString("Name"));
          tvViewCreate.setText(jsonObject.getString("Editor"));
          tvViewUpdate.setText(jsonObject.getString("EditTime").replace("T", "  "));
          moldeViewId = jsonObject.getString("ViewID");
        } catch (JSONException e) {
          e.printStackTrace();
        }
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 视点详情
   */

  private void displayViewPointResult() {

    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectId);
    params.put("ViewpointID", viewId);
    ModelController controller = new ModelController();
    controller.GetViewPointDetails(params, new CallBack<ModelPointViewDetails>() {
      @Override
      public void onSuccess(ModelPointViewDetails modelPointViewDetails) {
        if (!TextUtils.isEmpty(modelPointViewDetails.getLinks().get(0).getContent())
            && modelPointViewDetails.getLinks().get(0).getContent()
            .contains("base64")) {

          Glide.with(getApplicationContext())
              .load(Base64Utils.decode(
                  modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]))
              .into(img);

          photoDtoList
              .add(new PhotoDto(
                  modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]));
        } else {

          Glide.with(getApplicationContext())
              .load(R.mipmap.img_bg_project_list)
              .into(img);
        }

        tvViewName.setText(modelPointViewDetails.getName());
        if (TextUtils.isEmpty(modelPointViewDetails.getTag().getDescription())) {
          tvViewIntro.setText("<无描述>");
        } else {
          tvViewIntro.setText(modelPointViewDetails.getTag().getDescription());
        }
        tvViewCreate.setText(modelPointViewDetails.getEditor());
        tvViewUpdate.setText(modelPointViewDetails.getEditTime().replace("T", "  "));

        if (modelPointViewDetails.isIsDefault()) {

          tvDefault.setVisibility(View.VISIBLE);
        } else {

          tvDefault.setVisibility(View.INVISIBLE);
        }

        moldeViewId = modelPointViewDetails.getViewID();
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  @Override protected void onDestroy() {
    super.onDestroy();
    //ViewId = "";
    //ModelId = "";
    //ViewType = "";
    //IsCan = false;
  }
}
