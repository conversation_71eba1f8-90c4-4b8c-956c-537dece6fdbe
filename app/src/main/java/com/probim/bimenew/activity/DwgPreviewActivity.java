package com.probim.bimenew.activity;

import android.graphics.PointF;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Nullable;
import android.util.DisplayMetrics;
import android.view.MotionEvent;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.opendesign.android.TeighaDWGJni;
import com.opendesign.android.TeighaDwgView;
import com.probim.bimenew.R;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/10/24/17:05.
 */


public class DwgPreviewActivity extends BaseActivity {

    @BindView(R.id.view_cad)
    TeighaDwgView viewCad;
    @BindView(R.id.action_marker)
    TextView action_marker;
    @BindView(R.id.tzBZD)
    RelativeLayout tzBZD;
    private boolean isBZ_mode = false;
    private Handler mPdHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            viewCad.onLoad();
            mLoading.dismiss();
        }
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_tuzhi);
        ButterKnife.bind(this);
        initView();

    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        tzBZD.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
//				Toast.makeText(TeighaDwgActivity.this, "点击标注", Toast.LENGTH_SHORT).show();
                isBZ_mode = !isBZ_mode;
                showMarkBackground(isBZ_mode);
                if (isBZ_mode) {
                    //TODO 该模式下进行标注操作

                }

            }
        });


        TeighaDWGJni.init();
        if (getIntent() != null) {
            mLoading.show();
            String file_path = getIntent().getStringExtra("file");
            new Thread(new Runnable() {
                @Override
                public void run() {
                    TeighaDWGJni.open(file_path);
                    mPdHandler.sendEmptyMessage(0);
                }
            }).start();

        }
    }

    @Override
    protected void initData() {

    }

    @Override
    public void finalize() {
    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void onPause() {
        super.onPause();
        viewCad.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        viewCad.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (isFinishing()) {
            viewCad.onDestroy();
            TeighaDWGJni.close();
            TeighaDWGJni.finit();
        }
    }


    // we can be in one of these 3 states
    private static final int NONE = 0;
    private static final int DRAG = 1;
    private static final int ZOOM = 2;
    private static final int ORBIT = 3;
    private int mTouchMode = NONE;
    // remember some things for zooming
    private PointF mTouchStart = new PointF();
    private PointF mTouchMid = new PointF();
    private float mTouchOldDist = 1f;
    private float mTouchOldRot = 0f;
    private float[] mTouchLastEvent = null;
    private long mTouchLastTime = -1;

    /**
     * Determine the space between the first two fingers
     */
    private float spacing(MotionEvent event) {
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) Math.sqrt(x * x + y * y);
    }

    /**
     * Calculate the mid point of the first two fingers
     */
    private void midPoint(PointF point, MotionEvent event) {
        float x = event.getX(0) + event.getX(1);
        float y = event.getY(0) + event.getY(1);
        point.set(x / 2, y / 2);
    }

    /**
     * Calculate the degree to be rotated by.
     *
     * @param event
     * @return Degrees
     */
    private float rotation(MotionEvent event) {
        double delta_x = (event.getX(0) - event.getX(1));
        double delta_y = (event.getY(0) - event.getY(1));
        double radians = Math.atan2(delta_y, delta_x);
        return (float) Math.toDegrees(radians);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                if (viewCad.onToolbarClick(event.getX(), event.getY()))
                    return true;
                long thisTime = System.currentTimeMillis();
                mTouchStart.set(event.getX(), event.getY());
                if (thisTime - mTouchLastTime < 250 && mTouchMode == NONE && TeighaDWGJni.viewCanRotate()) {
                    // Double click
                    mTouchMode = ORBIT;
                    mTouchLastTime = -1;
                } else {
                    mTouchMode = DRAG;
                    mTouchLastTime = thisTime;
                }
                mTouchLastEvent = null;
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                mTouchOldDist = spacing(event);
                if (mTouchOldDist > 10f) {
                    midPoint(mTouchMid, event);
                    mTouchMode = ZOOM;
                }
                mTouchLastEvent = new float[4];
                mTouchLastEvent[0] = event.getX(0);
                mTouchLastEvent[1] = event.getX(1);
                mTouchLastEvent[2] = event.getY(0);
                mTouchLastEvent[3] = event.getY(1);
                mTouchOldRot = rotation(event);
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_POINTER_UP:
                mTouchMode = NONE;
                mTouchLastEvent = null;
                break;
            case MotionEvent.ACTION_MOVE:
                if (mTouchMode == DRAG) {
                    float dx = event.getX() - mTouchStart.x;
                    float dy = event.getY() - mTouchStart.y;
                    TeighaDWGJni.viewTranslate(dx, dy);
                    mTouchStart.x += dx;
                    mTouchStart.y += dy;
                } else if (mTouchMode == ORBIT) {
                    float dx = event.getX() - mTouchStart.x;
                    float dy = event.getY() - mTouchStart.y;
                    final DisplayMetrics displayMetrics = new DisplayMetrics();
                    getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
                    float density = displayMetrics.density;
                    TeighaDWGJni.viewOrbit((float) Math.toRadians(dx / density / 2), (float) Math.toRadians(dy / density / 2));
                    mTouchStart.x += dx;
                    mTouchStart.y += dy;
                } else if (mTouchMode == ZOOM) {
                    float newDist = spacing(event);
                    if (newDist > 10f) {
                        float scale = (newDist / mTouchOldDist);
                        TeighaDWGJni.viewScale(scale);
                        mTouchOldDist = newDist;
                    }
                    if (mTouchLastEvent != null && event.getPointerCount() == 3) {
                        float newRot = rotation(event);
                        float r = newRot - mTouchOldRot;
                        if (TeighaDWGJni.viewCanRotate())
                            TeighaDWGJni.viewRotate((float) Math.toRadians(r));
                        mTouchOldRot = newRot;
                    }
                }
                break;
        }
        return true;
    }

    private void showMarkBackground(boolean isMark) {
        if (isMark) {
            action_marker.setBackgroundResource(R.drawable.loaction_normal);
        } else {
            action_marker.setBackgroundResource(R.drawable.menu_marker);
        }
    }
}
