package com.probim.bimenew.activity;

import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.adapter.CheckRecordRvAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.result.CheckDetailsDto;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Description :检查记录界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/24/16:35.
 */

//申请复检权利    0
//负责人    1

public class CheckRecordListActivity extends BaseActivity {
    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.lin_back)
    LinearLayout linBack;
    @BindView(R.id.tv_black_title)
    TextView tvTitle;
    @BindView(R.id.tv_right)
    TextView tvRight;
    @BindView(R.id.rv_check_record)
    RecyclerView rvCheckRecord;
    @BindView(R.id.iv_status)
    ImageView ivStatus;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    private String organizeId;
    private String examineid;
    private CheckController checkController;
    private CheckRecordRvAdapter checkRecordRvAdapter;
    private MediaPlayer mediaPlayer;
    private int checkType;
    private CheckDetailsDto checkDetailsDto;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_check_record);
        TranslucentUtils.setTRANSLUCENT(this);
        ButterKnife.bind(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {

        if (getIntent() != null) {
            //获取复检记录
            checkDetailsDto =
                    (CheckDetailsDto) getIntent().getSerializableExtra(CustomParam.CheckRecordList);

            initView();
            //初始化流转记录列表
            initRecycleview();
        }
    }


    /**
     * 获取复检记录
     */

    @Override
    protected void initView() {
        tvTitle.setText("整改记录");
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        checkRecordRvAdapter =
                new CheckRecordRvAdapter(this, checkDetailsDto.getData().getList().getRecordWithAttachments(),
                        organizeId);
        VerticalNoItemRecyclerView.initialize(rvCheckRecord).setAdapter(checkRecordRvAdapter);

        checkRecordRvAdapter.addRecycleItemListener(new CheckRecordRvAdapter.OnRecycleItemListener() {
            @Override
            public void OnPlay(String url) {
/*
        String uuid = UUID.randomUUID().toString().replace("-", "");

        DownloadUtil.get().download(url, "BIMeFile", uuid,
            new DownloadUtil.OnDownloadListener() {
              @Override public void onDownloadSuccess(File docFile) {
                new Thread(new Runnable() {
                  @Override
                  public void run() {

                    playMedia(docFile.getAbsolutePath());
                  }
                }).start();
              }

              @Override public void onDownloading(int progress) {

              }

              @Override public void onDownloadFailed() {

              }
            });*/
            }
        });
    }

    @Override
    protected void initRefresh() {

    }

    @OnClick({R.id.lin_back, R.id.tv_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.tv_right:
                //创建人----复检权利---0

                if (checkType == 1) {
                    Intent intent = new Intent(this, ReviewCheck2Activity.class);
                    intent.putExtra(CustomParam.Examineid, examineid);
                    intent.putExtras(intent);
                    startActivity(intent);
                } else if (checkType == 0) {
                    Intent intent = new Intent(this, ReviewCheckActivity.class);
                    intent.putExtra(CustomParam.Examineid, examineid);
                    intent.putExtras(intent);
                    startActivity(intent);
                }
                break;
        }
    }
}
