package com.probim.bimenew.activity;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.event.EventBusUtils;
import com.probim.bimenew.interfaces.PermissionListener;
import com.probim.bimenew.utils.BindEventBus;
import com.probim.bimenew.utils.loading.LoadingProgressDialog;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;

/**
 * Description :Base基类
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/6/12/16:49.
 */
public abstract class BaseActivity extends AppCompatActivity {

    public static List<Activity> mActivities = new LinkedList<Activity>();
    private static Activity mCurrentActivity;// 对所有activity进行管理
    public PermissionListener mPermissionListener;
    public LoadingProgressDialog mLoading;
    public String organizeId;
    public String schedualOrganizeId;
    public boolean isSchedualDetials;
    private BaseApp app;

    /**
     * 退出应用的方法
     */
    public static void exitApp() {

        ListIterator<Activity> iterator = mActivities.listIterator();

        while (iterator.hasNext()) {
            Activity next = iterator.next();
            next.finish();
        }
    }

    public static Activity getCurrentActivity() {
        return mCurrentActivity;
    }

/*

  //得到当前界面的布局文件id(由子类实现)
  protected abstract int provideContentViewId();
*/

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 若使用BindEventBus注解，则绑定EventBus
        if (this.getClass().isAnnotationPresent(BindEventBus.class)) {
            EventBusUtils.register(this);
        }

   /* setContentView(provideContentViewId());
    ButterKnife.bind(this);
    //初始化数据
    initData();*/
        //初始化的时候将其添加到集合中
        //StatusBarUtils.StatusBarLightMode(this);
        synchronized (mActivities) {
            mActivities.add(this);
        }
        app = (BaseApp) getApplicationContext();

        // mLoading = LoadingView.createLoading(this);
        mLoading = new LoadingProgressDialog(this);


        getProjectDao();

        if (getIntent() != null) {
            isSchedualDetials = getIntent().getBooleanExtra(CustomParam.INTENT_PARAMS, false);
        }


    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        // TODO Auto-generated method stub
        super.onWindowFocusChanged(hasFocus);
        //initData();

    }

    //返回重复加载数据
    protected abstract void loadData();

    protected abstract void initView();

    //只加载一次加载数据
    protected abstract void initData();

    protected abstract void initRecycleview();

    protected abstract void initRefresh();

    @Override
    protected void onResume() {
        super.onResume();
        mCurrentActivity = this;
    }

    @Override
    protected void onPause() {
        super.onPause();
        mCurrentActivity = null;
    }

    //清理数据（可以放在Base类的onDestory中）
    public void removeAnim() {
        if (mLoading != null) {
            mLoading.removeAnim();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removeAnim();
        //销毁的时候从集合中移除
        synchronized (mActivities) {
            mActivities.remove(this);
        }

        // 若使用BindEventBus注解，则解绑定EventBus
        if (this.getClass().isAnnotationPresent(BindEventBus.class)) {
            EventBusUtils.unregister(this);
        }

    }

    @Override
    public void onBackPressed() {

        // Fragment 逐个出栈
        int count = getSupportFragmentManager().getBackStackEntryCount();
        if (count == 0) {
            super.onBackPressed();
        } else {
            getSupportFragmentManager().popBackStack();
        }
        super.onBackPressed();
    }

    /**
     * 申请运行时权限
     */
    public void requestRuntimePermission(String[] permissions,
                                         PermissionListener permissionListener) {
        mPermissionListener = permissionListener;
        List<String> permissionList = new ArrayList<>();
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission)
                    != PackageManager.PERMISSION_GRANTED) {
                permissionList.add(permission);
            }
        }

        if (!permissionList.isEmpty()) {
            ActivityCompat
                    .requestPermissions(this, permissionList.toArray(new String[permissionList.size()]), 1);
        } else {
            permissionListener.onGranted();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case 1:
                if (grantResults.length > 0) {
                    List<String> deniedPermissions = new ArrayList<>();
                    for (int i = 0; i < grantResults.length; i++) {
                        int grantResult = grantResults[i];
                        String permission = permissions[i];
                        if (grantResult != PackageManager.PERMISSION_GRANTED) {
                            deniedPermissions.add(permission);
                        }
                    }
                    if (deniedPermissions.isEmpty()) {
                        mPermissionListener.onGranted();
                    } else {
                        mPermissionListener.onDenied(deniedPermissions);
                    }
                }
                break;
        }
    }

    /**
     * [页面跳转]
     */
    public void startActivity(Class<?> clz) {
        startActivity(clz, null);
    }

    /**
     * [携带数据的页面跳转]
     */
    public void startActivity(Class<?> clz, Bundle bundle) {
        Intent intent = new Intent();
        intent.setClass(this, clz);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        startActivity(intent);
    }

    /**
     * [含有Bundle通过Class打开编辑界面]
     */
    public void startActivityForResult(Class<?> cls, Bundle bundle,
                                       int requestCode) {
        Intent intent = new Intent();
        intent.setClass(this, cls);
        if (bundle != null) {
            intent.putExtras(bundle);
        }
        startActivityForResult(intent, requestCode);
    }

    /**
     * [防止快速点击]
     */
    public boolean fastClick() {
        long lastClick = 0;
        if (System.currentTimeMillis() - lastClick <= 1000) {
            return false;
        }
        lastClick = System.currentTimeMillis();
        return true;
    }

    public void showMsg(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }

    /**
     * 通过id查找并获取控件，使用时不需要强转
     */
    @SuppressWarnings("unchecked")
    public <V extends View> V findView(int id) {
        return findViewById(id);
    }

    /**
     * 通过id查找并获取控件，并setOnClickListener
     */
    public <V extends View> V findView(int id, View.OnClickListener l) {
        V v = findView(id);
        v.setOnClickListener(l);
        return v;
    }

    /**
     * 通过id查找并获取控件，并setOnClickListener
     */
    public <V extends View> V findViewById(int id, View.OnClickListener l) {
        return findView(id, l);
    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getProjectID();
            schedualOrganizeId = bean.getBimProjectId();

        }
    }

    /**
     * 获取进度计划
     *
     * @return
     */
    public SchedualListDto.DataDTO getSchedualPlanDto() {
        if (Hawk.contains(CustomParam.SCHEDUAL_PLAN)) {
            SchedualListDto.DataDTO dto = Hawk.get(CustomParam.SCHEDUAL_PLAN);
            if (!TextUtils.isEmpty(dto.getName())) {
                return dto;
            }
            return new SchedualListDto.DataDTO();
        }
        return new SchedualListDto.DataDTO();
    }

    /**
     * 获取token
     *
     * @return
     */
    public String getToken() {
        String token = Hawk.get(CustomParam.Token);
        if (!TextUtils.isEmpty(token)) {
            return token;
        }
        return "";
    }

    /**
     * 获取登录人
     *
     * @return
     */
    public String getLoginAccount() {
        if (!TextUtils.isEmpty(Hawk.get(CustomParam.RealName))) {
            return Hawk.get(CustomParam.RealName);

        }
        return "";
    }

}
