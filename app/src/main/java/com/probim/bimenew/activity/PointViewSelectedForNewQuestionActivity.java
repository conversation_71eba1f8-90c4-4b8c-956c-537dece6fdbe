package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.ModelPointView2RvAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.model.ModelViewPointModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.view.AutoLoadRecyclerView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :新建检查选择视点界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/1/15/10:50.
 */

public class PointViewSelectedForNewQuestionActivity extends BaseActivity {
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.rv_project) AutoLoadRecyclerView rvProject;
  @BindView(R.id.smartRefreshLayout) SmartRefreshLayout smartRefreshLayout;
  @BindView(R.id.rv_status_layout) RelativeLayout rvStatusLayout;
  @BindView(R.id.tv_status) TextView tvStatus;
  private String modelId;
  private String projectId;
  private List<ModelViewPointModel> list = new ArrayList<>();
  private ModelPointView2RvAdapter adapter;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_for_point_view);
    ButterKnife.bind(this);
    initView();
  }

  @Override protected void loadData() {

  }

  @Override protected void initView() {
    tvTitle.setText("视点选择");
    if (getIntent() != null) {
      modelId = getIntent().getStringExtra(CustomParam.ModelId);
      projectId = getIntent().getStringExtra(CustomParam.ProjectId);
      initRecycleview();
      initRefresh();
    }
  }

  @Override protected void initData() {

    ModelController controller = new ModelController();
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", projectId);
    params.put("ModelID", modelId);
    controller.GetViewPoint(params, new CallBack<List<ModelViewPointModel>>() {
      @Override
      public void onSuccess(List<ModelViewPointModel> modelViewPointModels) {
        list.clear();
        //遍历list
        for (int i = 0; i < modelViewPointModels.size(); i++) {
          //type==0 视点, 1是批注
          if (modelViewPointModels.get(i).getType() == 0
              || modelViewPointModels.get(i).getType() == 1) {
            list.add(modelViewPointModels.get(i));
          }
        }

        if (list.isEmpty()) {
          rvStatusLayout.setVisibility(View.VISIBLE);
          tvStatus.setText("暂无数据");
        }
        adapter.notifyDataSetChanged();
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  @Override protected void initRecycleview() {
    //为 RecyclerView 设置布局管理器
    rvProject.setLayoutManager(new LinearLayoutManager(this));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvProject.addItemDecoration(
        new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    //动画
    rvProject.setItemAnimator(new DefaultItemAnimator());

    adapter = new ModelPointView2RvAdapter(this, list);

    rvProject.setAdapter(adapter);

    adapter.addRecycleItemListener(new ModelPointView2RvAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        ModelViewPointModel model = (ModelViewPointModel) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.WhoType, model.getType() + "");
        bundle.putString(CustomParam.ViewId, model.getID());
        bundle.putString(CustomParam.ProjectId, projectId);
        bundle.putString(CustomParam.ModelId, model.getModelID());
        bundle.putString(CustomParam.ViewPointName, model.getName());
        Intent intent =
            new Intent(PointViewSelectedForNewQuestionActivity.this, SelectModelViewActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        finish();
      }
    });
  }

  @Override protected void initRefresh() {

    smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
      @Override
      public void onRefresh(RefreshLayout refreshLayout) {
        refreshLayout.finishRefresh(1000);
        initData();
      }
    });
  }

  @OnClick({ R.id.iv_back, R.id.lin_back }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        break;
      case R.id.lin_back:
        finish();
        break;
    }
  }

  @Override protected void onResume() {
    super.onResume();
    initData();
  }
}
