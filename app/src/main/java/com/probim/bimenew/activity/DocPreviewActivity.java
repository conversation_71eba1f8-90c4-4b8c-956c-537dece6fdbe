package com.probim.bimenew.activity;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.BuildConfig;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.utils.DownloadUtil;
import com.probim.bimenew.utils.DownloadUtil.OnDownloadListener;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;
import com.probim.bimenew.utils.web.X5WebView;
import com.tencent.smtt.sdk.TbsReaderView;
import com.tencent.smtt.sdk.TbsReaderView.ReaderCallback;
import java.io.File;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/2/14:21.
 */
public class  DocPreviewActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.web_x)
  X5WebView webX;
  private TbsReaderView tbsReaderView;
  private String doc_type;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_web);
    ButterKnife.bind(this);
    StatusBarUtils.transparencyBar(this);
    initWeb();
    loadData();

  }

  private void initWeb() {
    tbsReaderView = new TbsReaderView(this, new ReaderCallback() {
      @Override
      public void onCallBackAction(Integer integer, Object o, Object o1) {

      }
    });
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      String doc_url = bundle.getString(CustomParam.DocUrl);
      String doc_name = bundle.getString(CustomParam.Doc_Name);
      doc_type = bundle.getString(CustomParam.Doc_Type);

      //微软预览文档
      //webX.loadUrl("https://view.officeapps.live.com/op/view.aspx?src=" + doc_url);

      DownloadUtil.get().download(doc_url, "BIMeFile", doc_name, new OnDownloadListener() {
        @Override
        public void onDownloadSuccess(File docFile) {

          new Thread(new Runnable() {
            @Override
            public void run() {
              openFile(docFile);
            }
          }).start();

         /* Bundle bundle = new Bundle();
          bundle.putString("filePath", docFile.getAbsolutePath().toString());
          bundle.putString("tempPath", Environment.getExternalStorageDirectory().getPath());
          boolean result = tbsReaderView.preOpen(docFile.getAbsolutePath().toString(), false);
          if (result) {
            tbsReaderView.openFile(bundle);
          }*/
        }

        @Override
        public void onDownloading(int progress) {

        }

        @Override
        public void onDownloadFailed() {

        }
      });

    }

  }

  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({R.id.iv_back, R.id.lin_back})
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
    }
  }

  /**
   * 打开文件
   */
  private void openFile(File f) {
/*
    Intent myIntent = new Intent(android.content.Intent.ACTION_VIEW);
    String extension = android.webkit.MimeTypeMap
        .getFileExtensionFromUrl(Uri.fromFile(f).toString());
    String mimetype = android.webkit.MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);
    myIntent.setDataAndType(Uri.fromFile(f), mimetype);
    startActivity(myIntent);*/

    Intent intent = new Intent(Intent.ACTION_VIEW);
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
      Uri uri = FileProvider.getUriForFile(this, getPackageName(), f);
      grantUriPermission(BuildConfig.APPLICATION_ID, uri,
          Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
      intent
          .addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
      intent
          .setFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
      intent.setData(uri);
    } else {
      intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      intent.setDataAndType(Uri.fromFile(f), doc_type);
    }

    startActivity(intent);

  /*  intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
    //officeFile：本地文档；type：文档MIMEType类型，可以使用文件格式后缀
    intent.setDataAndType(uri, null);
    if (intent.resolveActivity(getPackageManager()) != null) {



    }*/
  }
}
