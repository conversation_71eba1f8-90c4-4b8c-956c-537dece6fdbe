package com.probim.bimenew.activity.fullview.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.fullview.dto.AllFullViewItemDTO;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.ArrayList;
import java.util.List;

public class AllFullviewItemAdapter extends RecyclerView.Adapter<AllFullviewItemAdapter.ViewHolder> {

    private final String FULLVIEW_URL = Hawk.get(CustomParam.Panorama_URL);
    private List<AllFullViewItemDTO.DataDTO> mDatas = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;
    private String pb_name;
    private String pb_url;

    public AllFullviewItemAdapter(List<AllFullViewItemDTO.DataDTO> mDatas, String pb_name, String pb_url) {
        this.mDatas = mDatas;
        this.pb_name = pb_name;
        this.pb_url = pb_url;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_fullview_item, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AllFullViewItemDTO.DataDTO dto = mDatas.get(position);
        if (TextUtils.isEmpty(dto.getPsScenename())) {
            Glide.with(holder.itemView.getContext()).load(R.mipmap.xxxx_xxxx).into(holder.iv);
        } else {
            Glide.with(holder.itemView.getContext()).load(FULLVIEW_URL + "/Panorama" + pb_url + "/vtour/panos/" + dto.getPsScenename() + ".tiles/thumb.jpg").into(holder.iv);
        }
        if (TextUtils.isEmpty(dto.getViewId())) {
            holder.ivView.setVisibility(View.GONE);
        } else {
            holder.ivView.setVisibility(View.VISIBLE);
        }
        holder.tvName.setText(dto.getPchChname());
        holder.tvTime.setText(dto.getPsCollecteddatetime().split("T")[0]);
        holder.tvType.setText(pb_name);
        holder.linContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onClick(position, dto);
            }
        });
        holder.btnDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ((SwipeMenuLayout) holder.itemView).quickClose();
                onItemClickListener.OnDelete(position, dto);
            }
        });

    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvName;
        private final TextView tvType;
        private final TextView tvTime;
        private final AppCompatImageView iv;
        private final LinearLayout linContainer;
        private final AppCompatButton btnDelete;
        private final AppCompatImageView ivView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            iv = itemView.findViewById(R.id.iv_fullview);
            tvName = itemView.findViewById(R.id.tv_name);
            tvType = itemView.findViewById(R.id.tv_type);
            tvTime = itemView.findViewById(R.id.tv_time);
            linContainer = itemView.findViewById(R.id.lin_container);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            ivView = itemView.findViewById(R.id.iv_fullview_view);
            LinearLayout linFunc = itemView.findViewById(R.id.lin_func);
            if (mDatas.size() == 1){
                linFunc.setVisibility(View.GONE);
            }
        }
    }
}
