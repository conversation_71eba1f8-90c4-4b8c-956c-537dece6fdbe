package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.kcrason.dynamicpagerindicatorlibrary.DynamicPagerIndicator;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.TabAdatpter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.fragment.model.model_details.NtationFragment;
import com.probim.bimenew.fragment.model.model_details.PointViewFragment;
import com.probim.bimenew.fragment.model.model_details.TwoDimensionalViewFragment;
import com.probim.bimenew.fragment.model.model_details.ViewFragment;
import com.probim.bimenew.utils.view.NoScrollViewPager;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :项目模型详情列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/16:42.
 */
public class ProjectModelActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.tab_title)
  DynamicPagerIndicator tabTitle;
  @BindView(R.id.vp_content)
  NoScrollViewPager vpContent;
  private List<BaseFragment> mFragments = new ArrayList<>();
  List<String> mTitle = new ArrayList<>();

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_project_model);
    ButterKnife.bind(this);
    initData();

  }

  @Override
  protected void loadData() {

  }

  private void initTab(String pId, String mId) {
    mTitle.add("视图");
    mTitle.add("二维图纸");
    mTitle.add("视点");
    mTitle.add("批注");
    mFragments.add(ViewFragment.newInstance(pId, mId));
    mFragments.add(TwoDimensionalViewFragment.newInstance(pId, mId));
    mFragments.add(PointViewFragment.newInstance(pId, mId));
    mFragments.add(NtationFragment.newInstance(pId, mId));
    TabAdatpter vpAdapter = new TabAdatpter(getSupportFragmentManager(), mFragments, mTitle);
    vpContent.setAdapter(vpAdapter);
    vpContent.setOffscreenPageLimit(mFragments.size());
    tabTitle.setViewPager(vpContent);
  }


  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      String modelId = bundle.getString(CustomParam.ModelId);
      String projectId = bundle.getString(CustomParam.ProjectId);
      String modleName = bundle.getString(CustomParam.ModelName);
      tvTitle.setText(modleName);
      tvLeft.setText("模型");
      initTab(projectId, modelId);
    }
  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({R.id.iv_back, R.id.lin_back})
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
    }
  }
}
