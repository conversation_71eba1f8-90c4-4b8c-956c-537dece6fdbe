package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualNewPeopleDto {

    @SerializedName("MobileUserDetial_Name")
    private String mobileUserDetial_Name;
    @SerializedName("MobileUserDetial_PlanNum")
    private String mobileUserDetial_PlanNum;
    @SerializedName("MobileUserDetial_AdddelNum")
    private String mobileUserDetial_AdddelNum;
    @SerializedName("MobileUserDetial_AddNum")
    private String mobileUserDetial_AddNum;
    @SerializedName("MobileUserDetial_NewNum")
    private String mobileUserDetial_NewNum;
    @SerializedName("MobileUserDetial_type")
    private String mobileUserDetial_type;
    @SerializedName("MobileUserDetial_state")
    private String mobileUserDetial_state;
    @SerializedName("MobileUserDetial_ProjectID")
    private String mobileUserDetial_ProjectID;
    @SerializedName("MobileUserDetial_Unittime")
    private String mobileUserDetial_Unittime;
    @SerializedName("MobileUserDetial_createuser")
    private String mobileUserDetial_createuser;
    private String organizeId;

    public String getMobileUserDetial_Name() {
        return mobileUserDetial_Name;
    }

    public void setMobileUserDetial_Name(String mobileUserDetial_Name) {
        this.mobileUserDetial_Name = mobileUserDetial_Name;
    }

    public String getMobileUserDetial_PlanNum() {
        return mobileUserDetial_PlanNum;
    }

    public void setMobileUserDetial_PlanNum(String mobileUserDetial_PlanNum) {
        this.mobileUserDetial_PlanNum = mobileUserDetial_PlanNum;
    }

    public String getMobileUserDetial_AdddelNum() {
        return mobileUserDetial_AdddelNum;
    }

    public void setMobileUserDetial_AdddelNum(String mobileUserDetial_AdddelNum) {
        this.mobileUserDetial_AdddelNum = mobileUserDetial_AdddelNum;
    }

    public String getMobileUserDetial_AddNum() {
        return mobileUserDetial_AddNum;
    }

    public void setMobileUserDetial_AddNum(String mobileUserDetial_AddNum) {
        this.mobileUserDetial_AddNum = mobileUserDetial_AddNum;
    }

    public String getMobileUserDetial_NewNum() {
        return mobileUserDetial_NewNum;
    }

    public void setMobileUserDetial_NewNum(String mobileUserDetial_NewNum) {
        this.mobileUserDetial_NewNum = mobileUserDetial_NewNum;
    }

    public String getMobileUserDetial_type() {
        return mobileUserDetial_type;
    }

    public void setMobileUserDetial_type(String mobileUserDetial_type) {
        this.mobileUserDetial_type = mobileUserDetial_type;
    }

    public String getMobileUserDetial_state() {
        return mobileUserDetial_state;
    }

    public void setMobileUserDetial_state(String mobileUserDetial_state) {
        this.mobileUserDetial_state = mobileUserDetial_state;
    }

    public String getMobileUserDetial_ProjectID() {
        return mobileUserDetial_ProjectID;
    }

    public void setMobileUserDetial_ProjectID(String mobileUserDetial_ProjectID) {
        this.mobileUserDetial_ProjectID = mobileUserDetial_ProjectID;
    }

    public String getMobileUserDetial_Unittime() {
        return mobileUserDetial_Unittime;
    }

    public void setMobileUserDetial_Unittime(String mobileUserDetial_Unittime) {
        this.mobileUserDetial_Unittime = mobileUserDetial_Unittime;
    }

    public String getMobileUserDetial_createuser() {
        return mobileUserDetial_createuser;
    }

    public void setMobileUserDetial_createuser(String mobileUserDetial_createuser) {
        this.mobileUserDetial_createuser = mobileUserDetial_createuser;
    }

    public String getOrganizeId() {
        return organizeId;
    }

    public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
    }
}
