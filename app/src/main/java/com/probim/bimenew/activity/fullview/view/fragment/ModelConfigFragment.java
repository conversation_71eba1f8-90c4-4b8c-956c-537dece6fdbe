package com.probim.bimenew.activity.fullview.view.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.fullview.adapter.ModelCofigAdapter;
import com.probim.bimenew.activity.fullview.inters.ViewpointClickListener;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.model.ModelStages;
import com.probim.bimenew.net.CallBack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ModelConfigFragment extends Fragment {

    private RecyclerView recyclerView;
    private String projectID;
    private List<ModelStages.DataBean> beanList = new ArrayList<>();
    private ModelCofigAdapter cofigAdapter;
    private ViewpointClickListener viewpointClickListener;

    public static ModelConfigFragment newInstance(String label) {
        Bundle args = new Bundle();
        args.putString("label", label);
        ModelConfigFragment fragment = new ModelConfigFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_config, container, false);
        recyclerView = view.findViewById(R.id.rv_config);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initRecyclerView();
        getProjectDao();
        getModelStages();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        viewpointClickListener = (ViewpointClickListener) context;
    }

    private void initRecyclerView() {
        cofigAdapter = new ModelCofigAdapter(beanList);
        cofigAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                cofigAdapter.setSelectPosition(pos);
                cofigAdapter.notifyDataSetChanged();

                ModelStages.DataBean dto = (ModelStages.DataBean) o;
                viewpointClickListener.onSelectConfig(dto.getMenuName());
                Bundle result = new Bundle();
                result.putString("configId", dto.getBusinessCode());
                getParentFragmentManager().setFragmentResult("config", result);
            }

            @Override
            public void OnDelete(int pos, Object o) {

            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalNoItemRecyclerView.initialize(recyclerView).setAdapter(cofigAdapter);
    }

    /**
     * 获取模型阶段
     */
    private void getModelStages() {
        ModelController controller = new ModelController();
        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", projectID);
        controller.GetModelStages(params, new CallBack<ModelStages>() {

            @Override
            public void onSuccess(ModelStages modelStages) {
                if (modelStages.getData() != null) {
                    if (!beanList.isEmpty()) {
                        beanList.clear();
                    }
//                    beanList.addAll(modelStages.getProjectDictData().get(0).getChildren());
//                    cofigAdapter.notifyDataSetChanged();


                  /*  // 依Integer排序
                    Collections.sort(modelStages.getProjectDictData().get(0).getChildren(), new Comparator<ModelStages.ProjectDictDataBean.ChildrenBean>() {
                        @Override
                        public int compare(ModelStages.ProjectDictDataBean.ChildrenBean childrenBean, ModelStages.ProjectDictDataBean.ChildrenBean t1) {
                            if (!TextUtils.isEmpty(t1.getSort())) {
                                return Integer.parseInt(childrenBean.getSort()) - Integer.parseInt(t1.getSort());
                            }
                            return 0;
                        }
                    });*/

                }


            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }


    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            projectID = bean.getProjectID();
            // schedualOrganizeId = bean.getBimProjectId();

        }
    }
}
