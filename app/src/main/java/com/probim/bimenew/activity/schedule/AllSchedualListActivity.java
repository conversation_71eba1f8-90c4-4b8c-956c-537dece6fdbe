package com.probim.bimenew.activity.schedule;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.schedule.adapter.pop.AllSchedualPreviewAdapter;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.activity.schedule.modules.NewSchedualTaskActivty;
import com.probim.bimenew.activity.schedule.modules.NewTaskActivity;
import com.probim.bimenew.activity.schedule.modules.TaskDetailsActivity;
import com.probim.bimenew.activity.schedule.modules.TaskExamineActivty;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.SchedualController;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.dropdownmenu.bean.DropdownItemObject;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownButton;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownListView;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * 全部进度计划列表
 */
public class AllSchedualListActivity extends BaseActivity implements View.OnClickListener, DropdownListView.Container {
    private List<SchedualPreviewListDTO.DataDTO> dtoList = new ArrayList<>();
    private RecyclerView rvSchedual;
    private AllSchedualPreviewAdapter allSchedualPreviewAdapter;
    private SchedualController schedualController;
    private Animation dropdown_in, dropdown_out, dropdown_mask_out;
    private List<DropdownItemObject> chooseTypeData = new ArrayList<>();
    private List<DropdownItemObject> chooseStatusData = new ArrayList<>();

    private View mask;
    private DropdownListView lisDropdownStatus;
    private DropdownListView lisDropdownType;
    private DropdownButton dropStatus;
    private DropdownButton dropType;
    private DropdownListView currentDropdownList;
    private String statusSelected = "";
    private String typeSelected = "";
    private SmartRefreshLayout smartRefreshLayout;

    private LinearLayout linNoData;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_schedual_preview);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        initRecycleview();
        getPlan();
    }

    @Override
    protected void loadData() {
        schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token",Hawk.get(CustomParam.Token));
        if (TextUtils.isEmpty(typeSelected)) {
            parmas.put("organizeId", organizeId);

        } else {
            parmas.put("uid", typeSelected);
            parmas.put("projectid", typeSelected);
        }
        if (!TextUtils.isEmpty(statusSelected)) {
            parmas.put("auditStatus", statusSelected);
        }

        schedualController.getAllSchedualList(parmas, new CallBack<SchedualPreviewListDTO>() {
            @Override
            public void onSuccess(SchedualPreviewListDTO schedualPreviewListDTO) {
                dtoList.clear();
                dtoList.addAll(schedualPreviewListDTO.getData());
                allSchedualPreviewAdapter.notifyDataSetChanged();
                if (dtoList.isEmpty()) {
                    linNoData.setVisibility(View.VISIBLE);
                } else {
                    linNoData.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

    }

    @Override
    protected void initView() {
        linNoData = findView(R.id.lin_rv_status_layout);
        dropStatus = findView(R.id.drop_status);
        dropType = findView(R.id.drop_type);
        lisDropdownStatus = findView(R.id.lis_dropdown_status);
        lisDropdownType = findView(R.id.lis_dropdown_type);
        mask = findView(R.id.mask);
        rvSchedual = findView(R.id.rv_schedual);
        TextView tvTittle = findView(R.id.tv_black_title);
        tvTittle.setText("进度填报");
        LinearLayout linBack = findView(R.id.lin_back, this);
        ImageView ivAdd = findView(R.id.iv_save, this);
        ivAdd.setVisibility(View.VISIBLE);
        ivAdd.setImageResource(R.mipmap.btn_common_add);
        smartRefreshLayout = findView(R.id.smartRefreshLayout);
        smartRefreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshLayout) {
                loadData();
                refreshLayout.finishRefresh(1000);
            }
        });


    }

    @Override
    protected void initData() {
    }

    @Override
    protected void initRecycleview() {
        allSchedualPreviewAdapter = new AllSchedualPreviewAdapter(dtoList);
        allSchedualPreviewAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                Intent intent;
                SchedualPreviewListDTO.DataDTO dto = (SchedualPreviewListDTO.DataDTO) o;
//                Hawk.put(CustomParam.SCHEDUAL_TIME_SELECTED, dto.getMobilePA_Unittime());
                Hawk.put(CustomParam.SCHEDUAL_ITEM_SELECTED, dto);
                switch (dto.getAuditStatus()) {
                    case 0:
                    case 3:
                        intent = new Intent(AllSchedualListActivity.this, TaskDetailsActivity.class);
                        intent.putExtra("schedule-dto", dto);
                        startActivity(intent);
                        // Hawk.put(CustomParam.SCHEDUAL_TIME_SELECTED, dto.getMobilePA_Unittime());
                        // Hawk.put(CustomParam.SCHEDUAL_ITEM_SELECTED, dto);
                        break;
                    case 1:
                    case 2:
                        intent = new Intent(AllSchedualListActivity.this, TaskDetailsActivity.class);
                        intent.putExtra("schedule-dto", dto);
                        startActivity(intent);
                        break;

                }

             /*   SchedualListDto.DataDTO dto = (SchedualListDto.DataDTO) o;
                Intent intent = new Intent(AllSchedualListActivity.this,SchedualTypeListActivity.class);
                intent.putExtra(CustomParam.INTENT_PARAMS, dto);*/
            }

            @Override
            public void OnDelete(int pos, Object o) {
                SchedualPreviewListDTO.DataDTO dto = (SchedualPreviewListDTO.DataDTO) o;
                if (dto.getAuditStatus() == 0) {
                    deleteItem(dto, pos);
                } else {
                    showMsg("进度已提交了，无法删除！");
                }


            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalRecyclerView.initialize(rvSchedual).setAdapter(allSchedualPreviewAdapter);

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.iv_save:
                // 判断权限 是否有权限新增
                if (Hawk.get("isCanAdd")){
                    Hawk.delete(CustomParam.SCHEDUAL_TIME_SELECTED);
                    Hawk.delete(CustomParam.SCHEDUAL_ITEM_SELECTED);
                    Intent intent = new Intent(AllSchedualListActivity.this, NewTaskActivity.class);
                    intent.putExtra(CustomParam.isEditTask, false);
                    startActivity(intent);
                    }else {
                        showMsg("暂无权限，请在PC端修改相应权限！");
                    }


                break;
            default:
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadData();
    }

    /**
     * 删除item
     */
    private void deleteItem(SchedualPreviewListDTO.DataDTO dto, int pos) {
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Progress_ID", dto.getProgress_ID());
        parmas.put("token", Hawk.get(CustomParam.Token));
        schedualController.deleteScedual(parmas, new CallBack<SchedualBaseResult>() {
            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                if (schedualBaseResult.getRet() == 1) {
                    loadData();
                } else {
                    showMsg(schedualBaseResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 舒适化下拉
     */
    private void initDrop() {
        // 加载动画
        dropdown_in = AnimationUtils.loadAnimation(this, R.anim.dropdown_in);
        dropdown_out = AnimationUtils.loadAnimation(this, R.anim.dropdown_out);
        dropdown_mask_out = AnimationUtils.loadAnimation(this, R.anim.dropdown_mask_out);
        //加载数据
        chooseStatusData.add(new DropdownItemObject("全部状态", 0, ""));
        chooseStatusData.add(new DropdownItemObject("待提交", 1, "0"));
        chooseStatusData.add(new DropdownItemObject("待审核", 2, "1"));
        chooseStatusData.add(new DropdownItemObject("已审核", 3, "2"));
        chooseStatusData.add(new DropdownItemObject("驳回待提交", 4, "3"));


        mask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hide();
            }
        });
        reset();
        lisDropdownStatus.bind(chooseStatusData, dropStatus, this, 0);

        lisDropdownType.bind(chooseTypeData, dropType, this, 0);


        dropdown_mask_out.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (currentDropdownList == null) {
                    reset();
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
    }

    void reset() {
        dropStatus.setChecked(false);
        dropType.setChecked(false);

        mask.setVisibility(View.GONE);
        lisDropdownType.setVisibility(View.GONE);
        lisDropdownStatus.setVisibility(View.GONE);

        lisDropdownType.clearAnimation();
        lisDropdownStatus.clearAnimation();
        mask.clearAnimation();
    }

    @Override
    public void show(DropdownListView listView) {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            currentDropdownList.startAnimation(dropdown_out);
            currentDropdownList.setVisibility(View.GONE);
            currentDropdownList.button.setChecked(false);
        }
        currentDropdownList = listView;
        mask.clearAnimation();
        mask.setVisibility(View.VISIBLE);
        currentDropdownList.clearAnimation();
        currentDropdownList.startAnimation(dropdown_in);
        currentDropdownList.setVisibility(View.VISIBLE);
        currentDropdownList.button.setChecked(true);
    }

    @Override
    public void hide() {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            currentDropdownList.startAnimation(dropdown_out);
            currentDropdownList.button.setChecked(false);
            mask.clearAnimation();
            mask.startAnimation(dropdown_mask_out);
        }
        currentDropdownList = null;
    }

    @Override
    public void onSelectionChanged(DropdownListView view, String itemId) {
        // 状态筛选
        if (lisDropdownStatus == view) {

            if (itemId.equals("填报状态")) {
                statusSelected = "";
            } else {
                statusSelected = itemId;
            }


        }

        // 类型筛选
        if (lisDropdownType == view) {

            if (itemId.equals("计划")) {
                typeSelected = "";
            } else {
                typeSelected = itemId;
            }


        }
        dtoList.clear();
        allSchedualPreviewAdapter.notifyDataSetChanged();
        loadData();
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
        super.onPointerCaptureChanged(hasCapture);
    }

    /**
     * 获取计划列表
     */
    private void getPlan() {
        schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("organizeId", organizeId);
        schedualController.getSchedualList(parmas, new CallBack<SchedualListDto>() {
            @Override
            public void onSuccess(SchedualListDto schedualListDto) {
                if (schedualListDto.getData() != null) {
                    chooseTypeData.add(new DropdownItemObject("全部计划", 0, ""));
                    for (SchedualListDto.DataDTO dto : schedualListDto.getData()) {
                        chooseTypeData.add(new DropdownItemObject(dto.getName(), 1, dto.getUID()));
                    }

                }
                initDrop();

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
}
