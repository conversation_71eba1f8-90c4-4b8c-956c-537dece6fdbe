package com.probim.bimenew.activity.flow;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;

import java.util.ArrayList;
import java.util.List;

public class AllFlowListActivity extends BaseActivity implements TabLayout.BaseOnTabSelectedListener, View.OnClickListener {
    private String[] tabs = {"我的待办", "已完结"};
    private List<Fragment> tabFragmentList = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_flow);
        initView();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        TextView tvTittle = findView(R.id.tv_title);
        tvTittle.setText("业务流程");
        LinearLayout linBack = findView(R.id.lin_back, this);
        tabFragmentList.add(FlowFragment.newInstance("Todolist_Init"));
        tabFragmentList.add(FlowFragment.newInstance("Complete_Init"));
        TabLayout tabLayout = findView(R.id.tb_flow);
        for (int i = 0; i < tabs.length; i++) {
            tabLayout.addTab(tabLayout.newTab().setText(tabs[i]));
        }
        tabLayout.addOnTabSelectedListener(this);
        ViewPager vp = findView(R.id.vp_flow);
        vp.setAdapter(new FragmentPagerAdapter(getSupportFragmentManager()) {
            @NonNull
            @Override
            public Fragment getItem(int position) {
                return tabFragmentList.get(position);
            }

            @Override
            public int getCount() {
                return tabFragmentList.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return tabs[position];
            }
        });
        tabLayout.setupWithViewPager(vp);

    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onTabSelected(TabLayout.Tab tab) {

    }

    @Override
    public void onTabUnselected(TabLayout.Tab tab) {

    }

    @Override
    public void onTabReselected(TabLayout.Tab tab) {

    }

    @Override
    public void onClick(View v) {
        finish();
    }
}
