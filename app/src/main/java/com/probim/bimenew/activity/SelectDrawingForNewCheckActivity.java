package com.probim.bimenew.activity;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Environment;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.model.DrawingsModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.Base64Utils;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;
import com.probim.bimenew.utils.zoomPhoto.PhotoViewAttacherZoom;
import com.probim.bimenew.utils.zoomPhoto.PhotoViewZoom;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;

/**
 * Description :图纸选点功能
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/17:32.
 */
public class SelectDrawingForNewCheckActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.iv_drawings)
  PhotoViewZoom ivDrawings;
  @BindView(R.id.tv_right) TextView tvRight;
  public static int imageWidth;
  public static int imageHeight;
  public static String img2d_ModelId = "";
  public static String img2dId = "";
  public static String img2d_pos_x = "";
  public static String img2d_pos_y = "";
  public static String imgName = "";
  public static String img_url = "";
  public static boolean isCan = false;
  public static String pixelX = "";
  public static String pixelY = "";
  public static String X = "";
  public static String Y = "";
  public static String Z = "";

  private String modelId;
  private String twoDimensId;
  private float pX;
  private float pY;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_drawings);
    ButterKnife.bind(this);
    StatusBarUtils.transparencyBar(this);
    initView();
    loadData();
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      //if ()
      twoDimensId = bundle.getString(CustomParam.TwoDimensId);
      String name = bundle.getString(CustomParam.TwoDimensName);
      imgName = name;
      String projectId = bundle.getString(CustomParam.ProjectId);
      modelId = bundle.getString(CustomParam.ModelId);

      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ModelID", modelId);
      params.put("VersionNO", "");
      params.put("FileType", "PlanView");
      params.put("FileName", twoDimensId);

      ModelController controller = new ModelController();

      controller.GetTwoDimensionalDetails(params, new CallBack<DrawingsModel>() {
        @Override
        public void onSuccess(DrawingsModel drawingsModel) {
          pixelX  =drawingsModel.getPixel2mx()+"";
          pixelY = drawingsModel.getPixel2my()+"";
          X =drawingsModel.getLeftptm().getX()+"";
          Y =drawingsModel.getLeftptm().getY()+"";
          Z =drawingsModel.getLeftptm().getZ()+"";
          img_url = drawingsModel.getImagebase64();
          /*Glide.with(getApplicationContext())
              .load(Base64Utils.decode(drawingsModel.getImagebase64())).asBitmap().into(
              new SimpleTarget<Bitmap>() {
                @Override public void onResourceReady(Bitmap resource,
                    GlideAnimation<? super Bitmap> glideAnimation) {
                  imageWidth = resource.getWidth();
                  imageHeight = resource.getHeight();
                  ivDrawings.mOriginalWidth = imageWidth;
                  ivDrawings.mOriginalHeight = imageHeight;
                  ivDrawings.setImageBitmap(resource);
                }
              });*/
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }
  }

  @Override
  protected void initView() {
    tvTitle.setText("图纸选择");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("完成");
    ivDrawings.setOnPhotoTapListener(new PhotoViewAttacherZoom.OnPhotoTapListener() {
      @Override public void onPhotoTap(View view, float x, float y) {
        //ivDrawings.setOnSelectedDrawX( x * imageWidth);
        //ivDrawings.setOnSelectedDrawY(y * imageHeight);

      }
    });
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({ R.id.iv_back, R.id.lin_back, R.id.tv_right })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        img2d_ModelId = modelId;
        img2d_pos_x = Hawk.get(CustomParam.SelectPositionX)+"";
        img2d_pos_y = Hawk.get(CustomParam.SelectPositionY)+"";
        img2dId = twoDimensId;
        isCan = true;
        finish();
        CustomParam.WhereModifyCheck = true;
        break;
    }
  }

  @Override protected void onDestroy() {
    super.onDestroy();
    //img2d_ModelId = "";
    //img2dId = "";
    //img2d_pos = "";
    //img_url="";
    //imgName="";
  }

  /**
   * 数组转字符串
   */
  private String byteToString(byte[] bytes) {

    try {

      String res = new String(bytes, "UTF-8");

      System.out.println(res);

      return res;
    } catch (UnsupportedEncodingException e) {

      // TODO Auto-generated catch block

      e.printStackTrace();
    }
    return "";
  }

  /**
   * 保存图片到本地
   */
  private void saveBitmap(String bytes) {

    byte[] decode = Base64Utils.decode(bytes);

    Bitmap bitmap = BitmapFactory.decodeByteArray(decode, 0, decode.length);

    try {
      String path = Environment.getExternalStorageDirectory().getPath()
          + "/decodeImage.jpg";
      Log.d("linc", "path is " + path);
      OutputStream stream = new FileOutputStream(path);
      bitmap.compress(Bitmap.CompressFormat.JPEG, 15, stream);
      stream.close();
      Log.e("linc", "jpg okay!");
    } catch (IOException e) {
      e.printStackTrace();
      Log.e("linc", "failed: " + e.getMessage());
    }
  }
}
