package com.probim.bimenew.activity.schedule.adapter.pop;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class AllSchedualAdapter extends RecyclerView.Adapter<AllSchedualAdapter.ViewHolder> {

    private List<SchedualListDto.DataDTO> dtoList = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;
    public AllSchedualAdapter(List<SchedualListDto.DataDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_schedual, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SchedualListDto.DataDTO dto = dtoList.get(position);
        holder.tvSchedualName.setText(dto.getName());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener!=null){
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.schedual_name)
        TextView tvSchedualName;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
