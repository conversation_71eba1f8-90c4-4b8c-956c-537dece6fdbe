package com.probim.bimenew.activity.check;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.RoleUsersActivity;
import com.probim.bimenew.adapter.AllPeopleMultiSelectAdapter;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.UserDto;
import com.probim.bimenew.event.EventBean;
import com.probim.bimenew.event.EventBusUtils;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllPeoplerResult;
import com.probim.bimenew.result.AllUsersDto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * Description :全部人员
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class AllPeopleMultiSelectListActivity extends BaseActivity implements View.OnClickListener {

    private final List<AllUsersDto.DataBean.ListBean> projectRoleList = new ArrayList<>();
    private String projectId;
    private AllPeopleMultiSelectAdapter allAdapter;
    private CheckController checkController;
    private EditText edtSearch;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_people);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        initListener();
        loadData();
        Logger.t("AllPeopleMultiSelectListActivity").e(getTaskId() + "");

    }

    @Override
    protected void loadData() {
        getProjectDao();
        checkController = new CheckController();
        getProjectUsers("");
    }


    @Override
    protected void initView() {
        RelativeLayout rlRoleUser = findView(R.id.rl_role_user, this);
        TextView tvTittle = findViewById(R.id.tv_black_title);
        tvTittle.setText("指定人员");
        TextView tvRight = findView(R.id.tv_right, this);
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("完成");
        LinearLayout liniBack = findView(R.id.lin_back, this);
        LinearLayout linSearch = findView(R.id.lin_search, this);
        edtSearch = findView(R.id.edt_search);

        //初始化全部人员适配器
        setAllAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            projectId = bean.getProjectID();
        }
    }

    @Override
    public void onClick(View v) {
        Intent intent = null;
        switch (v.getId()) {

            case R.id.lin_back:

                finish();

                break;

            case R.id.tv_right:

                intent = new Intent(AllPeopleMultiSelectListActivity.this, CheckActivity.class);
                Bundle bundle = new Bundle();
                bundle.putSerializable(CustomParam.StartForResult, (Serializable) getSelectedList());
                intent.putExtras(bundle);
                setResult(RESULT_OK, intent);
                finish();


                EventBusUtils.sendEvent(new EventBean(ApiConstant.CODE_TYPE_USER, getSelectedList()));
                finish();

                break;
            case R.id.rl_role_user:
                intent = new Intent(AllPeopleMultiSelectListActivity.this, RoleUsersActivity.class);
                startActivity(intent);


                break;

            default:
                break;
        }
    }

    /**
     * 设置文件夹适配器
     */
    private void setAllAdapter() {
        RecyclerView rvAllPeople = findView(R.id.rv_all_people);
        allAdapter = new AllPeopleMultiSelectAdapter(this, projectRoleList);
        allAdapter.addRecycleItemListener(new AllPeopleMultiSelectAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {

            }
        });
        VerticalRecyclerView.initialize(rvAllPeople).setAdapter(allAdapter);
        rvAllPeople.setAdapter(allAdapter);
    }

    /**
     * 获取项目所有人员
     */
    /**
     * 获取项目所有人员
     */
    private void getProjectUsers(String keyWord) {

        HashMap<String, String> params = new HashMap<>();
        params.put("PageNum", "1");
        params.put("PageSize", "999");
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("OrganizeId", projectId);
        params.put("KeyWord", keyWord);
        checkController.getCheckPeople(params, new CallBack<AllUsersDto>() {
            @Override
            public void onSuccess(AllUsersDto allUsersDto) {
                projectRoleList.clear();
                for (AllUsersDto.DataBean.ListBean dataBean : allUsersDto.getData().getList()) {
                    projectRoleList.add(dataBean);
                    allAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //键盘搜索键
    private void initListener() {

        edtSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView textView,
                                          int i, KeyEvent keyEvent) {
                if (i == EditorInfo.IME_ACTION_SEARCH) {
                    getProjectUsers(
                            edtSearch.getText().toString().trim());
                    return true;
                }
                return false;
            }
        });
    }

    /**
     * 获取选中数据
     */
    private List<UserDto> getSelectedList() {

        List<UserDto> isSelectedAllList = new ArrayList<>();

        for (int i = 0; i < projectRoleList.size(); i++) {

            if (projectRoleList.get(i).isSelected()) {

                UserDto dto = new UserDto(projectRoleList.get(i).getUserId(), projectRoleList.get(i).getRealName());
                isSelectedAllList.add(dto);
            }
        }
        return isSelectedAllList;
    }

    /**
     * 去除人员重复数据
     */
    public List<String> removeStringListDupli(List<String> stringList) {

        Set<String> set = new LinkedHashSet<>();
        set.addAll(stringList);
        stringList.clear();
        stringList.addAll(set);
        return stringList;
    }

}
