package com.probim.bimenew.activity.fullview.view;

import android.Manifest;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bumptech.glide.Glide;
import com.google.android.material.tabs.TabLayout;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.PhotoViewActivity;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.fullview.adapter.AllLabelAdapter;
import com.probim.bimenew.activity.fullview.controller.FullviewController;
import com.probim.bimenew.activity.fullview.dto.FullviewLableDTO;
import com.probim.bimenew.activity.fullview.dto.FullviewResult;
import com.probim.bimenew.activity.fullview.dto.LabelFileDto;
import com.probim.bimenew.activity.fullview.inters.ViewpointClickListener;
import com.probim.bimenew.activity.fullview.view.fragment.ModelConfigFragment;
import com.probim.bimenew.activity.fullview.view.fragment.ModelFragment;
import com.probim.bimenew.activity.fullview.view.fragment.ModelPointFragment;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.PermissionUtils;
import com.probim.bimenew.utils.bannerLoader.GlideImageLoader;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;
import com.youth.banner.listener.OnBannerListener;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import java.io.File;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;

/**
 * 新增全景图
 */
public class NewFullviewActivity extends BaseActivity implements View.OnClickListener, TabLayout.BaseOnTabSelectedListener, ViewpointClickListener, OnBannerListener, ViewPager.OnPageChangeListener {
    private static TabLayout tabLayout;
    private final int code_photo = 1000;
    private final String FULLVIEW_URL = Hawk.get(CustomParam.Panorama_URL);
    List<PhotoDto> photoDtoList = new ArrayList<>();
    private FullviewController fullviewController = new FullviewController();
    private EditText edtName;
    private TextView tvLabel;
    private AppCompatImageView ivPhoto;
    private LinearLayout linSelectPhoto;
    private List<FullviewLableDTO.DataDTO> fullviewLableDTOData;
    private FullviewLableDTO.DataDTO selectedLableDto;
    private PopupWindow mPop;
    private List<Fragment> tabFragmentList = new ArrayList<>();
    private String[] tabs = {"阶段", "模型", "空间视点"};
    private TextView tvConfig;
    private TextView tvViewpoint;
    private TextView tvModel;
    private RelativeLayout rlPhoto;
    private LinearLayout linViewPoint;
    private File fileOrigin;
    private String fullview_modelId = "";
    private String fullview_viewId = "";
    private List<String> bannerList = new ArrayList<>();
    private Banner banner;
    private boolean isAll;
    private String parent_pb_guid;
    private LinearLayout linEdtPhotoName;
    private EditText edtPhotName;
    private String fileType;
    private TextView tvAddTime;
    private String[] perms = new String[] {
            Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
    };

    public static void repalce(int position) {
        tabLayout.getTabAt(position).select();
    }

    private TimePickerView pvTime;

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String getNowTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date(System.currentTimeMillis());
        return simpleDateFormat.format(date);
    }

    /**
     * 时间选择器
     */
    private void openTimePicker(TextView tv) {
        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);
        //确定按钮文字颜色
        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                String formatDate = getDay(date);
                tv.setText(formatDate);

            }
        }).setCancelText("清空").setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false}).setContentTextSize(16).setLabel("", "", "", "", "", "").isCenterLabel(true).setLineSpacingMultiplier(3.0f).setDividerColor(R.color.divider_color).setDecorView(null)
//                .setRangDate(startDate, endDate)
                .setTextColorCenter(Color.parseColor("#283A4F")).setTextColorOut(Color.parseColor("#A6AEB6"))
                //容器
                //.setDecorView(linContainer)
                .setLayoutRes(R.layout.pickerview_newcheck, new CustomListener() {
                    @Override
                    public void customLayout(View v) {

                        TextView tvDateTittle = v.findViewById(R.id.tv_date_title);
                        tvDateTittle.setText("请选择日期");
                        TextView btnConfirm = v.findViewById(R.id.btn_confirm);
                        btnConfirm.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });

                        TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                        tv_clear.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                pvTime.dismiss();
                            }
                        });

                        ImageView ivClose = (ImageView) v.findViewById(R.id.iv_close);
                        ivClose.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                //pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });
                    }
                }).build();
        pvTime.show();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_new_fullview);
        initView();
        initData();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        if (getIntent() != null) {
            isAll = getIntent().getBooleanExtra("isAll", false);
            parent_pb_guid = getIntent().getStringExtra("pbguid");
        }
        LinearLayout linEdtName = findView(R.id.lin_edt_name);
        LinearLayout linEdtLabel = findView(R.id.lin_edt_label);
        banner = findView(R.id.banner);
        banner.setOnBannerListener(this);
        banner.setOnPageChangeListener(this);
        if (!isAll) {
            // 子集上传
            linEdtName.setVisibility(View.GONE);
            linEdtLabel.setVisibility(View.GONE);
//            getLabelFile();
        }
        linEdtPhotoName = findView(R.id.lin_edt_photo_name);
        edtPhotName = findView(R.id.edt_photo_name);
        TextView tvRight = findView(R.id.tv_right, this);
        tvRight.setVisibility(View.VISIBLE);
        linViewPoint = findView(R.id.lin_view_point);
        rlPhoto = findView(R.id.rl_photo, this);
        ImageView ivDelete = findView(R.id.iv_delete, this);
        LinearLayout linSelectLabel = findView(R.id.lin_select_label, this);
        LinearLayout linSelectTime = findView(R.id.lin_select_time, this);
        LinearLayout linBack = findView(R.id.lin_back, this);
        linSelectPhoto = findView(R.id.lin_select_photo, this);
        ivPhoto = findView(R.id.iv_photo);
        TextView tvTitle = findView(R.id.tv_title);
        tvLabel = findView(R.id.tv_label);
        tvConfig = findView(R.id.tv_model_config);
        tvViewpoint = findView(R.id.tv_model_viewpoint);
        tvModel = findView(R.id.tv_model);
        tvAddTime = findView(R.id.tv_time);
        tabFragmentList.add(ModelConfigFragment.newInstance("1"));
        tabFragmentList.add(ModelFragment.newInstance("2"));
        tabFragmentList.add(ModelPointFragment.newInstance("3"));
        tabLayout = findView(R.id.tb_point);
        for (int i = 0; i < tabs.length; i++) {
            tabLayout.addTab(tabLayout.newTab().setText(tabs[i]));
        }
        tabLayout.addOnTabSelectedListener(this);
        ViewPager vp = findView(R.id.vp);
        vp.setAdapter(new FragmentPagerAdapter(getSupportFragmentManager()) {
            @NonNull
            @Override
            public Fragment getItem(int position) {
                return tabFragmentList.get(position);
            }

            @Override
            public int getCount() {
                return tabFragmentList.size();
            }

            @Nullable
            @Override
            public CharSequence getPageTitle(int position) {
                return tabs[position];
            }
        });
        tabLayout.setupWithViewPager(vp);

        tvTitle.setText("添加全景图");
        edtName = findView(R.id.edt_name);
        getLable();
    }

    @Override
    protected void initData() {
        getLable();
    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.lin_select_label:
                closeKeybord();
                showLabelPop();
                break;
            case R.id.lin_select_time:
                openTimePicker(tvAddTime);
                break;
            case R.id.lin_select_photo:
                requestCameraPermission();
                break;
            case R.id.iv_delete:
                fileOrigin = null;
                rlPhoto.setVisibility(View.GONE);
                linSelectPhoto.setVisibility(View.VISIBLE);
                linEdtPhotoName.setVisibility(View.GONE);
                break;
            case R.id.tv_right:
                if (fileOrigin == null) {
                    showMsg("请选择全景图！");
                } else {
                    addFullview();
                }

                break;
        }

    }
    /**
     * 显示权限申请理由对话框
     */
    private void showPermissionRationale(String title, String message, Runnable onConfirm) {
        new AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("确定", (dialog, which) -> onConfirm.run())
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 显示跳转设置页面的对话框
     */
    private void showGoToSettingsDialog(String message) {
        new AlertDialog.Builder(this)
                .setTitle("权限设置")
                .setMessage(message)
                .setPositiveButton("去设置", (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", getPackageName(), null);
                    intent.setData(uri);
                    startActivity(intent);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 处理权限被拒绝的情况
     */
    private void handlePermissionDenied(String permissionName, boolean neverAskAgain) {
        if (neverAskAgain) {
            showGoToSettingsDialog(permissionName + "被永久拒绝，请在设置中手动开启");
        } else {
            Toast.makeText(this, permissionName + "被拒绝", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 申请相机权限示例
     */
    private void requestCameraPermission() {
        // 先检查权限是否已授予
        if (PermissionUtils.hasPermissions(this, perms)) {
            getPhoto();
            return;
        }

        // 检查是否需要显示权限说明
        if (PermissionUtils.shouldShowRequestPermissionRationale(this, PermissionUtils.Permission.CAMERA)) {
            showPermissionRationale("相机权限", "需要相机权限才能拍照", () -> {
                PermissionUtils.requestPermissions(this, perms, new PermissionUtils.PermissionCallback() {
                    @Override
                    public void onPermissionGranted() {
                        getPhoto();
                    }

                    @Override
                    public void onPermissionDenied(List<String> deniedPermissions, boolean neverAskAgain) {
                        handlePermissionDenied("相机权限", neverAskAgain);
                    }
                });
            });
        } else {
            // 直接申请权限
            PermissionUtils.requestPermissions(this, perms, new PermissionUtils.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    getPhoto();
                }

                @Override
                public void onPermissionDenied(List<String> deniedPermissions, boolean neverAskAgain) {
                    handlePermissionDenied("相机权限", neverAskAgain);
                }
            });
        }
    }
    /**
     * 获取全部标签
     */
    private void getLable() {

        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", Hawk.get(CustomParam.Token));
        parmas.put("OrganizeId", organizeId);
        fullviewController.getLabelList(parmas, new CallBack<FullviewLableDTO>() {
            @Override
            public void onSuccess(FullviewLableDTO fullviewLableDTO) {
                if (fullviewLableDTO.getData() != null) {

                    fullviewLableDTOData = fullviewLableDTO.getData();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 新建全景图
     */
    private void addFullview() {
        mLoading.show();
        // return (_this.S4()+_this.S4()+"-"+_this.S4()+"-"+_this.S4()+"-"+_this.S4()+"-"+_this.S4()+_this.S4()+_this.S4());
        String pbguid = getStringRandom(4) + getStringRandom(4) + "-" + getStringRandom(4) + "-" + getStringRandom(4) + "-" + getStringRandom(4) + "-" + getStringRandom(4) + getStringRandom(4) + getStringRandom(4);
        HashMap<String, String> url_parmas = new HashMap<>();
        url_parmas.put("pbguid", pbguid);
        url_parmas.put("willappend", "0");
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", Hawk.get(CustomParam.Token));
        if (isAll) {
            parmas.put("organizeId", organizeId);
            parmas.put("collectDate", tvAddTime.getText().toString());
            parmas.put("pbname", edtName.getText().toString());
            if (selectedLableDto != null) {
                parmas.put("labelId", selectedLableDto.getLabelId());
            }
            parmas.put("gisinfo", "");
            parmas.put("ModelId", fullview_modelId);
            parmas.put("ViewId", fullview_viewId);
        } else {
            parmas.put("organizeId", organizeId);
            parmas.put("targetPatchGuid", parent_pb_guid);
            parmas.put("collectDate", tvAddTime.getText().toString());
            parmas.put("ModelId", fullview_modelId);
            parmas.put("ViewId", fullview_viewId);

        }

        fullviewController.addFullview(fileOrigin, parmas, url_parmas, edtPhotName.getText().toString() + fileType, new CallBack<FullviewResult>() {
            @Override
            public void onSuccess(FullviewResult fullviewResult) {
                if (fullviewResult.getRet() == 1) {
                    mLoading.dismiss();
                    showMsg("上传全景图成功");
                    finish();
                } else {
                    mLoading.dismiss();
                    showMsg(fullviewResult.getMsg());
                }

            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });

    }

    /**
     * 获取文件
     */
    private void getLabelFile() {
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("organizeId", schedualOrganizeId);
        if (isAll) {
            parmas.put("labelId", selectedLableDto.getLabelId());
        } else {
            parmas.put("labelId", BaseApp.getInstance().selectInfo("lableId"));
        }

        parmas.put("fileName", "");
        fullviewController.getFileList(parmas, new CallBack<LabelFileDto>() {
            @Override
            public void onSuccess(LabelFileDto labelFileDto) {
                if (!labelFileDto.getData().isEmpty()) {
                    for (LabelFileDto.DataDTO dto : labelFileDto.getData()) {
                        bannerList.add(FULLVIEW_URL + "/" + dto.getFilePath());
                        photoDtoList.add(new PhotoDto(FULLVIEW_URL + "/" + dto.getFilePath()));

                    }
                    banner.setVisibility(View.VISIBLE);
                    banner.setImages(bannerList).setImageLoader(new GlideImageLoader()).setIndicatorGravity(BannerConfig.CENTER).start();

                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }

    /**
     * 生成随ID
     */
    // 生成4位随机数字+字母,
    public String getStringRandom(int length) {
        String val = "";
        Random random = new Random();

        // 参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {

            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 输出是大写字母还是小写字母
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (random.nextInt(26) + temp);
            } else if ("num".equalsIgnoreCase(charOrNum)) {
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }


    /**
     * 选取照片
     */
    private void getPhoto() {
        Set<MimeType> mimeTypeSet = new HashSet<>();
        mimeTypeSet.add(MimeType.JPEG);
        mimeTypeSet.add(MimeType.PNG);
        Matisse.from(this).choose(mimeTypeSet).countable(true).capture(true).captureStrategy(new CaptureStrategy(true, getPackageName())).maxSelectable(9).addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K)).gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size)).thumbnailScale(0.85f).imageEngine(new GlideEngine()).restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT).forResult(code_photo);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK) {
            if (requestCode == code_photo) {
                for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {
                    linSelectPhoto.setVisibility(View.GONE);
                    rlPhoto.setVisibility(View.VISIBLE);
                    linEdtPhotoName.setVisibility(View.VISIBLE);
                    fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(0));
                    int index = fileOrigin.getName().lastIndexOf(".");
                    fileType = fileOrigin.getName().substring(index);
                    edtPhotName.setText(fileOrigin.getName().substring(0, index - 1));
                    Glide.with(this).load(fileOrigin.getAbsolutePath()).into(ivPhoto);

                }
            }

        }

    }

    /**
     * 选择标签
     */
    private void showLabelPop() {
        View view = LayoutInflater.from(this).inflate(R.layout.fragment_fullview_label, null);
        setBackgroundAlpha(0.5f);
        if (mPop == null) {
            mPop = new PopupWindow(this);
        }
        if (!mPop.isShowing()) {
            mPop.setContentView(view);
            mPop.setFocusable(true);
            mPop.setOutsideTouchable(true);
            mPop.setBackgroundDrawable(new ColorDrawable());
            mPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
            mPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
            RecyclerView rvPeopleType = view.findViewById(R.id.rv_label);
            AllLabelAdapter allLabelAdapter = new AllLabelAdapter(fullviewLableDTOData);
            allLabelAdapter.setOnItemClickListener(new IOnItemClickListener() {
                @Override
                public void onClick(int pos, Object o) {
                    selectedLableDto = (FullviewLableDTO.DataDTO) o;
//                    getLabelFile();
                    tvLabel.setText(selectedLableDto.getLabelName());
                    mPop.dismiss();

                }

                @Override
                public void OnDelete(int pos, Object o) {

                }

                @Override
                public void OnClose(int pos, Object o) {

                }
            });
            VerticalRecyclerView.initialize(rvPeopleType).setAdapter(allLabelAdapter);
            mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
                @Override
                public void onDismiss() {
                    setBackgroundAlpha(1.0f);
                }
            });
            mPop.setAnimationStyle(R.style.picker_view_slide_anim);
            mPop.showAtLocation((ViewGroup) findViewById(android.R.id.content), Gravity.BOTTOM, 0, 0);
        }
    }

    /**
     * 设置popwindow默认背景变灰
     */
    public void setBackgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha;
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        getWindow().setAttributes(lp);
    }


    /**
     * 自动关闭软键盘
     */
    public void closeKeybord() {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(edtName.getWindowToken(), 0);
        }
    }

    @Override
    public void onTabSelected(TabLayout.Tab tab) {
        Logger.t("tab").e(tab.getPosition() + "");
    }

    @Override
    public void onTabUnselected(TabLayout.Tab tab) {

    }

    @Override
    public void onTabReselected(TabLayout.Tab tab) {

    }

    @Override
    public void onSelectConfig(String str) {
        if (!TextUtils.isEmpty(str)) {
            linViewPoint.setVisibility(View.VISIBLE);
            tvConfig.setText(str);
        }

    }

    @Override
    public void onSelectModel(String str, String modelId) {
        tvModel.setText(str);
        fullview_modelId = modelId;
    }

    @Override
    public void onSelectViewpoint(String str, String viewId) {
        tvViewpoint.setText(str);
        fullview_viewId = viewId;
    }


    @Override
    public void OnBannerClick(int position) {
        if (photoDtoList.size() != 0) {
            Bundle bundle = new Bundle();
            bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
            bundle.putInt(CustomParam.PhotoPosition, position);
            Intent intent = new Intent(NewFullviewActivity.this, PhotoViewActivity.class);
            intent.putExtras(bundle);
            startActivity(intent);
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {

    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    /**
     * 获取系统时间
     *
     * @return
     */
    private String getDay(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

}