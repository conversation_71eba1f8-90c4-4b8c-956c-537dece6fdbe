package com.probim.bimenew.activity.check;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.NewCheckActivity;
import com.probim.bimenew.adapter.MaterialItemAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.MateriaListBean;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :构件列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class MaterialListActivity extends BaseActivity implements View.OnClickListener {

  private List<MateriaListBean.DataBean.ListBean> allList = new ArrayList<>();
  private String organizeId;
  private MaterialItemAdapter adapter;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_project_structure);
    initView();
    loadData();
  }

  @Override protected void loadData() {
    GetProjectDao();
    getMaterialList();
  }

  @Override protected void initView() {
    TextView tvTittle = findViewById(R.id.tv_title);
    tvTittle.setText("构件列表");
    TextView tvRight = findView(R.id.tv_right, this);
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("完成");
    LinearLayout liniBack = findView(R.id.lin_back, this);
    RecyclerView rvProjectStructure = findViewById(R.id.rv_project_structure);
    adapter = new MaterialItemAdapter(this, allList);
    VerticalRecyclerView.initialize(rvProjectStructure).setAdapter(adapter);
    adapter.addRecycleItemListener(new MaterialItemAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {

      }
    });
    rvProjectStructure.setAdapter(adapter);
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

  }

  @Override protected void initRefresh() {

  }

  /**
   * 获取构件列表
   */
  private void getMaterialList() {
    mLoading.show();
    HashMap<String, String> params = new HashMap<>();
    List<String> stringList = new ArrayList<>();
    params.put("organizeId", organizeId);
    params.put("bc_guid_materialtype", "-1000");
    params.put("bm_materialcode", "");
    params.put("bm_materialname", "");
    params.put("statusIdlistjson", stringList.toString());
    params.put("ifhasrelation", stringList.toString());
    params.put("updatetimestart", "");
    params.put("updatetimeend", "");
    params.put("SortField", "bm_updatetime");
    params.put("SortType", "desc");
    // params.put("likepara", (isFirstFloor) ? "" : dataBean.getEc_code());
    CheckController checkController = new CheckController();
    checkController.getMateriaList2(params, new CallBack<MateriaListBean>() {

      @Override public void onSuccess(MateriaListBean materiaListBean) {
        if (materiaListBean.getData().getList().isEmpty()) {

          // 空值处理

        } else {

          // 循环遍历数据 设置isSelected 值为false
          for (MateriaListBean.DataBean.ListBean bean : materiaListBean.getData().getList()) {
            bean.setSelected(false);
          }
          allList.addAll(materiaListBean.getData().getList());
          adapter.notifyDataSetChanged();
        }
        mLoading.dismiss();
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      organizeId = bean.getBimProjectId();
    }
  }

  @Override public void onClick(View v) {
    switch (v.getId()) {

      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:

        Intent intent = new Intent(this, NewCheckActivity.class);
        Bundle bundle = new Bundle();
        bundle.putSerializable(CustomParam.StartForResult, (Serializable) getSelectedList());
        //intent.putExtra(CustomParam.StartForResult, getSelectedList());
        intent.putExtras(bundle);
        setResult(RESULT_OK, intent);
        finish();
        break;
    }
  }

  /**
   * 获取选中数据
   */
  private List<MateriaListBean.DataBean.ListBean> getSelectedList() {

    List<MateriaListBean.DataBean.ListBean> isSelectedAllList = new ArrayList<>();

    for (int i = 0; i < allList.size(); i++) {

      if (allList.get(i).getSelected()) {

        isSelectedAllList.add(allList.get(i));
      }
    }
    return isSelectedAllList;
  }
}
