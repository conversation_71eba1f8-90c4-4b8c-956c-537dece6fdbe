package com.probim.bimenew.activity;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.fragment.position.DrawingsPositionFragment;
import com.probim.bimenew.fragment.position.ModelPositionFragment;
import com.probim.bimenew.utils.view.NoScrollViewPager;
import java.util.ArrayList;

/**
 * Description :新建检查 定位界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/17/16:41.
 */

public class PointPositionActivity extends BaseActivity {
  @BindView(R.id.iv_back) ImageView ivBack;
  @BindView(R.id.tv_left) TextView tvLeft;
  @BindView(R.id.lin_back) LinearLayout linBack;
  @BindView(R.id.tab_segment) SegmentTabLayout tabSegment;
  @BindView(R.id.img_right) ImageView imgRight;
  @BindView(R.id.fragment_header) Toolbar fragmentHeader;
  @BindView(R.id.vp_scene) NoScrollViewPager vpScene;
  private String[] mTitles = { "模型定位", "图纸定位" };
  private ArrayList<Fragment> mFragments = new ArrayList<>();
  private int tabPosition;
  private String projectId;
  private String modelId;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_point_posintion);
    ButterKnife.bind(this);
    initView();
  }

  @Override protected void loadData() {

  }

  @Override protected void initView() {
    if (getIntent() != null) {
      projectId = getIntent().getStringExtra(CustomParam.ProjectId);
      modelId = getIntent().getStringExtra(CustomParam.ModelId);
    }

    mFragments.add(ModelPositionFragment.newInstance(projectId, modelId));
    mFragments.add(DrawingsPositionFragment.newInstance(projectId, modelId));
    tabSegment.setTabData(mTitles);

    vpScene.setAdapter(new MyPagerAdapter(getSupportFragmentManager()));

    tabSegment.setOnTabSelectListener(new OnTabSelectListener() {

      @Override public void onTabSelect(int position) {

        vpScene.setCurrentItem(position);

        tabPosition = position;
      }

      @Override public void onTabReselect(int position) {

      }
    });
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

  }

  @Override protected void initRefresh() {

  }

  @OnClick({ R.id.lin_back, R.id.img_right }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.img_right:
        break;
    }
  }

  private class MyPagerAdapter extends FragmentPagerAdapter {
    public MyPagerAdapter(FragmentManager fm) {
      super(fm);
    }

    @Override
    public int getCount() {
      return mFragments.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
      return mTitles[position];
    }

    @Override
    public Fragment getItem(int position) {
      return mFragments.get(position);
    }
  }
}
