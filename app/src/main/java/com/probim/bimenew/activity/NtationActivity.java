package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.model.PointViewDescModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.Base64Utils;
import java.util.HashMap;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Description :模型--->批注详情
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/16/15:11.
 */
public class NtationActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.tv_view_name)
  TextView tvViewName;
  @BindView(R.id.tv_view_intro)
  TextView tvViewIntro;
  @BindView(R.id.tv_view_create)
  TextView tvViewCreate;
  @BindView(R.id.tv_view_update)
  TextView tvViewUpdate;
  @BindView(R.id.btn_loading)
  Button btnLoading;
  @BindView(R.id.img)
  ImageView img;
  private String projectId;
  private String viewId;
  private String modelId;
  private String moldeViewId;
  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_model_ntation);
    ButterKnife.bind(this);
    initView();
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      projectId = bundle.getString(CustomParam.ProjectId);
      viewId = bundle.getString(CustomParam.ViewId);
      modelId = bundle.getString(CustomParam.ModelId);
      String title = bundle.getString(CustomParam.ViewPointName);
      tvTitle.setText(title);
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ViewpointID", viewId);
      ModelController controller = new ModelController();

      controller.GetNtationDetails(params, new CallBack<String>() {
        @Override
        public void onSuccess(String s) {
          displayResult(s);
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }


  }

  @Override
  protected void initView() {
    tvLeft.setText("批注");
    loadData();
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({R.id.iv_back, R.id.lin_back, R.id.btn_loading})
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.btn_loading:
        loadView();
        break;
    }
  }

  private void displayResult(String s) {

    try {

      //将得到json数据转换为一个json对象
      JSONObject jsonObject = new JSONObject(s);
      //获取"Links"的json对象,并将其转换为一个json数组
      JSONArray jsonArray = jsonObject.getJSONArray("Links");
      //通过循环获取数据,并放入list集合中
      String img_url = jsonArray.getJSONObject(0).getString("Content");

      if (!TextUtils.isEmpty(img_url)
          && img_url
          .contains("base64")) {

        Glide.with(NtationActivity.this)
            .load(Base64Utils.decode(
                img_url.split("base64,")[1]))
            .into(img);

      } else {

        Glide.with(NtationActivity.this)
            .load(R.mipmap.img_bg_project_list)
            .into(img);
      }
      PointViewDescModel model = new Gson()
          .fromJson(jsonObject.getString("Tag"), PointViewDescModel.class);
      if (TextUtils.isEmpty(model.getDescription())) {
        tvViewIntro.setText("<无描述>");
      } else {
        tvViewIntro.setText(model.getDescription());
      }

      tvViewName.setText(jsonObject.getString("Name"));
      tvViewCreate.setText(jsonObject.getString("Editor"));
      tvViewUpdate.setText(jsonObject.getString("EditTime").replace("T", "  "));
      moldeViewId = jsonObject.getString("ViewID");
    } catch (JSONException e) {
      e.printStackTrace();
    }

  }

  private void loadView() {
    Bundle bundle = new Bundle();
    bundle.putString(CustomParam.ViewId, moldeViewId);
    bundle.putString(CustomParam.ProjectId, projectId);
    bundle.putString(CustomParam.ModelId, modelId);
    bundle.putString(CustomParam.ViewPointId, viewId);
    bundle.putString(CustomParam.TvLeft, "批注");
    Intent intent = new Intent(this, LoadModelViewActivity.class);
    intent.putExtras(bundle);
    startActivity(intent);

  }

}
