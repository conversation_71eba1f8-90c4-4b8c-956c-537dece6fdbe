package com.probim.bimenew.activity.fullview.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class AllFullViewItemDTO {


    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("PsGuid")
        private String psGuid;
        @SerializedName("PbGuid")
        private String pbGuid;
        @SerializedName("PsScenename")
        private String psScenename;
        @SerializedName("PsCollecteddatetime")
        private String psCollecteddatetime;
        @SerializedName("ModelId")
        private Object modelId;
        @SerializedName("ViewId")
        private String viewId;
        @SerializedName("PchChname")
        private String pchChname;

        public String getPsGuid() {
            return psGuid;
        }

        public void setPsGuid(String psGuid) {
            this.psGuid = psGuid;
        }

        public String getPbGuid() {
            return pbGuid;
        }

        public void setPbGuid(String pbGuid) {
            this.pbGuid = pbGuid;
        }

        public String getPsScenename() {
            return psScenename;
        }

        public void setPsScenename(String psScenename) {
            this.psScenename = psScenename;
        }

        public String getPsCollecteddatetime() {
            return psCollecteddatetime;
        }

        public void setPsCollecteddatetime(String psCollecteddatetime) {
            this.psCollecteddatetime = psCollecteddatetime;
        }

        public Object getModelId() {
            return modelId;
        }

        public void setModelId(Object modelId) {
            this.modelId = modelId;
        }

        public String getViewId() {
            return viewId;
        }

        public void setViewId(String viewId) {
            this.viewId = viewId;
        }

        public String getPchChname() {
            return pchChname;
        }

        public void setPchChname(String pchChname) {
            this.pchChname = pchChname;
        }
    }
}
