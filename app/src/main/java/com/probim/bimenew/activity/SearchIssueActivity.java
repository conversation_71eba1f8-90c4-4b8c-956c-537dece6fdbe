package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.TextView.OnEditorActionListener;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.IssueListAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.model.ProblemListModel;
import com.probim.bimenew.model.ProblemListModel.DataBean.ItemsBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.DeleteIssueResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :问题搜索界面 Author : Gary Email  : <EMAIL> Date   :
 * 2018/8/9/16:56.
 */
public class SearchIssueActivity extends BaseActivity {

    @BindView(R.id.edt_search)
    EditText edtSearch;
    @BindView(R.id.rv_question)
    RecyclerView rvQuestion;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    @BindView(R.id.tv_search_result)
    TextView tvSearchResult;
    @BindView(R.id.lin_search_result)
    LinearLayout linSearchResult;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    private List<ItemsBean> list = new ArrayList<>();
    private IssueListAdapter adapter;
    private String organizeId;
    private IssueController controller;
    private String projectId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_search_problem);
        ButterKnife.bind(this);
        initView();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        edtSearch.setHint("  请输入问题");
        edtSearch.setFocusable(true);
        edtSearch.setFocusableInTouchMode(true);
        edtSearch.requestFocus();
        //显示软键盘
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        GetProjectDao();
        initRecycleview();
        setListener();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        // RecyclerView 设置布局管理器
        rvQuestion.setLayoutManager(new LinearLayoutManager(this));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvQuestion.addItemDecoration(
                new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        //动画
        rvQuestion.setItemAnimator(new DefaultItemAnimator());

        adapter = new IssueListAdapter(this, list, projectId);

        rvQuestion.setAdapter(adapter);

        adapter.addRecycleItemListener(new IssueListAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                Bundle bundle = new Bundle();
                Intent intent = new Intent(SearchIssueActivity.this, IssueDetailActivity.class);
                bundle.putString(CustomParam.IssueId, list.get(pos).getIssueId());
                intent.putExtras(bundle);
                startActivity(intent);
            }

            @Override
            public void OnDelete(int pos, Object o) {
                DeleteProblem(list.get(pos).getIssueId(), pos);
            }
        });
    }

    @Override
    protected void initRefresh() {

    }

    //添加输入框监听以及键盘搜索按键监听
    private void setListener() {

        //键盘搜索键
        edtSearch.setOnEditorActionListener(new OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
                if (i == EditorInfo.IME_ACTION_SEARCH) {
                    GetProblemList("", "", edtSearch.getText().toString().trim());
                    return true;
                }
                return false;
            }
        });
    }

    @OnClick({R.id.tv_cancle, R.id.rv_status_layout})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_cancle:
                finish();
                break;
            case R.id.rv_status_layout:
                break;
        }
    }

    /**
     * 获取问题列表
     */
    private void GetProblemList(String typeId, String statusId, String keyWord) {

        HashMap<String, String> params = new HashMap<>();
        params.put("OrganizeId", projectId);
        params.put("IssueTypeId", typeId);
        params.put("IssueStatusId", statusId);
        params.put("keyword", keyWord);
        params.put("UserType", "");
        params.put("PageIndex", "1");
        params.put("PageSize", Integer.MAX_VALUE + "");
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ContainsArchive", "");
        controller = new IssueController();
        controller.GetProblemList(params, new CallBack<ProblemListModel>() {
            @Override
            public void onSuccess(ProblemListModel problemListModel) {

                if (problemListModel.getRet() == 1) {
                    if (problemListModel.getData().getItems().size() == 0) {
                        noData();
                        list.clear();
                        adapter.notifyDataSetChanged();
                    } else {
                        linSearchResult.setVisibility(View.VISIBLE);
                        rvStatusLayout.setVisibility(View.GONE);
                        tvSearchResult.setText("(  " + problemListModel.getData().getItems().size() + "  )");
                        list.clear();
                        list.addAll(problemListModel.getData().getItems());
                        adapter.notifyDataSetChanged();
                    }
                } else {
                    showMsg("暂无搜索结果");
                }

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();
            projectId = bean.getProjectID();
        }
    }

    /**
     * 删除问题
     */
    private void DeleteProblem(String id, int postion) {
        HashMap<String, String> params = new HashMap<>();
        params.put("IssueId", id);

        controller.DeleteProblem(params, new CallBack<DeleteIssueResult>() {
            @Override
            public void onSuccess(DeleteIssueResult deleteIssueResult) {
                if (1 == deleteIssueResult.getRet()) {
                    list.remove(postion);
                    adapter.notifyItemRemoved(postion);
                    adapter.notifyItemRangeChanged(postion, list.size());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 暂无数据
     */
    private void noData() {
        rvStatusLayout.setVisibility(View.VISIBLE);
        tvStatus.setText("未搜索到问题");
    }
}
