package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.CommentsAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.DeleteCommentResult;
import com.probim.bimenew.result.IssueDetailResult;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.SpacesItemDecoration;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :评论界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/27/16:48.
 */
public class CommentsActivity extends BaseActivity {

  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.img_right)
  ImageView imgRight;
  @BindView(R.id.rv_comments)
  RecyclerView rvComments;
  @BindView(R.id.iv_status)
  ImageView ivStatus;
  @BindView(R.id.tv_status)
  TextView tvStatus;
  @BindView(R.id.rv_status_layout)
  RelativeLayout rvStatusLayout;
  @BindView(R.id.tv_right)
  TextView tvRight;
  private IssueController issueController = new IssueController();
  private CommentsAdapter commentsRvAdapter;
  private String organizeId;
  private String issueId;
  private List<IssueDetailResult.DataBean.CommentsBean> dataBeanList = new ArrayList<>();
  private List<IssueDetailResult.DataBean.JoinersBean> joinersBeanList = new ArrayList<>();

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_comments);
    ButterKnife.bind(this);
    initView();
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {
    tvTitle.setText("评论");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("新增评论");
    tvStatus.setText("暂无评论");
    ivStatus.setImageResource(R.mipmap.ic_comments_empty);
    if (getIntent() != null) {
      issueId = getIntent().getStringExtra(CustomParam.IssueId);
      organizeId = getIntent().getStringExtra(CustomParam.OrganizeId);
      initRecycleview();
    }
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {
    //为 RecyclerView 设置布局管理器
    rvComments.setLayoutManager(new LinearLayoutManager(this));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    //rvComments.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
    //动画
    rvComments.setItemAnimator(new DefaultItemAnimator());

    rvComments.addItemDecoration(new SpacesItemDecoration(20));
    //    rvComments.getRecycledViewPool().setMaxRecycledViews(VIEWHOLDERTYPE, 0);
    commentsRvAdapter = new CommentsAdapter(this, dataBeanList, organizeId);

    commentsRvAdapter.addRecycleItemListener(new CommentsAdapter.OnRecycleItemListener() {
      @Override public void OnItemReply(int pos, Object o) {
        String talkId = (String) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.IssueId, issueId);
        bundle.putString(CustomParam.CommentsTalkId, talkId);
        Intent intent = new Intent(CommentsActivity.this, NewCommentsActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
      }

      @Override public void OnInnerItemReply(int pos, Object o) {
        String talkId = (String) o;
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.IssueId, issueId);
        bundle.putString(CustomParam.CommentsTalkId, talkId);
        Intent intent = new Intent(CommentsActivity.this, NewCommentsActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
      }

      @Override public void OnItemDelete(int pos, Object o) {

        IssueDetailResult.DataBean.CommentsBean commentsBean =
            (IssueDetailResult.DataBean.CommentsBean) o;
        if (Hawk.get(CustomParam.UserId).equals(commentsBean.getUserid())) {
          deleteComments(commentsBean.getIssue_talkid(), pos);
        } else {
          showMsg("只能删除自己的评论");
        }
      }

      @Override public void OnInnerItemDelete(int pos, Object o) {
      }
    });

    rvComments.setAdapter(commentsRvAdapter);
  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({ R.id.lin_back, R.id.tv_right })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        Bundle bundle = new Bundle();
        bundle.putString(CustomParam.IssueId, issueId);
        bundle.putString(CustomParam.JoinUser, JsonHelper.toJson(joinersBeanList));
        Intent intent = new Intent(this, NewCommentsActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        break;
    }
  }

  /**
   * 删除评论
   */
  private void deleteComments(String messageId, int position) {
    HashMap<String, String> params = new HashMap<>();
    params.put("Token", Hawk.get(CustomParam.Token));
    params.put("Issue_TalkId", messageId);
    params.put("IssueId", issueId);
    issueController.deleteComments(params, new CallBack<DeleteCommentResult>() {

      @Override public void onSuccess(DeleteCommentResult deleteCommentResult) {
        if (deleteCommentResult.getRet() == 1) {
          showMsg("删除评论成功");
          dataBeanList.remove(position);
          commentsRvAdapter.notifyDataSetChanged();
        } else {
          showMsg("删除评论失败");
        }
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 获取问题评论列表
   */
  private void getIssueComments() {
    HashMap<String, String> params = new HashMap<>();
    params.put("IssueID", issueId);
    params.put("Token", Hawk.get(CustomParam.Token));
    issueController.getIssueDetail(params, new CallBack<IssueDetailResult>() {

      @Override
      public void onSuccess(IssueDetailResult result) {
        if (1 == result.getRet()) {
          if (result.getData().getComments().isEmpty()) {
            rvStatusLayout.setVisibility(View.VISIBLE);
          } else {
            rvStatusLayout.setVisibility(View.GONE);
            dataBeanList.clear();
            dataBeanList.addAll(result.getData().getComments());
            commentsRvAdapter.notifyDataSetChanged();
          }
          if (!result.getData().getJoiners().isEmpty()){
            joinersBeanList.clear();
            joinersBeanList.addAll(result.getData().getJoiners());
          }
        }
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  @Override protected void onResume() {
    super.onResume();
    getIssueComments();
  }
}
