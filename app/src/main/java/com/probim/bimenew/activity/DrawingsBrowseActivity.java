package com.probim.bimenew.activity;

import android.app.Activity;
import android.app.Instrumentation;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import androidx.annotation.Nullable;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.model.DrawingsModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.imageview.PinchImageView;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static android.view.MotionEvent.ACTION_DOWN;

/**
 * Description :通用webview界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/17:32.
 */
public class DrawingsBrowseActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.iv_drawings)
  PinchImageView ivDrawings;
  private int imageWidth;
  private int imageHeight;
  private int left;
  private int top;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_drawings_preview);
    ButterKnife.bind(this);
    StatusBarUtils.transparencyBar(this);
    initView();
    loadData();
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      String twoDimensId = bundle.getString(CustomParam.TwoDimensId);
      String name = bundle.getString(CustomParam.TwoDimensName);
      String projectId = bundle.getString(CustomParam.ProjectId);
      String modelId = bundle.getString(CustomParam.ModelId);
      tvTitle.setText(name + "");
      tvLeft.setText("图纸");
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ModelID", modelId);
      params.put("VersionNO", "");
      params.put("FileType", "PlanView");
      params.put("FileName", twoDimensId);

      ModelController controller = new ModelController();

      controller.GetTwoDimensionalDetails(params, new CallBack<DrawingsModel>() {
        @Override
        public void onSuccess(DrawingsModel drawingsModel) {
         /* Glide.with(getApplicationContext())
              .load(Base64Utils.decode(drawingsModel.getImagebase64()))
              .into(ivDrawings);*/
        /*  Glide.with(getApplicationContext())
              .load(Base64Utils.decode(drawingsModel.getImagebase64())).asBitmap().into(
              new SimpleTarget<Bitmap>() {
                @Override public void onResourceReady(Bitmap resource,
                    GlideAnimation<? super Bitmap> glideAnimation) {
                  imageWidth = resource.getWidth();
                  imageHeight = resource.getHeight();
                  ivDrawings.setImageBitmap(resource);

                  Logger.t("图纸大小------------>")
                      .e("x----->" + imageWidth + ",y----->" + imageHeight);

                  //到指定位置
                  //ivDrawings.zoomTo(1f, 4.125549f, 14.236945f);

                 *//* setMoveToTop(200, DrawingsBrowseActivity.this);
                  new Thread(new Runnable() {
                    @Override public void run() {
                      moni(100, 0);
                    }
                  }).start();*//*
                }
              });*/
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }
  }

  @Override
  protected void initView() {
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {
  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({ R.id.iv_back, R.id.lin_back, R.id.tv_right })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:

        break;
    }
  }

  private View.OnTouchListener movingEventListener = new View.OnTouchListener() {
    int lastX, lastY, x, y;

    @Override
    public boolean onTouch(View v, MotionEvent event) {
      switch (event.getAction()) {
        case ACTION_DOWN:
          lastX = (int) event.getRawX();
          lastY = (int) event.getRawY();
          x = (int) event.getRawX();
          y = (int) event.getRawY();
          break;
        case MotionEvent.ACTION_MOVE:
          int dx = (int) event.getRawX() - lastX;
          int dy = (int) event.getRawY() - lastY;

          left = v.getLeft() + dx;
          top = v.getTop() + dy;
          int right = v.getRight() + dx;
          int bottom = v.getBottom() + dy;
          // 设置不能出界
          if (left < 0) {
            left = 0;
            right = left + v.getWidth();
          }

          if (right > imageWidth) {
            right = imageWidth;
            left = right - v.getWidth();
          }

          if (top < 0) {
            top = 0;
            bottom = top + v.getHeight();
          }

          if (bottom > imageHeight) {
            bottom = imageHeight;
            top = bottom - v.getHeight();
          }

          v.layout(left, top, right, bottom);
          //v.layout(100,100,0,0);
          lastX = (int) event.getRawX();
          lastY = (int) event.getRawY();
          showMsg("xxxxxxxxxxxxx" + left + "yyyyyyyyyyy" + top);
          break;
        case MotionEvent.ACTION_UP:
          //检测移动的距离，如果很微小可以认为是点击事件
          if (Math.abs(event.getRawX() - x) < 10 && Math.abs(event.getRawY() - y) < 10) {
            try {
              Field field = View.class.getDeclaredField("mListenerInfo");
              field.setAccessible(true);
              Object object = field.get(v);
              field = object.getClass().getDeclaredField("mOnClickListener");
              field.setAccessible(true);
              object = field.get(object);
              if (object != null && object instanceof View.OnClickListener) {
                ((View.OnClickListener) object).onClick(v);
              }
            } catch (Exception e) {
            }
          }
          break;
      }
      return true;
    }
  };

  /**
   * 坐标转换
   */

  //其中，descendant 是子View，coord是在descendant中的坐标值。而这个函数是父View的函数。当然，我们可以可以加一个参数，指定父View。
  public float getDescendantCoordRelativeToSelf(View descendant, int[] coord) {
    float scale = 1.0f;
    float[] pt = { coord[0], coord[1] };
    //坐标值进行当前窗口的矩阵映射，比如View进行了旋转之类，它的坐标系会发生改变。map之后，会把点转换为改变之前的坐标。
    descendant.getMatrix().mapPoints(pt);
    //转换为直接父窗口的坐标
    scale *= descendant.getScaleX();
    pt[0] += descendant.getLeft();
    pt[1] += descendant.getTop();
    ViewParent viewParent = descendant.getParent();
    //循环获得父窗口的父窗口，并且依次计算在每个父窗口中的坐标
    while (viewParent instanceof View && viewParent != this) {
      final View view = (View) viewParent;
      view.getMatrix().mapPoints(pt);
      scale *= view.getScaleX();//这个是计算X的缩放值。此处可以不管
      //转换为相当于可视区左上角的坐标，scrollX，scollY是去掉滚动的影响
      pt[0] += view.getLeft() - view.getScrollX();
      pt[1] += view.getTop() - view.getScrollY();
      viewParent = view.getParent();
    }
    coord[0] = (int) Math.round(pt[0]);
    coord[1] = (int) Math.round(pt[1]);
    return scale;
  }

  /**
   * 模拟用户滑动操作
   *
   * @param view 要触发操作的view
   * @param type 模拟操作类型：均匀滑动、快速滑动
   * @param p1x 滑动的起始点x坐标
   * @param p1y 滑动的起始点y坐标
   * @param p2x 滑动的终点x坐标
   * @param p2y 滑动的终点y坐标
   */
  private static void analogUserScroll(View view, final int type, final float p1x, final float p1y,
      final float p2x, final float p2y) {
    if (view == null) {
      return;
    }
    long downTime = SystemClock.uptimeMillis();//模拟按下去的时间

    long eventTime = downTime;

    float pX = p1x;
    float pY = p1y;
    int speed = 0;//快速滑动
    float touchTime = 116;//模拟滑动时发生的触摸事件次数

    //平均每次事件要移动的距离
    float perX = (p2x - p1x) / touchTime;
    float perY = (p2y - p1y) / touchTime;

    boolean isReversal = perX < 0 || perY < 0;//判断是否反向：手指从下往上滑动，或者手指从右往左滑动
    boolean isHandY = Math.abs(perY) > Math.abs(perX);//判断是左右滑动还是上下滑动

    if (type == 1) {//加速滑动
      touchTime = 10;//如果是快速滑动，则发生的触摸事件比均匀滑动更少
      speed = isReversal ? -20 : 20;//反向移动则坐标每次递减
    }

    //模拟用户按下
    MotionEvent downEvent = MotionEvent.obtain(downTime, eventTime,
        ACTION_DOWN, pX, pY, 0);
    view.onTouchEvent(downEvent);

    //模拟移动过程中的事件
    List<MotionEvent> moveEvents = new ArrayList<>();
    boolean isSkip = false;
    for (int i = 0; i < touchTime; i++) {

      pX += (perX + speed);
      pY += (perY + speed);
      if ((isReversal && pX < p2x) || (!isReversal && pX > p2x)) {
        pX = p2x;
        isSkip = !isHandY;
      }

      if ((isReversal && pY < p2y) || (!isReversal && pY > p2y)) {
        pY = p2y;
        isSkip = isHandY;
      }
      eventTime += 20.0f;//事件发生的时间要不断递增
    /*  MotionEvent moveEvent = getMoveEvent(downTime, eventTime, pX, pY);
      moveEvents.add(moveEvent);
      view.onTouchEvent(moveEvent);*/
      if (type == 1) {//加速滑动
        speed += (isReversal ? -70 : 70);
      }
      if (isSkip) {
        break;
      }
    }

    //模拟手指离开屏幕
    MotionEvent upEvent = MotionEvent.obtain(downTime, eventTime,
        MotionEvent.ACTION_UP, pX, pY, 0);
    view.onTouchEvent(upEvent);

    //回收触摸事件
    downEvent.recycle();
    for (int i = 0; i < moveEvents.size(); i++) {
      moveEvents.get(i).recycle();
    }
    upEvent.recycle();
  }

  public static void setMoveToTop(int distance, Activity activity) {
    activity.dispatchTouchEvent(
        MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(),
            MotionEvent.ACTION_DOWN, 400, 500, 0));
    activity.dispatchTouchEvent(
        MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(),
            MotionEvent.ACTION_MOVE, 400, 500 + distance, 0));
    activity.dispatchTouchEvent(
        MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(),
            MotionEvent.ACTION_UP, 400, 500 + distance, 0));
  }

  private void moni(float x, float y) {

    Instrumentation inst = new Instrumentation();
    long dowTime = SystemClock.uptimeMillis();
    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
        MotionEvent.ACTION_DOWN, x, y, 0));
    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime,
        MotionEvent.ACTION_MOVE, x, y, 0));
    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + 20,
        MotionEvent.ACTION_MOVE, x + 20, y, 0));
    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + 30,
        MotionEvent.ACTION_MOVE, x + 40, y, 0));
    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + 40,
        MotionEvent.ACTION_MOVE, x + 60, y, 0));
    inst.sendPointerSync(MotionEvent.obtain(dowTime, dowTime + 40,
        MotionEvent.ACTION_UP, x + 60, y, 0));
  }
}
