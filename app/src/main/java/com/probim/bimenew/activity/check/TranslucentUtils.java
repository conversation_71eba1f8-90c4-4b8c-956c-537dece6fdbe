package com.probim.bimenew.activity.check;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window;
import android.view.WindowManager;
import java.lang.reflect.Field;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/12/1/15:01
 */
public class TranslucentUtils {

  //=============沉侵式==(begin)=================
  private static View mStatusBarView;

  /** 设置全屏沉侵式效果 */
  public static void setNoStatusBarFullMode(Activity activity) {
    // sdk 4.4
    if (Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
      Window window = activity.getWindow();
      window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
      window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);

      if (mStatusBarView != null) {
        ViewGroup root = (ViewGroup) activity.findViewById(android.R.id.content);
        root.removeView(mStatusBarView);
      }
      return;
    }

    // sdk 5.x
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      Window window = activity.getWindow();
      window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
      window.setStatusBarColor(Color.TRANSPARENT);
      return;
    }
  }

  /** 设置控件的paddingTop, 使它不被StatusBar覆盖 */
  public static void setStatusBarPadding(View view) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
      int marginTop = getStatusBarHeight(view.getContext());
      view.setPadding(view.getPaddingLeft(), marginTop,
          view.getPaddingRight(), view.getPaddingBottom());
      return;
    }
  }

  public static void setStatusBarColor(Activity activity, int statusColor) {
    // sdk 4.4
    if (Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
        ViewGroup root = (ViewGroup) activity.findViewById(android.R.id.content);
        if (mStatusBarView == null) {
          //为了适配一些特殊机型的状态栏颜色无法改变，同时高度和系统原生的高度区别，所以这里重新创建一个View用于覆盖状态栏来实现效果
          mStatusBarView = new View(activity);
          mStatusBarView.setBackgroundColor(statusColor);
        } else {
          // 先解除父子控件关系，否则重复把一个控件多次
          // 添加到其它父控件中会出错
          ViewParent parent = mStatusBarView.getParent();
          if (parent != null) {
            ViewGroup viewGroup = (ViewGroup) parent;
            if (viewGroup != null) {
              viewGroup.removeView(mStatusBarView);
            }
          }
        }
        ViewGroup.LayoutParams param = new ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            getStatusBarHeight(activity));
        root.addView(mStatusBarView, param);
      }
      return;
    }

    // sdk 5.x
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      activity.getWindow().setStatusBarColor(statusColor);
      return;
    }
  }

  /**
   * 通过反射的方式获取状态栏高度，
   * 一般为24dp，有些可能较特殊，所以需要反射动态获取
   */
  private static int getStatusBarHeight(Context context) {
    try {
      Class<?> clazz = Class.forName("com.android.internal.R$dimen");
      Object obj = clazz.newInstance();
      Field field = clazz.getField("status_bar_height");
      int id = Integer.parseInt(field.get(obj).toString());
      return context.getResources().getDimensionPixelSize(id);
    } catch (Exception e) {
      e.printStackTrace();
      System.out.println("-------无法获取到状态栏高度");
    }
    return dp2px(24);
  }

  private static int dp2px(int dp) {
    return (int) (dp * Resources.getSystem().getDisplayMetrics().density);
  }


  public static void setTRANSLUCENT(Activity activity) {

    if (Build.VERSION.SDK_INT >= 21) {
      View decorView = activity.getWindow().getDecorView();
      /*int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
          | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
          | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;*/

      int option =
          View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
              | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
      decorView.setSystemUiVisibility(option);
      activity.getWindow().setNavigationBarColor(Color.TRANSPARENT);
      activity.getWindow().setStatusBarColor(Color.TRANSPARENT);
    }
  }
}
