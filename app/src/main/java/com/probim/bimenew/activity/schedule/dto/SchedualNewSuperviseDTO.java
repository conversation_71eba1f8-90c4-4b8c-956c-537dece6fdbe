package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualNewSuperviseDTO {

    @SerializedName("MobileSupervise_Name")
    private String mobileSupervise_Name;
    @SerializedName("MobileSupervise_ProjectID")
    private String mobileSupervise_ProjectID;
    @SerializedName("MobileSupervise_Unittime")
    private String mobileSupervise_Unittime;
    @SerializedName("MobileSupervise_Createusername")
    private String mobileSupervise_Createusername;
    private String organizeId;

    public String getMobileSupervise_Name() {
        return mobileSupervise_Name;
    }

    public void setMobileSupervise_Name(String mobileSupervise_Name) {
        this.mobileSupervise_Name = mobileSupervise_Name;
    }

    public String getMobileSupervise_ProjectID() {
        return mobileSupervise_ProjectID;
    }

    public void setMobileSupervise_ProjectID(String mobileSupervise_ProjectID) {
        this.mobileSupervise_ProjectID = mobileSupervise_ProjectID;
    }

    public String getMobileSupervise_Unittime() {
        return mobileSupervise_Unittime;
    }

    public void setMobileSupervise_Unittime(String mobileSupervise_Unittime) {
        this.mobileSupervise_Unittime = mobileSupervise_Unittime;
    }

    public String getMobileSupervise_Createusername() {
        return mobileSupervise_Createusername;
    }

    public void setMobileSupervise_Createusername(String mobileSupervise_Createusername) {
        this.mobileSupervise_Createusername = mobileSupervise_Createusername;
    }

    public String getOrganizeId() {
        return organizeId;
    }

    public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
    }
}
