package com.probim.bimenew.activity.schedule.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.interfaces.IOnItemSchedualMaterailClickListener;
import com.probim.bimenew.result.MateriaOriginListBean;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualPartsAdapter extends RecyclerView.Adapter<SchedualPartsAdapter.ViewHolder> {

    private List<MateriaOriginListBean.DataBean.ListBean> dtoList = new ArrayList<>();
    private IOnItemSchedualMaterailClickListener onItemClickListener;

    public SchedualPartsAdapter(List<MateriaOriginListBean.DataBean.ListBean> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_parts, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        MateriaOriginListBean.DataBean.ListBean dto = dtoList.get(position);
        holder.tvParts.setText(dto.getBm_materialname());

    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualMaterailClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_parts)
        TextView tvParts;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
