package com.probim.bimenew.activity.flow;

import android.os.Bundle;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.AllFlowDto;

public class LoadFlowActivity extends BaseActivity {
    private final String flowUrl = Hawk.get(CustomParam.Flow_URL);

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_flow_web);
        initView();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        TextView tvTittle = findViewById(R.id.tv_title);
        LinearLayout linBack = findViewById(R.id.lin_back);
        linBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        WebView webView = findView(R.id.web_x);
        webView.setWebViewClient(new WebViewClient());
        webView.setWebChromeClient(new WebChromeClient());
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setDomStorageEnabled(true);
        webView.getSettings().setBuiltInZoomControls(true);
        webView.getSettings().supportZoom();
        webView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
        webView.getSettings().setJavaScriptCanOpenWindowsAutomatically(true);
        if (getIntent() != null) {
            //读取数据
            AllFlowDto dto = (AllFlowDto) getIntent().getSerializableExtra("AllFlowDto");
            tvTittle.setText(dto.getTitle());
            String atPara = dto.getAtPara().replaceAll("@", "&");
            String token = Hawk.get(CustomParam.CCToken);
            String account = Hawk.get(CustomParam.ACCOUNT);
            String url = flowUrl + "/WF/MyFlowGener.htm?WorkID=" + dto.getWorkID() + "&NodeID=" + dto.getFK_Node() + "&FK_Node=" + dto.getFK_Node() + "&FID=" + dto.getFID() + "&UserNo=" + account + "&Token=" + token + "&FK_Flow=" + dto.getFK_Flow() + "&PWorkID=" + dto.getPWorkID() + "&IsRead=" + "0" + "&Paras=1" + atPara + "&PageName=MyFlowGener";
            webView.loadUrl(url);
            Logger.t("流程地址").e(url);

        }
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }
}
