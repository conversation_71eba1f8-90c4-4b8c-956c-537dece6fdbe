package com.probim.bimenew.activity.fullview.dto;

public class ViewPointDTO {


    private PosDTO pos;
    private TargetDTO target;
    private FocalOffsetDTO focalOffset;

    public PosDTO getPos() {
        return pos;
    }

    public void setPos(PosDTO pos) {
        this.pos = pos;
    }

    public TargetDTO getTarget() {
        return target;
    }

    public void setTarget(TargetDTO target) {
        this.target = target;
    }

    public FocalOffsetDTO getFocalOffset() {
        return focalOffset;
    }

    public void setFocalOffset(FocalOffsetDTO focalOffset) {
        this.focalOffset = focalOffset;
    }

    public static class PosDTO {
        public PosDTO(Double x, Double y, Double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        private Double x;
        private Double y;
        private Double z;

        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getZ() {
            return z;
        }

        public void setZ(Double z) {
            this.z = z;
        }
    }

    public static class TargetDTO {
        public TargetDTO(Double x, Double y, Double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        private Double x;
        private Double y;
        private Double z;

        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getZ() {
            return z;
        }

        public void setZ(Double z) {
            this.z = z;
        }
    }

    public static class FocalOffsetDTO {
        public FocalOffsetDTO(Double x, Double y, Double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        private Double x;
        private Double y;
        private Double z;

        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getZ() {
            return z;
        }

        public void setZ(Double z) {
            this.z = z;
        }
    }
}
