package com.probim.bimenew.activity.check;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.result.IssueDetailResult;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/8/19/11:36
 */
public class UserJoinerAdapter
    extends RecyclerView.Adapter<UserJoinerAdapter.ViewHolder> {
  private Context mContex;
  private List<IssueDetailResult.DataBean.JoinersBean> mDatas;
  private OnRecycleItemListener mRecycleItemListener;

  public UserJoinerAdapter(Context mContex,
                           List<IssueDetailResult.DataBean.JoinersBean> mDatas) {
    this.mContex = mContex;
    this.mDatas = mDatas;
  }

  @NonNull @Override public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
    return new ViewHolder(
        LayoutInflater.from(mContex).inflate(R.layout.item_rv_joiner, null, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
    IssueDetailResult.DataBean.JoinersBean dto = mDatas.get(i);
    viewHolder.tvTaskName.setText(dto.getRealName());
    viewHolder.ivDelete.setVisibility(View.VISIBLE);
    viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View v) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnRecycleItemClick(i, dto);
        }
      }
    });
  }

  @Override public int getItemCount() {
    return mDatas.size();
  }

  static
  class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_task_name) TextView tvTaskName;
    @BindView(R.id.iv_delete)
    ImageView ivDelete;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
