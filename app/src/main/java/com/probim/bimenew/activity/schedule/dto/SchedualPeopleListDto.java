package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SchedualPeopleListDto {


    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("Token")
        private Object token;
        @SerializedName("Mobile_UserId")
        private int mobile_UserId;
        @SerializedName("Mobile_UserType")
        private String mobile_UserType;
        @SerializedName("Mobile_PlanNum")
        private int mobile_PlanNum;
        @SerializedName("Mobile_Unit")
        private String mobile_Unit;
        private String organizeId;
        @SerializedName("Mobile_AddNum")
        private String mobile_AddNum;

        public Object getToken() {
            return token;
        }

        public void setToken(Object token) {
            this.token = token;
        }

        public int getMobile_UserId() {
            return mobile_UserId;
        }

        public void setMobile_UserId(int mobile_UserId) {
            this.mobile_UserId = mobile_UserId;
        }

        public String getMobile_UserType() {
            return mobile_UserType;
        }

        public void setMobile_UserType(String mobile_UserType) {
            this.mobile_UserType = mobile_UserType;
        }

        public int getMobile_PlanNum() {
            return mobile_PlanNum;
        }

        public void setMobile_PlanNum(int mobile_PlanNum) {
            this.mobile_PlanNum = mobile_PlanNum;
        }

        public String getMobile_Unit() {
            return mobile_Unit;
        }

        public void setMobile_Unit(String mobile_Unit) {
            this.mobile_Unit = mobile_Unit;
        }

        public String getOrganizeId() {
            return organizeId;
        }

        public void setOrganizeId(String organizeId) {
            this.organizeId = organizeId;
        }

        public String getMobile_AddNum() {
            return mobile_AddNum;
        }

        public void setMobile_AddNum(String mobile_AddNum) {
            this.mobile_AddNum = mobile_AddNum;
        }
    }
}
