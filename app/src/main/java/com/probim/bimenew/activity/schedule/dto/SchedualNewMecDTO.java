package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualNewMecDTO {

    @SerializedName("MaterialsDetial_Name")
    private String materialsDetial_Name;
    @SerializedName("MaterialsDetial_PlanNum")
    private String materialsDetial_PlanNum;
    @SerializedName("MaterialsDetial_AddNum")
    private String materialsDetial_AddNum;
    @SerializedName("MaterialsDetial_AdddelNum")
    private String materialsDetial_AdddelNum;
    @SerializedName("MaterialsDetial_NewNum")
    private String materialsDetial_NewNum;
    @SerializedName("MaterialsDetial_type")
    private String materialsDetial_type;
    @SerializedName("MaterialsDetial_state")
    private String materialsDetial_state;
    @SerializedName("MaterialsDetial_ProjectID")
    private String materialsDetial_ProjectID;
    @SerializedName("MaterialsDetial_Unittime")
    private String materialsDetial_Unittime;
    @SerializedName("MaterialsDetial_createuser")
    private String materialsDetial_createuser;
    private String organizeId;

    public String getMaterialsDetial_Name() {
        return materialsDetial_Name;
    }

    public void setMaterialsDetial_Name(String materialsDetial_Name) {
        this.materialsDetial_Name = materialsDetial_Name;
    }

    public String getMaterialsDetial_PlanNum() {
        return materialsDetial_PlanNum;
    }

    public void setMaterialsDetial_PlanNum(String materialsDetial_PlanNum) {
        this.materialsDetial_PlanNum = materialsDetial_PlanNum;
    }

    public String getMaterialsDetial_AddNum() {
        return materialsDetial_AddNum;
    }

    public void setMaterialsDetial_AddNum(String materialsDetial_AddNum) {
        this.materialsDetial_AddNum = materialsDetial_AddNum;
    }

    public String getMaterialsDetial_AdddelNum() {
        return materialsDetial_AdddelNum;
    }

    public void setMaterialsDetial_AdddelNum(String materialsDetial_AdddelNum) {
        this.materialsDetial_AdddelNum = materialsDetial_AdddelNum;
    }

    public String getMaterialsDetial_NewNum() {
        return materialsDetial_NewNum;
    }

    public void setMaterialsDetial_NewNum(String materialsDetial_NewNum) {
        this.materialsDetial_NewNum = materialsDetial_NewNum;
    }

    public String getMaterialsDetial_type() {
        return materialsDetial_type;
    }

    public void setMaterialsDetial_type(String materialsDetial_type) {
        this.materialsDetial_type = materialsDetial_type;
    }

    public String getMaterialsDetial_state() {
        return materialsDetial_state;
    }

    public void setMaterialsDetial_state(String materialsDetial_state) {
        this.materialsDetial_state = materialsDetial_state;
    }

    public String getMaterialsDetial_ProjectID() {
        return materialsDetial_ProjectID;
    }

    public void setMaterialsDetial_ProjectID(String materialsDetial_ProjectID) {
        this.materialsDetial_ProjectID = materialsDetial_ProjectID;
    }

    public String getMaterialsDetial_Unittime() {
        return materialsDetial_Unittime;
    }

    public void setMaterialsDetial_Unittime(String materialsDetial_Unittime) {
        this.materialsDetial_Unittime = materialsDetial_Unittime;
    }

    public String getMaterialsDetial_createuser() {
        return materialsDetial_createuser;
    }

    public void setMaterialsDetial_createuser(String materialsDetial_createuser) {
        this.materialsDetial_createuser = materialsDetial_createuser;
    }

    public String getOrganizeId() {
        return organizeId;
    }

    public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
    }
}
