package com.probim.bimenew.activity.schedule.modules;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.schedule.AllSchedualListActivity;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.SchedualController;
import com.probim.bimenew.net.CallBack;

import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;

public class SchedualPreviewActivty extends BaseActivity implements View.OnClickListener {

    private WebView webView;
    private SchedualPreviewListDTO.DataDTO dto;
    private boolean isItemClick;
    private SchedualListDto.DataDTO planDto;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_schedual_preview);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        initWebView();
    }

    /**
     * 初始化webview
     */
    private void initWebView() {
        WebSettings webSetting = webView.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setJavaScriptCanOpenWindowsAutomatically(true);
        webSetting.setAllowFileAccess(true);
        webSetting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        webSetting.setSupportZoom(true);
        webSetting.setBuiltInZoomControls(true);
        webSetting.setUseWideViewPort(true);
        webSetting.setSupportMultipleWindows(true);
        webSetting.setSupportMultipleWindows(true);
        initJs();
    }

    private void initJs() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                // 一般根据scheme（协议格式） & authority（协议名）判断（前两个参数）
                // 例如：url = "js://webview?arg1=111&arg2=222"
                Uri uri = Uri.parse(url);
                // 如果url的协议 = 预先约定的 js 协议
                Logger.t("url").e(url);
                if (uri.getScheme().equals("xxx")) {

                }
                return super.shouldOverrideUrlLoading(view, url);
            }
        });
        webView.setWebChromeClient(new WebChromeClient() {
                                       @Override
                                       public void onProgressChanged(WebView view, int newProgress) {
                                           mLoading.show();
                                           if (newProgress == 100) {

                                               mLoading.dismiss();
                                           }
                                           super.onProgressChanged(view, newProgress);
                                       }


                                       @Override
                                       public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                                           return super.onConsoleMessage(consoleMessage);
                                       }


                                   }


        );


    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        if (Hawk.contains(CustomParam.SCHEDUAL_ITEM_SELECTED)) {
            isItemClick = true;
        } else {
            isItemClick = false;
        }

        webView = findView(R.id.web_x);
        LinearLayout linBottom = findView(R.id.lin_bottom);
        ImageView iv = findView(R.id.iv_save, this);

        if (isItemClick) {
            // 列表点击
            dto = Hawk.get(CustomParam.SCHEDUAL_ITEM_SELECTED);

            switch (dto.getAuditStatus()) {
                case 0:
                case 3:
                    iv.setVisibility(View.VISIBLE);
                    iv.setImageResource(R.mipmap.fabu);
                    linBottom.setVisibility(View.GONE);
                    break;
                case 2:

                    linBottom.setVisibility(View.GONE);
                    break;
                case 1:
                    break;
            }
        } else {
            // 新建点击
            iv.setVisibility(View.VISIBLE);
            iv.setImageResource(R.mipmap.fabu);
            linBottom.setVisibility(View.GONE);

        }


        TextView tvTittle = findView(R.id.tv_black_title);
        tvTittle.setText("预览");
        LinearLayout linBack = findView(R.id.lin_back, this);
        RelativeLayout rlBohui = findView(R.id.rl_bohui, this);
        RelativeLayout rlTongguo = findView(R.id.rl_tongguo, this);

        /** https://newweb.biaddti.com/#/Home/ProjectBoot/ProgressReportingDaily/6a2fd73e-9210-4358-b3de-8f0950b9740d/null/45f6e860-fa0d-4540-88a1-73dfce2a1f61/2022-09-30
         * 预览地址：/#/Home/ProjectBoot/ProgressReportingDaily
         * 后面传递参数organizeId/ProjectID/CreateuserId/Unittime   MobilePA_ProjectID
         */
        String baseurl = Hawk.get(CustomParam.Web_URL);
        String url = "/#/Home/ProjectBoot/ProgressReportingDaily";
        if (isItemClick) {
//            String xx = baseurl + url + "/" + schedualOrganizeId + "/" + dto.getMobilePA_ProjectID() + "/" + dto.getMobilePA_Createuserid() + "/" + dto.getMobilePA_Unittime();
//            Logger.t("地址").e(xx);
//            webView.loadUrl(xx);
        } else {

            planDto = getSchedualPlanDto();
            String xx = baseurl + url + "/" + schedualOrganizeId + "/" + planDto.getUID() + "/" + Hawk.get(CustomParam.Alpha) + "/" + Hawk.get(CustomParam.SCHEDUAL_TIME_SELECTED);
            Logger.t("地址").e(xx);
            webView.loadUrl(xx);
        }


    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.rl_bohui:
                updateState("驳回待提交");
                break;
            case R.id.rl_tongguo:
                updateState("已审核");
                break;
            case R.id.iv_save:
                updateState("待审核");
                break;
            default:
                break;
        }
    }

    /**
     * 预览发布接口
     */
    private void updateState(String state) {
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        if (isItemClick) {
//            parmas.put("Unittime", dto.getMobilePA_Unittime());
//            parmas.put("CreateuserId", dto.getMobilePA_Createuserid());
//            parmas.put("ProjectID", dto.getMobilePA_ProjectID());

        } else {
            parmas.put("Unittime", Hawk.get(CustomParam.SCHEDUAL_TIME_SELECTED));
            parmas.put("CreateuserId", Hawk.get(CustomParam.Alpha));
            parmas.put("ProjectID", planDto.getUID());
        }

        parmas.put("organizeId", schedualOrganizeId);
        parmas.put("State", state);
        parmas.put("Token", getToken());
        schedualController.updateState(parmas, new CallBack<SchedualBaseResult>() {
            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                if (schedualBaseResult.getRet() == 1) {
                    showMsg(schedualBaseResult.getData());
                    Timer mTimer = new Timer();
                    TimerTask mTimerTask = new TimerTask() {//创建一个线程来执行run方法中的代码
                        @Override
                        public void run() {
                            //要执行的代码
                            Intent intent = new Intent(SchedualPreviewActivty.this, AllSchedualListActivity.class);
                            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            startActivity(intent);
                            finish();
                        }
                    };
                    mTimer.schedule(mTimerTask, 500);//延迟1秒执行
                } else {
                    showMsg(schedualBaseResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
}
