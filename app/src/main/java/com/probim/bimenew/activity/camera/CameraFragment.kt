package com.probim.bimenew.activity.camera

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.webkit.MimeTypeMap
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.*
import androidx.camera.video.VideoCapture
import androidx.concurrent.futures.await
import androidx.core.content.ContextCompat
import androidx.core.net.toFile
import androidx.core.util.Consumer
import androidx.lifecycle.lifecycleScope
import com.probim.bimenew.R
import com.probim.bimenew.activity.NewCheckActivity
import com.probim.bimenew.api.CustomParam
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import com.probim.bimenew.databinding.FragmentCameraBinding

class CameraFragment : AppCompatActivity() {

    private var _fragmentCameraBinding: FragmentCameraBinding? = null
    private val fragmentCameraBinding get() = _fragmentCameraBinding!!

    private lateinit var outputDirectory: File
    private lateinit var videoCapture: VideoCapture<Recorder>
    private var activeRecording: ActiveRecording? = null
    private lateinit var recordingState: VideoRecordEvent
    private var audioEnabled = true
    private val mainThreadExecutor by lazy { ContextCompat.getMainExecutor(this) }
    private var isBack = true
    private var imageCapture: ImageCapture? = null
    private lateinit var cameraExecutor: ExecutorService

    enum class UiState {
        IDLE,       // Not recording, all UI controls are active.
        RECORDING,  // Camera is recording, only display Pause/Resume & Stop button.
        FINALIZED,  // Recording just completes, disable all RECORDING UI controls.
    }



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _fragmentCameraBinding = FragmentCameraBinding.inflate(layoutInflater)
        val view = _fragmentCameraBinding!!.root
        setContentView(view)
    }


    override fun onResume() {
        super.onResume()
        initCameraFragment()
    }

    override fun onDestroy() {
        _fragmentCameraBinding = null
        super.onDestroy()
        cameraExecutor.shutdown()
    }

    private fun setGalleryThumbnail(uri: Uri) {

    }

    private suspend fun bindCameraUseCases() {
        val cameraProvider: ProcessCameraProvider =
            ProcessCameraProvider.getInstance(this).await()
        val cameraSelector =
            if (isBack) CameraSelector.DEFAULT_BACK_CAMERA else CameraSelector.DEFAULT_FRONT_CAMERA

        val preview = Preview.Builder()
            .setTargetAspectRatio(DEFAULT_ASPECT_RATIO)
            .build()
            .apply { setSurfaceProvider(fragmentCameraBinding.previewView.surfaceProvider) }

        val recorder = Recorder.Builder()
            .setQualitySelector(QualitySelector.of(QualitySelector.QUALITY_SD))
            .build()

        videoCapture = VideoCapture.withOutput(recorder)

        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .setFlashMode(ImageCapture.FLASH_MODE_AUTO)
            .setTargetAspectRatio(DEFAULT_ASPECT_RATIO)
            .build()

        try {
            cameraProvider.unbindAll()
            cameraProvider.bindToLifecycle(
                this,
                cameraSelector,
                videoCapture,
                imageCapture,
                preview
            )
        } catch (exc: Exception) {
            // we are on main thread, let's reset the controls on the UI.
            Log.e(TAG, "Use case binding failed", exc)
            resetUIandState("bindToLifecycle failed: $exc")
        }
    }

    @SuppressLint("MissingPermission")
    private fun startRecording() {
        val outFile = createFile(outputDirectory, FILENAME, VIDEO_EXTENSION)
        Log.i(TAG, "outFile: $outFile")
        val outputOptions: FileOutputOptions = FileOutputOptions.Builder(outFile).build()
        activeRecording = videoCapture.output.prepareRecording(this, outputOptions)
            .withEventListener(mainThreadExecutor, captureListener)
            .apply { if (audioEnabled) withAudioEnabled() }
            .start()

        Log.i(TAG, "Recording started")
    }

    private val captureListener = Consumer<VideoRecordEvent> { event ->
        if (event !is VideoRecordEvent.Status) recordingState = event

        updateUI(event)

        if (event is VideoRecordEvent.Finalize) showVideo(event)
    }

    private fun createFile(baseFolder: File, format: String, extension: String) =
        File(
            baseFolder,
            SimpleDateFormat(format, Locale.US).format(System.currentTimeMillis()) + extension
        )

    private fun takePicture() {
        imageCapture?.let { imageCapture ->
            val photoFile = createFile(outputDirectory, FILENAME, PHOTO_EXTENSION)
            val metadata = ImageCapture.Metadata().apply {
                isReversedHorizontal = isBack
            }
            val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile)
                .setMetadata(metadata)
                .build()

            imageCapture.takePicture(
                outputOptions, cameraExecutor, object : ImageCapture.OnImageSavedCallback {
                    override fun onError(exc: ImageCaptureException) {
                        Log.e(TAG, "Photo capture failed: ${exc.message}", exc)
                    }

                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                        val savedUri: Uri = output.savedUri ?: Uri.fromFile(photoFile)
                        Log.d(TAG, "Photo capture succeeded: $savedUri")
                        intent = Intent(
                            this@CameraFragment,
                            NewCheckActivity::class.java
                        )

                        intent.putExtra(CustomParam.CameraPhoto,savedUri.path)
                        setResult(Activity.RESULT_OK, intent)
                        finish()

                        // We can only change the foreground Drawable using API level 23+ API
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                            // Update the gallery thumbnail with latest picture taken
                            setGalleryThumbnail(savedUri)
                        }

                        // Implicit broadcasts will be ignored for devices running API level >= 24
                        // so if you only target API level 24+ you can remove this statement
                        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                            sendBroadcast(
                                Intent(android.hardware.Camera.ACTION_NEW_PICTURE, savedUri)
                            )
                        }

                        // If the folder selected is an external media directory, this is
                        // unnecessary but otherwise other apps will not be able to access our
                        // images unless we scan them using [MediaScannerConnection]
                        val mimeType =
                            MimeTypeMap.getSingleton()
                                .getMimeTypeFromExtension(savedUri.toFile().extension)
                        MediaScannerConnection.scanFile(
                            this@CameraFragment,
                            arrayOf(savedUri.toFile().absolutePath),
                            arrayOf(mimeType)
                        ) { _, uri ->
                            Log.d(TAG, "Image capture scanned into media store: $uri")
                        }
                    }
                })


        }
    }
   private fun getOutputDirectory(context: Context): File {
        val appContext = context.applicationContext
        val mediaDir = context.externalMediaDirs.firstOrNull()?.let {
            File(it, appContext.resources.getString(R.string.app_name)).apply { mkdirs() }
        }
        return if (mediaDir != null && mediaDir.exists())
            mediaDir else appContext.filesDir
    }
    private fun initCameraFragment() {
        outputDirectory =getOutputDirectory(this)
        cameraExecutor = Executors.newSingleThreadExecutor()
        initializeUI()
        lifecycleScope.launch {
            bindCameraUseCases()
        }
    }

    private fun switchCamera() {
        isBack = !isBack
        lifecycleScope.launch {
            bindCameraUseCases()
        }
    }

    private fun changeFlashMode() {
        when (imageCapture?.flashMode) {
            ImageCapture.FLASH_MODE_AUTO -> {
                imageCapture?.flashMode = ImageCapture.FLASH_MODE_ON
                fragmentCameraBinding.ivTorch.setImageResource(R.mipmap.icon_flash_always_on)
            }
            ImageCapture.FLASH_MODE_ON -> {
                imageCapture?.flashMode = ImageCapture.FLASH_MODE_OFF
                fragmentCameraBinding.ivTorch.setImageResource(R.mipmap.icon_flash_always_off)
            }
            ImageCapture.FLASH_MODE_OFF -> {
                imageCapture?.flashMode = ImageCapture.FLASH_MODE_AUTO
                fragmentCameraBinding.ivTorch.setImageResource(R.mipmap.icon_flash_auto)
            }
            else -> Unit
        }
    }

    @SuppressLint("ClickableViewAccessibility", "MissingPermission")
    private fun initializeUI() {
       /* lifecycleScope.launch(Dispatchers.IO) {
            outputDirectory.listFiles { file ->
                EXTENSION_WHITELIST.contains(file.extension.uppercase(Locale.ROOT))
            }?.maxOrNull()?.let {
                setGalleryThumbnail(Uri.fromFile(it))
            }
        }*/

        fragmentCameraBinding.btnSwitchCamera.setOnClickListener {
            switchCamera()
        }



        fragmentCameraBinding.btnRecord.setOnLongClickListener(object :
            CircleProgressButtonView.OnLongClickListener {
            override fun onLongClick() {
                if (!this@CameraFragment::recordingState.isInitialized || recordingState is VideoRecordEvent.Finalize) {
                    startRecording()
                }
            }

            override fun onNoMinRecord(currentTime: Int) = Unit

            override fun onRecordFinishedListener() {
                if (activeRecording == null || recordingState is VideoRecordEvent.Finalize) return
                val recording = activeRecording
                if (recording != null) {
                    recording.stop()
                    activeRecording = null
                }
            }

        })

        fragmentCameraBinding.btnRecord.setOnClickListener(CircleProgressButtonView.OnClickListener {
            takePicture()
        })

        fragmentCameraBinding.ivTorch.setOnClickListener {
            changeFlashMode()
        }
    }

    private fun updateUI(event: VideoRecordEvent) {
        val state = if (event is VideoRecordEvent.Status) recordingState.getName()
        else event.getName()
        Log.i(TAG, "event.getName(): ${event.getName()}")
        when (event) {
            is VideoRecordEvent.Status -> {
                // placeholder: we update the UI with new status after this when() block,
                // nothing needs to do here.
            }
            is VideoRecordEvent.Start -> {
                showUI(UiState.RECORDING, event.getName())
            }
            is VideoRecordEvent.Finalize -> {
                showUI(UiState.FINALIZED, event.getName())
            }
            is VideoRecordEvent.Pause -> {
            }
            is VideoRecordEvent.Resume -> {
            }
            else -> {
                Log.e(TAG, "Error(Unknown Event) from Recorder")
                return
            }
        }

        val stats = event.recordingStats
        val size = stats.numBytesRecorded / 1000
        val time = java.util.concurrent.TimeUnit.NANOSECONDS.toSeconds(stats.recordedDurationNanos)
        var text = "${state}: recorded ${size}KB, in ${time}second"
        if (event is VideoRecordEvent.Finalize)
            text = "${text}\nFile saved to: ${event.outputResults.outputUri}"

       // fragmentCameraBinding.captureStatus.text = text
        Log.i(TAG, "recording event: $text")
    }

    private fun showUI(state: UiState, status: String = "idle") {
        Log.i(TAG, "showUI: UiState: $status")
        fragmentCameraBinding.let {
            when (state) {
                UiState.IDLE -> {
                    it.btnSwitchCamera.visibility = View.VISIBLE
                    it.audioSelection.visibility = View.VISIBLE
                }
                UiState.RECORDING -> {
                    it.btnSwitchCamera.visibility = View.INVISIBLE
                    it.audioSelection.visibility = View.INVISIBLE
                }
                UiState.FINALIZED -> {
                }
                else -> {
                    val errorMsg = "Error: showUI($state) is not supported"
                    Log.e(TAG, errorMsg)
                    return
                }
            }
            it.captureStatus.text = status
        }
    }

    private fun resetUIandState(reason: String) {
        showUI(UiState.IDLE, reason)
    }

    private fun showVideo(event: VideoRecordEvent) {
        if (event !is VideoRecordEvent.Finalize) return

        intent = Intent(
            this@CameraFragment,
            NewCheckActivity::class.java
        )

        intent.putExtra(CustomParam.CameraVideo,  event.outputResults.outputUri.path)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    companion object {
        const val DEFAULT_ASPECT_RATIO = AspectRatio.RATIO_16_9
        val TAG: String = CameraFragment::class.java.simpleName
        private const val FILENAME = "yyyy-MM-dd-HH-mm-ss"
        private const val VIDEO_EXTENSION = ".mp4"
        private const val PHOTO_EXTENSION = ".jpg"
    }
}

fun VideoRecordEvent.getName(): String {
    return when (this) {
        is VideoRecordEvent.Status -> "Status"
        is VideoRecordEvent.Start -> "Started"
        is VideoRecordEvent.Finalize -> "Finalized"
        is VideoRecordEvent.Pause -> "Paused"
        is VideoRecordEvent.Resume -> "Resumed"
        else -> "Error(Unknown)"
    }
}