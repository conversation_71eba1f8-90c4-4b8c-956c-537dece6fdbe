package com.probim.bimenew.activity.bro;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.probim.bimenew.R;

public class SecondAct extends AppCompatActivity implements View.OnClickListener {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_second);
        Button button = findViewById(R.id.btn_2);
        button.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_2:

                Intent intent = new Intent();
                //广播标志. 在静态注册中即在在AndroidManifest中需拦截这个action。
                intent.setAction("com.broadcast.user");
                //数据.
                intent.putExtra("test", "test data 123");
                //采用本地广播 安全
                LocalBroadcastManager mLocalBroadcastManager = LocalBroadcastManager.getInstance(this);
                //发送广播
                mLocalBroadcastManager.sendBroadcast(intent);
                finish();
                break;
            default:
                break;
        }
    }


}