package com.probim.bimenew.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CustomController;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;

import java.util.HashMap;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Description : 修改密码界面 Author : <PERSON>  : <EMAIL> Date
 * :2018/9/25/16:48.
 */
public class ModifyPasswordActivity extends BaseActivity {

    @BindView(R.id.lin_back)
    LinearLayout linBack;
    @BindView(R.id.tv_black_title)
    TextView tvTitle;
    @BindView(R.id.tv_right)
    TextView tvRight;
    @BindView(R.id.edt_input_oldPwd)
    EditText edtInputOldPwd;
    @BindView(R.id.edt_input_newPwd)
    EditText edtInputNewPwd;
    @BindView(R.id.edt_input_newPwd_agian)
    EditText edtInputNewPwdAgian;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_modify_pwd);
        ButterKnife.bind(this);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        tvTitle.setText("修改密码");
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("确认修改");
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @OnClick({R.id.lin_back, R.id.tv_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.tv_right:
        /*if (TextUtils.isEmpty(edtInputOldPwd.getText().toString().trim())) {
          showMsg("原密码不能为空");
          return;
        }*/
                if (TextUtils.isEmpty(edtInputNewPwd.getText().toString().trim())) {
                    showMsg("新密码不能为空");
                    return;
                }
                if (TextUtils.isEmpty(edtInputNewPwdAgian.getText().toString().trim())) {
                    showMsg("确认密码不能为空");
                    return;
                }
                if (!edtInputNewPwd.getText().toString().trim()
                        .equals(edtInputNewPwdAgian.getText().toString().trim())) {
                    showMsg("两次输入密码不一致");
                    return;
                }
                modifyPwd(edtInputNewPwd.getText().toString().trim());
                break;
        }
    }

    /**
     * 修改密码
     */
    private void modifyPwd(String newPwd) {
        mLoading.show();
        CustomController customController = new CustomController();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("Pwd", newPwd);
        params.put("PwdRepeat", newPwd);
        params.put("Type", "4");
        customController.ModifyPassWord(params, new CallBack<BaseResult>() {

            @Override
            public void onSuccess(BaseResult baseResult) {
                mLoading.dismiss();
                if (baseResult.getRet() == 1) {

                    showMsg("修改密码成功");
                    startActivity(LoginActivity.class);
                    finish();
                } else {

                    showMsg(baseResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
}
