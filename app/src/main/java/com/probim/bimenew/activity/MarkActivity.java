package com.probim.bimenew.activity;

import android.content.Intent;
import android.graphics.RectF;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.util.Log;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

import com.github.chrisbanes.photoview.OnMatrixChangedListener;
import com.github.chrisbanes.photoview.OnPhotoTapListener;
import com.github.chrisbanes.photoview.PhotoView;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.dto.PostionBean;
import com.probim.bimenew.model.DrawingsModel;
import com.probim.bimenew.net.CallBack;

import java.util.HashMap;

/**
 * Description :SelectDrawingPointActivity
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/3/21/16:23.
 */

public class MarkActivity extends BaseActivity implements View.OnTouchListener {

  /**
   * 这个是用来计算手指移动距离的
   */
  float oldX = 0;
  float oldY = 0;
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.tv_right) TextView tvRight;
  @BindView(R.id.img_right) ImageView imgRight;
  @BindView(R.id.pv) PhotoView photoView;
  @BindView(R.id.marker) ImageView marker;
  public static int imageWidth;
  public static int imageHeight;
  public static String img2d_ModelId = "";
  public static String img2dId = "";
  public static float img2d_pos_x;
  public static float img2d_pos_y;
  public static String imgName = "";
  public static String img_url = "";
  public static boolean isCan = false;
  public static String pixelX = "";
  public static String pixelY = "";
  public static String X = "";
  public static String Y = "";
  public static String Z = "";

  private String modelId;
  private String twoDimensId;
  private float pX;
  private float pY;
  public static String img2d_pos = "";

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_mark);
    ButterKnife.bind(this);
    initView();
    loadData();
  }

  @Override protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      //if ()
      twoDimensId = bundle.getString(CustomParam.TwoDimensId);
      String name = bundle.getString(CustomParam.TwoDimensName);
      imgName = name;
      String projectId = bundle.getString(CustomParam.ProjectId);
      modelId = bundle.getString(CustomParam.ModelId);

      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ModelID", modelId);
      params.put("VersionNO", "");
      params.put("FileType", "PlanView");
      params.put("FileName", twoDimensId);

      ModelController controller = new ModelController();

      controller.GetTwoDimensionalDetails(params, new CallBack<DrawingsModel>() {
        @Override
        public void onSuccess(DrawingsModel drawingsModel) {
          pixelX = drawingsModel.getPixel2mx() + "";
          pixelY = drawingsModel.getPixel2my() + "";
          X = drawingsModel.getLeftptm().getX() + "";
          Y = drawingsModel.getLeftptm().getY() + "";
          Z = drawingsModel.getLeftptm().getZ() + "";

         /* Glide.with(getApplicationContext())
              .load(Base64Utils.decode(drawingsModel.getImagebase64())).asBitmap().into(
              new SimpleTarget<Bitmap>() {
                @Override public void onResourceReady(Bitmap resource,
                    GlideAnimation<? super Bitmap> glideAnimation) {
                  //photoView.setImageBitmap(resource);
                  //Drawable d = photoView.getDrawable();
                  //photoView.setImageDrawable();
                  Drawable drawable = new BitmapDrawable(resource);
                  photoView.setImageDrawable(drawable);
                }
              });*/

          PostionBean bean = new PostionBean();
          if (NewIssueActivity.isFromWhere) {
            //预览点位置
            String position = bundle.getString(CustomParam.DrawingPosition);
            String[] strPosition = position.split("_");
            pX = Float.valueOf(strPosition[0]);
            pY = Float.valueOf(strPosition[1]);
            //预览功能
            bean.setPointX(pX);
            bean.setPointY(pY);
            marker.setTag(bean);
          } else {
            //选点

            //bean.setPointX(0 - photoView.getDisplayRect().left);
            //bean.setPointY(0 - photoView.getDisplayRect().top);
            marker.setTag(bean);
            marker.setOnTouchListener(MarkActivity.this);
          }
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }
  }

  @Override protected void initView() {

    if (NewIssueActivity.isFromWhere) {
      //预览
      tvTitle.setText("图纸预览");
    } else {
      //选点
      tvTitle.setText("图纸选择");
      tvRight.setVisibility(View.VISIBLE);
      tvRight.setText("完成");
    }

    initPhotoView();
  }

  private void initPhotoView() {
    //滑动临界值
    int limit = ViewConfiguration.get(this).getScaledTouchSlop();
    /**
     * 设置复位监听
     */
    photoView.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        /**
         * 恢复放大倍数（好像photoview不能缩小）
         */
        photoView.setScale(1);
      }
    });
    /**
     * photoview图片移动监听（这个返回的位置是背景图相对于左上角与屏幕的相对距离）
     */
    photoView.setOnMatrixChangeListener(new OnMatrixChangedListener() {
      @Override public void onMatrixChanged(RectF rect) {

        PostionBean bean = (PostionBean) marker.getTag();
        //原始的相对位置乘当前的放大倍数再加上偏移量即当前的相对位置

        if (bean != null) {

          marker.setX(bean.getPointX() * photoView.getScale() + rect.left);
          marker.setY(bean.getPointY() * photoView.getScale() + rect.top);
        }
      }
    });

    /**
     * 点击获取坐标
     */
    photoView.setOnPhotoTapListener(new OnPhotoTapListener() {
      @Override public void onPhotoTap(ImageView view, float x, float y) {
        float X = view.getWidth() * x;
        float Y = view.getHeight() * y;
      }
    });
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

  }

  @Override protected void initRefresh() {

  }

  @Override public boolean onTouch(View v, MotionEvent event) {

    switch (event.getAction()) {
      //按下
      case MotionEvent.ACTION_DOWN:
        oldX = event.getRawX();
        oldY = event.getRawY();
        break;
      //移动
      case MotionEvent.ACTION_MOVE:
        //让控件跟着自己的手指动
        v.setX(v.getX() + event.getRawX() - oldX);
        v.setY(v.getY() + event.getRawY() - oldY);
        oldX = event.getRawX();
        oldY = event.getRawY();
        break;
      //抬手
      case MotionEvent.ACTION_UP:
        PostionBean bean = (PostionBean) v.getTag();
        RectF rectF = photoView.getDisplayRect();
        //计算相对于photoview左边定点的相对位置
        bean.setPointX((v.getX() - rectF.left) / photoView.getScale());
        bean.setPointY((v.getY() - rectF.top) / photoView.getScale());
        img2d_pos_x = bean.getPointX();
        img2d_pos_y = bean.getPointY();
        Log.e("点击位置--", bean.getPointX() + " -------  " + bean.getPointY());
        break;
      default:
    }
    return true;
  }

  @Override public boolean onTouchEvent(MotionEvent event) {
    VelocityTracker velocityTracker = VelocityTracker.obtain();
    velocityTracker.addMovement(event);
    return super.onTouchEvent(event);
  }

  @OnClick({ R.id.iv_back, R.id.tv_right, R.id.lin_back }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        break;
      case R.id.tv_right:

        img2d_ModelId = modelId;
        img2d_pos =
            img2d_pos_x + "_" + img2d_pos_y;
        img2dId = twoDimensId;
        isCan = true;
        finish();
        break;
      case R.id.lin_back:
        break;
    }
  }
}
