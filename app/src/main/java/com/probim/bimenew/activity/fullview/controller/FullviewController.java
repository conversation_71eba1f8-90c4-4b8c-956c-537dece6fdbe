package com.probim.bimenew.activity.fullview.controller;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.activity.fullview.dto.AllFullViewItemDTO;
import com.probim.bimenew.activity.fullview.dto.AllFullviewDTO2;
import com.probim.bimenew.activity.fullview.dto.FullviewLableDTO;
import com.probim.bimenew.activity.fullview.dto.FullviewResult;
import com.probim.bimenew.activity.fullview.dto.LabelFileDto;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.yyx.beautifylib.utils.ToastUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;

import okhttp3.Call;
import okhttp3.Response;

public class FullviewController {
    private final Context mContext = BaseApp.getContext();

    private final String BASE_URL = Hawk.get(CustomParam.Base_URL);

    private final String BIME_URL = Hawk.get(CustomParam.Bim_URL);

    private final String WEB_URL = Hawk.get(CustomParam.Web_URL);

    private final String FULLVIEW_URL = Hawk.get(CustomParam.Panorama_URL);

    /**
     * 获取标签
     */
    public void getLabelList(HashMap<String, String> params,
                             final CallBack<FullviewLableDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(FULLVIEW_URL + ApiConstant.GET_FULLVIEW_LABEL,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取全景图标签数据---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, FullviewLableDTO.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除全景图
     */
    public void deleteFullview(HashMap<String, String> params,
                               final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postFormData(FULLVIEW_URL + ApiConstant.DELETE_FULLVIEW,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("删除全景图---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 删除全景图
     */
    public void deleteItem(HashMap<String, String> params,
                               final CallBack<SchedualBaseResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postWithUrlParams(FULLVIEW_URL + ApiConstant.DELETE_FULLVIEW_ITEM,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("删除全景图子集---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, SchedualBaseResult.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取全景图总列表
     */
    public void getFullviewList(HashMap<String, String> params,
                                final CallBack<AllFullviewDTO2> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(FULLVIEW_URL + ApiConstant.GET_FULLVIEW,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取所有全景图---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, AllFullviewDTO2.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取全景图子列表
     */
    public void getFullviewItemList(HashMap<String, String> params,
                                    final CallBack<AllFullViewItemDTO> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(FULLVIEW_URL + ApiConstant.GET_FULLVIEW_ITEM,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取全景图子列表---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, AllFullViewItemDTO.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 新建全景图
     */
    public void addFullview(File file, HashMap<String, String> params, HashMap<String, String> urlParams,String photoName,
                            final CallBack<FullviewResult> callBack) {

        OkHttpHelper.getInstance(mContext)
                .uploadFullviewPhoto(FULLVIEW_URL + ApiConstant.ADD_FULLVIEW, file,
                        params, urlParams,photoName, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("新建全景图---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, FullviewResult.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * 获取标签
     */
    public void getFileList(HashMap<String, String> params,
                            final CallBack<LabelFileDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .getAsync(FULLVIEW_URL + ApiConstant.GET_FULLVIEW_FILE,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取全景图文件数据---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, LabelFileDto.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

}
