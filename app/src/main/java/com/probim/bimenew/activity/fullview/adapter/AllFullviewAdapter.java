package com.probim.bimenew.activity.fullview.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.fullview.dto.AllFullviewDTO2;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.ArrayList;
import java.util.List;

public class AllFullviewAdapter extends RecyclerView.Adapter<AllFullviewAdapter.ViewHolder> {

    private final String FULLVIEW_URL = Hawk.get(CustomParam.Panorama_URL);
    private List<AllFullviewDTO2.DataDTO.PanoramasDTO> mDatas = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;

    public AllFullviewAdapter(List<AllFullviewDTO2.DataDTO.PanoramasDTO> mDatas) {
        this.mDatas = mDatas;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_fullview, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AllFullviewDTO2.DataDTO.PanoramasDTO dto = mDatas.get(position);
        if (TextUtils.isEmpty(dto.getPbUrl())) {
            Glide.with(holder.itemView.getContext()).load(R.mipmap.xxxx_xxxx).into(holder.iv);
        } else {
            Glide.with(holder.itemView.getContext()).load(FULLVIEW_URL + "/Panorama" + dto.getPbUrl() + "/cover.png").into(holder.iv);
        }

        String statusStr = null;
        switch (dto.getPqFlag()) {
            case 0:
                statusStr = "(待转换)";
                break;
            case 1:
                statusStr = "(转换中)";
                break;
            case 2:
                statusStr = "";
                break;
            case 3:
                statusStr = "(转换失败)";
                break;
            default:
                statusStr = "";
                break;
        }
        holder.tvName.setText(dto.getPbName() + statusStr);
        holder.tvTime.setText(dto.getPbUpdatetime().split("T")[0]);
        holder.tvType.setText(dto.getLabelName());
        holder.linContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onClick(position, dto);
            }
        });
        holder.rlDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ((SwipeMenuLayout) holder.itemView).quickClose();
                onItemClickListener.OnDelete(holder.getAdapterPosition(), dto);
            }
        });
        holder.rlEdit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ((SwipeMenuLayout) holder.itemView).quickClose();
                onItemClickListener.OnClose(holder.getAdapterPosition(), dto);
            }
        });

    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvName;
        private final TextView tvType;
        private final TextView tvTime;
        private final AppCompatImageView iv;
        private final LinearLayout linContainer;
        private final RelativeLayout rlDelete;
        private final RelativeLayout rlEdit;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            iv = itemView.findViewById(R.id.iv_fullview);
            tvName = itemView.findViewById(R.id.tv_name);
            tvType = itemView.findViewById(R.id.tv_type);
            tvTime = itemView.findViewById(R.id.tv_time);
            linContainer = itemView.findViewById(R.id.lin_container);
            rlDelete = itemView.findViewById(R.id.rl_delete);
            rlEdit = itemView.findViewById(R.id.rl_edit);

        }
    }
}
