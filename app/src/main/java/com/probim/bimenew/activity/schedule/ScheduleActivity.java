package com.probim.bimenew.activity.schedule;

import android.os.Bundle;

import androidx.annotation.Nullable;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;

/**
 *  任务进度界面
 */
public class ScheduleActivity extends BaseActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_schedual);
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {

    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }
}
