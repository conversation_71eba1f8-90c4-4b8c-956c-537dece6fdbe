package com.probim.bimenew.activity.bro

import android.os.Bundle
import android.widget.Toolbar
import androidx.appcompat.app.AppCompatActivity
import com.probim.bimenew.R


class Kotlin : AppCompatActivity() {

    private lateinit var c: Toolbar


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        c = findViewById(R.id.toolbar)
        val toInt = toInt(244.33f)



    }

}

fun main() {

    println("hello world")
}


fun doubles(s: Int) {
    println(s)

}