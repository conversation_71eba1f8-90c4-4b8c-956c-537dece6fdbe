package com.probim.bimenew.activity.check;

import android.os.Build;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import com.dueeeke.videocontroller.StandardVideoController;
import com.dueeeke.videocontroller.component.ErrorView;
import com.dueeeke.videocontroller.component.GestureView;
import com.dueeeke.videocontroller.component.PrepareView;
import com.dueeeke.videocontroller.component.TitleView;
import com.dueeeke.videocontroller.component.VodControlView;
import com.dueeeke.videoplayer.BuildConfig;
import com.dueeeke.videoplayer.exo.ExoMediaPlayerFactory;
import com.dueeeke.videoplayer.player.VideoView;
import com.dueeeke.videoplayer.player.VideoViewConfig;
import com.dueeeke.videoplayer.player.VideoViewManager;
import com.dueeeke.videoplayer.util.PlayerUtils;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;

/**
 * 全屏播放
 * Created by dueeeke on 2017/4/21.
 */

public class FullScreenActivity extends AppCompatActivity {

  public static final String SAMPLE_URL =
      "http://vfx.mtime.cn/Video/2019/03/14/mp4/190314223540373995.mp4";
  private VideoView mVideoView;
  private StandardVideoController mController;
  private String urlStr;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    adaptCutoutAboveAndroidP();
    initVideo();
    startVideo();
  }

  private void initVideo() {

    urlStr = getIntent().getStringExtra(CustomParam.VIDEOURL);
    //播放器配置，注意：此为全局配置，按需开启
    VideoViewManager.setConfig(VideoViewConfig.newBuilder()
        .setLogEnabled(BuildConfig.DEBUG)//调试的时候请打开日志，方便排错
        .setPlayerFactory(ExoMediaPlayerFactory.create())
        //                .setPlayerFactory(ExoMediaPlayerFactory.create())
        //                .setRenderViewFactory(SurfaceRenderViewFactory.create())
        //                .setEnableOrientation(true)
        //                .setEnableAudioFocus(false)
        //                .setScreenScaleType(VideoView.SCREEN_SCALE_MATCH_PARENT)
        //                .setAdaptCutout(false)
        //                .setPlayOnMobileNetwork(true)
        //                .setProgressManager(new ProgressManagerImpl())
        .build());
  }

  private void startVideo() {
    mVideoView = new VideoView(this);
    mVideoView.startFullScreen();
    mVideoView.setUrl(urlStr);
    mController = new StandardVideoController(this);
    mController.addControlComponent(new CompleteView(this));
    mController.addControlComponent(new ErrorView(this));
    mController.addControlComponent(new PrepareView(this));
    mVideoView.setVideoController(mController);
    //mVideoView.setScreenScaleType(VideoView.SCREEN_SCALE_16_9);
    mController.addControlComponent(new GestureView(this));

    TitleView titleView = new TitleView(this);
    // 我这里改变了返回按钮的逻辑，我不推荐这样做，我这样只是为了方便，
    // 如果你想对某个组件进行定制，直接将该组件的代码复制一份，改成你想要的样子
    titleView.findViewById(R.id.back).setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        finish();
      }
    });
    titleView.setTitle("现场数据视频");
    mController.addControlComponent(titleView);
    VodControlView vodControlView = new VodControlView(this);
    // 我这里隐藏了全屏按钮并且调整了边距，我不推荐这样做，我这样只是为了方便，
    // 如果你想对某个组件进行定制，直接将该组件的代码复制一份，改成你想要的样子
    vodControlView.findViewById(R.id.fullscreen).setVisibility(View.GONE);
    LinearLayout.LayoutParams lp =
        (LinearLayout.LayoutParams) vodControlView.findViewById(R.id.total_time).getLayoutParams();
    lp.rightMargin = PlayerUtils.dp2px(this, 16);
    mController.addControlComponent(vodControlView);
    mVideoView.start();
  }

  private void adaptCutoutAboveAndroidP() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
      WindowManager.LayoutParams lp = getWindow().getAttributes();
      lp.layoutInDisplayCutoutMode =
          WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
      getWindow().setAttributes(lp);
    }
  }

  @Override
  public void onBackPressed() {
    if (mVideoView != null) {
      mVideoView.release();
      mVideoView = null;
      finish();
    }
    super.onBackPressed();
  }
}
