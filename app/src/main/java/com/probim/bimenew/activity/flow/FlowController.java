package com.probim.bimenew.activity.flow;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.activity.fullview.dto.AllFullViewItemDTO;
import com.probim.bimenew.activity.fullview.dto.AllFullviewDTO2;
import com.probim.bimenew.activity.fullview.dto.FullviewLableDTO;
import com.probim.bimenew.activity.fullview.dto.FullviewResult;
import com.probim.bimenew.activity.fullview.dto.LabelFileDto;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.dto.AllFlowDto;
import com.probim.bimenew.dto.BIMeBaseDto;
import com.probim.bimenew.dto.BaseDto;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.net.IHttpRequestHandler1;
import com.probim.bimenew.net.OkHttpHelper;
import com.yyx.beautifylib.utils.ToastUtils;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;

import okhttp3.Call;
import okhttp3.Response;

public class FlowController {
    private final Context mContext = BaseApp.getContext();


    //private final String FlowUrl = Hawk.get(CustomParam.Flow_URL);

    private final String FlowUrl = Hawk.get(CustomParam.Flow_URL);

    private final String BASE_URL = Hawk.get(CustomParam.Base_URL);

    /**
     * 获取流程数据
     */
    public void getFlowList(String type, HashMap<String, String> params,
                            final CallBack<List<AllFlowDto>> callBack) {
        String url = "/WF/Comm/Handler.ashx?DoType=HttpHandler&DoMethod=" + type + "&HttpHandlerName=BP.WF.HttpHandler.WF";

        OkHttpHelper.getInstance(mContext)
                .postFormData(FlowUrl + url,
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("获取流程数据---------->>>").e(str);
                                    Type collectionType = new TypeToken<List<AllFlowDto>>() {
                                    }.getType();
                                    List<AllFlowDto> lcs = (List<AllFlowDto>) new Gson()
                                            .fromJson(str, collectionType);
                                    callBack.onSuccess(lcs);
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

    /**
     * flow登录
     */
    public void getCCFlow(HashMap<String, String> params,
                            final CallBack<BIMeBaseDto> callBack) {

        OkHttpHelper.getInstance(mContext)
                .postJsonAsync(BASE_URL + "/api/User/User/CCFlowLogin",
                        params, new IHttpRequestHandler1() {
                            @Override
                            public void onResponse(Call call, Response response, String str)
                                    throws IOException {
                                if (!TextUtils.isEmpty(str)) {
                                    Logger.t("flow登录---------->>>").e(str);
                                    callBack.onSuccess(new Gson().fromJson(str, BIMeBaseDto.class));
                                } else {
                                    ToastUtils.toast(mContext, "暂无数据");
                                }

                            }

                            @Override
                            public void onFailure(Call call, IOException e) {

                            }

                            @Override
                            public void onResponse(Call call, Response response) throws IOException {

                            }
                        });
    }

}
