package com.probim.bimenew.activity.schedule.adapter;

import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualNewTaskDTO;
import com.probim.bimenew.interfaces.IOnItemSchedualTaskPlanClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualNewTaskListAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private final int TYPE_PLAN = 0;
    private final int TYPE_NO_PLAN = 1;
    private IOnItemSchedualTaskPlanClickListener onItemClickListener;
    private List<SchedualNewTaskDTO> dtoList = new ArrayList<>();
    private Handler handler = new Handler();

    public SchedualNewTaskListAdapter(List<SchedualNewTaskDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_PLAN) {
            return new ViewHoolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_task_plan, parent, false));
        } else {
            return new NoPlanViewHoolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_task_no_plan, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        SchedualNewTaskDTO dto = dtoList.get(position);
        int temp = position + 1;
        if (holder instanceof ViewHoolder) {
            ViewHoolder vHolder = (ViewHoolder) holder;
            vHolder.tvName.setText("任务" + temp);
            vHolder.tvTaskName.setText(dto.getProgress_Name());
            vHolder.edtActualratio.setText(dto.getProgress_actualratio());
            vHolder.edtAddratio.setText(dto.getProgress_MobileAddratio());
            vHolder.edtPlantratio.setText(dto.getProgress_planratio());
            vHolder.edtTomorrowratio.setText(dto.getProgress_MobileTomorrowratio());
            vHolder.edtWhy.setText(dto.getProgress_MobileReason());
            vHolder.tvStatus.setText(dto.getProgress_state());
            vHolder.tvStartDate.setText(dto.getProgress_planstarttime());
            vHolder.tvEndDate.setText(dto.getProgress_plannendtime());
            vHolder.rlSelectTask.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onItemClickListener.onTaskClick(position, dto);

                }
            });
            vHolder.edtWhy.clearFocus();
            vHolder.edtWhy.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        onItemClickListener.showEdtWhyChange(position, editable.toString());
                    }
                }
            });
            vHolder.edtTomorrowratio.clearFocus();
            vHolder.edtTomorrowratio.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        onItemClickListener.showEdtTomorrowratioChange(position, editable.toString());
                    }
                }
            });
            vHolder.edtPlantratio.clearFocus();
            vHolder.edtPlantratio.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        onItemClickListener.showEdtPlantratioChange(position, editable.toString());
                    }
                }
            });

            vHolder.edtAddratio.clearFocus();
            vHolder.edtAddratio.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        onItemClickListener.showEdtAddratioChange(position, editable.toString());
                    }
                }
            });


            vHolder.edtActualratio.clearFocus();
            vHolder.edtActualratio.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    if (onItemClickListener != null) {
                        if (!TextUtils.isEmpty(editable.toString())) {
                            Runnable runnable = new Runnable() {
                                @Override
                                public void run() {
                                    //结束后进行操作
                                    //holder.edtChange.setFocusable(false);
                                    onItemClickListener.showEdtActualratioChange(position, editable.toString());
                                }
                            };
                            handler.postDelayed(runnable, 2000);

                        }

                    }


                }
            });



        } else if (holder instanceof NoPlanViewHoolder) {
            NoPlanViewHoolder noPlanViewHoolder = (NoPlanViewHoolder) holder;
            noPlanViewHoolder.tvNoPlanName.setText("序号" + temp);
            noPlanViewHoolder.edtNoPlan.setText(dto.getNoProgress_Name());
            noPlanViewHoolder.edtNoPlan.clearFocus();
            noPlanViewHoolder.edtNoPlan.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void afterTextChanged(Editable editable) {
                    if (!TextUtils.isEmpty(editable.toString())) {
//                        onItemClickListener.showEdtNoPlanChange(position, editable.toString());
                    }
                }
            });
        }

    }

    @Override
    public int getItemViewType(int position) {
        if (dtoList.get(position).getTaskType() == 0) {
            return TYPE_PLAN;
        } else {
            return TYPE_NO_PLAN;
        }
    }


    @Override
    public int getItemCount() {

        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualTaskPlanClickListener listener) {
        this.onItemClickListener = listener;
    }

    public class ViewHoolder extends RecyclerView.ViewHolder {
        @BindView(R.id.rl_select_task)
        RelativeLayout rlSelectTask;
        @BindView(R.id.tv_task_name)
        TextView tvTaskName;
        @BindView(R.id.tv_task_start_date)
        TextView tvStartDate;
        @BindView(R.id.tv_task_end_date)
        TextView tvEndDate;
        @BindView(R.id.tv_task_status)
        TextView tvStatus;
        @BindView(R.id.edt_why)
        EditText edtWhy;
        @BindView(R.id.tv_name)
        TextView tvName;
        @BindView(R.id.edt_planratio)
        EditText edtPlantratio;
        @BindView(R.id.edt_actualratio)
        EditText edtActualratio;
        @BindView(R.id.edt_addratio)
        EditText edtAddratio;
        @BindView(R.id.edt_tomorrowratio)
        EditText edtTomorrowratio;


        public ViewHoolder(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

    public class NoPlanViewHoolder extends RecyclerView.ViewHolder {

        @BindView(R.id.edt_no_plan)
        EditText edtNoPlan;
        @BindView(R.id.tv_no_plan_name)
        TextView tvNoPlanName;


        public NoPlanViewHoolder(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }

}
