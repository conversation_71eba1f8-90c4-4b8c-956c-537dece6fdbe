package com.probim.bimenew.activity.check;

import static com.bumptech.glide.load.resource.bitmap.VideoBitmapDecoder.FRAME_OPTION;

import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.media.MediaMetadataRetriever;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.request.RequestOptions;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.CheckRecordListActivity;
import com.probim.bimenew.activity.PhotoViewActivity;
import com.probim.bimenew.activity.camera.Video4DetailsActivity;
import com.probim.bimenew.adapter.CheckPhotoAdapter;
import com.probim.bimenew.adapter.ProblemHeadAdapter;
import com.probim.bimenew.adapter.ProblemJoinAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.controller.UserController;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.CheckPeopleDto;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.ProjectUser;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllPeoplerResult;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.result.CheckDetailsDto;
import com.probim.bimenew.result.CheckResult;
import com.probim.bimenew.result.CheckTypeResult;
import com.probim.bimenew.result.DeleteCheckPhotoResult;
import com.probim.bimenew.result.IssueJoinerResult;
import com.probim.bimenew.result.MateriaOriginListBean;
import com.probim.bimenew.result.UpdateCheckPhotoResult;
import com.probim.bimenew.result.UsersDto;
import com.probim.bimenew.utils.CloseKeybordUtils;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.UIUtils;
import com.probim.bimenew.utils.WheelView;
import com.probim.bimenew.utils.flowlayout.FlowLayout;
import com.probim.bimenew.utils.flowlayout.TagAdapter;
import com.probim.bimenew.utils.flowlayout.TagFlowLayout;
import com.probim.bimenew.utils.popwindow.CheckTypePopWindow;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import java.io.File;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * Description :检查详情
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/12/17/14:45.
 */

public class CheckDetailsActivity extends BaseActivity implements
        ViewPager.OnPageChangeListener, View.OnClickListener {
    private final int code_select_people = 2;
    private final int code_photo = 3000;
    private final int code_select_materials = 1;
    private final int code_select_task = 3;
    private final int code_ps = 7000;
    private final List<String> bannerList = new ArrayList<>();
    private final List<String> newPhotoList = new ArrayList<>();
    private final List<String> bannerPhotoIdList = new ArrayList<>();
    private final List<File> photoFileList = new ArrayList<>();
    private final List<String> upLoadPhotoList = new ArrayList<>();
    private final List<String> wheelLevelList = new ArrayList<>();
    private final List<CheckDetailsDto.DataBean.ListBean.TasksBean> taskDtoList = new ArrayList<>();
    private final List<IssueJoinerResult.DataBean> projectRoleList = new ArrayList<>();
    private final List<ProjectUser.UsersBean> projectMemberList = new ArrayList<>();

    List<String> headList = new ArrayList<>();
    List<String> joinList = new ArrayList<>();
    List<String> joinIDList = new ArrayList<>();
    List<String> headIdList = new ArrayList<>();
    List<PhotoDto> photoDtoList = new ArrayList<>();
    List<PhotoDto> photoNewDtoList = new ArrayList<>();
    List<CheckDetailsDto.DataBean.ListBean.MaterialsBean> materialsDtoList = new ArrayList<>();
    private ProblemHeadAdapter problemHeadAdapter;
    private ProblemJoinAdapter problemJoinAdapter;
    private CheckController checkController;
    private String organizeId;
    private int selectedPosition;
    private String examineId;
    private CheckDetailsDto checkDetailData;
    private GridView joinOrHead;
    private TimePickerView pvTime;
    private String dateStr;
    private PopupWindow levelPop;
    private WheelView levelWheelView;
    private String wheelLevelSelecde;
    private int checkType = -1;
    private TasksAdapter tasksAdapter;
    private CheckPhotoAdapter checkPhotoAdapter;
    private MaterialsAdapter materialsAdapter;
    private TextView tvWheelConfirm;
    private TextView tvWheelLevel;
    private EditText tvTittle;
    private TextView tvEndTime;
    private TextView tvStartTime;
    private TextView tvType;
    private TextView tvStatus;
    private TextView btnConfirm;
    private TextView tvCheckPeople;
    private TextView tvTaskSize;
    private TextView tvMaterilaSize;
    private TextView tvCheckStr;
    private ImageView ivPhotoLeft;
    private ImageView ivPhotoMiddle;
    private ImageView ivPhotoRight;
    private ImageView ivPhotoAplh;
    private TextView tvPhotoSize;
    private TextView tvSeverityLevel;
    private TextView tvZgrSize;
    private TextView tvYSrSize;
    private TagFlowLayout flowZgr;
    private TagFlowLayout flowYsr;
    private TextView tvYsrSize;
    private LinearLayout linCheckIntro;
    private LinearLayout linCheckPeople;
    private LinearLayout linCheckLevel;
    private RelativeLayout rlBottomHasCheck;
    private TextView tvCheckPhase;
    private LinearLayout linHasChcek;
    private FrameLayout fraCheckVideo;
    private LinearLayout linCheckMoreImg;
    private String videoUrl;
    private ImageView ivCheckVideo;
    private String createrId;
    private String timeStr;
    private String startTimeStr;
    private String endTimeStr;
    private String modify_flag;
    private LinearLayout linCheckType;
    private View viewPop;
    private String typeIDSelected;
    private LinearLayout linSelectPeople;
    private String remark;
    private RecyclerView rvPhoto;
    private NewPhoto2CheckAdapter newPhotoAdapter;
    private LinearLayout rl_check_photo;
    private FrameLayout fraVideo;
    private ImageView ivVdieo;
    private RelativeLayout rl_photo;
    private String linkType;
    private RecyclerView rvMaterials;
    private RecyclerView rvTasks;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_check_details);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
        //现场详情
        getCheckDetail(examineId);
    }

    /*
     * 获取项目所有人
     */
    private void initPeople() {
        UserController userController = new UserController();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("organizeId", organizeId);
//        params.put("roleId", "4f387b02-b889-4bb7-8129-03e3099f4d5b");
        userController.getUser(params, new CallBack<UsersDto>() {
            @Override
            public void onSuccess(UsersDto usersDto) {

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void initView() {


        checkController = new CheckController();
        //初始化时间选择器
        if (getIntent() != null) {
            linkType = getIntent().getStringExtra("LinkType");
            examineId = getIntent().getStringExtra(CustomParam.Examineid);
            createrId = getIntent().getStringExtra(CustomParam.ExaminerId);
            //tvTitle.setText("检查任务");
            //数据库数据
            GetProjectDao();
            // initPeople();
        }
        LinearLayout linSelectTask = findView(R.id.lin_select_task, this);
        LinearLayout linSelectMaterail = findView(R.id.lin_select_material, this);
        fraCheckVideo = findView(R.id.fra_check_details_video);
        linCheckMoreImg = findView(R.id.lin_check_more_img);
        ImageView ivCheckVideoPlay = findView(R.id.iv_check_video_play, this);
        ivCheckVideo = findView(R.id.iv_check_video);
        TextView tvCheckRecord = findView(R.id.tv_check_record, this);
        linHasChcek = findView(R.id.lin_has_check, this);
        linCheckIntro = findView(R.id.lin_check_intro);
        linCheckPeople = findView(R.id.lin_check_people);
        linCheckLevel = findView(R.id.lin_check_level);
        rlBottomHasCheck = findView(R.id.rl_has_check);
        tvCheckPhase = findView(R.id.tv_check_phase);
        rvMaterials = findView(R.id.rv_materials);
        rvTasks = findView(R.id.rv_task);
        //RecyclerView rvCheckPeople = findView(R.id.rv_people);
        tvStartTime = findView(R.id.tv_select_start_time);
        tvEndTime = findView(R.id.tv_select_end_time);
        tvTittle = findView(R.id.tv_write_title);
        tvType = findView(R.id.tv_check_type);
        tvStatus = findView(R.id.tv_check_staus);
        btnConfirm = findView(R.id.btn_check_details_confirm, this);
        tvCheckPeople = findView(R.id.tv_select_people_attention);
        tvTaskSize = findView(R.id.tv_select_task_attention);
        tvMaterilaSize = findView(R.id.tv_select_matrial_attention);
        tvCheckStr = findView(R.id.tv_check_content_1);
        ivPhotoLeft = findView(R.id.img_check_left, this);
        ivPhotoMiddle = findView(R.id.img_check_middle, this);
        ivPhotoRight = findView(R.id.img_check_right, this);
        ivPhotoAplh = findView(R.id.aplh_view, this);
        tvPhotoSize = findView(R.id.tv_size);
        tvSeverityLevel = findView(R.id.tv_severity_level);
        tvZgrSize = findView(R.id.tv_select_zgr_attention);
        tvYSrSize = findView(R.id.tv_select_ysr_attention);
        flowZgr = findView(R.id.flow_zgr);
        flowYsr = findView(R.id.flow_ysr);
        tvZgrSize = findView(R.id.tv_select_zgr_attention);
        tvYsrSize = findView(R.id.tv_select_ysr_attention);
        View viewBack = findView(R.id.view_back, this);
        linCheckType = findView(R.id.lin_check_type);
        viewPop = findView(R.id.view_pop);
        linSelectPeople = findView(R.id.lin_select_people);
        rvPhoto = findView(R.id.rv_check_img);
        rl_check_photo = findView(R.id.lin_rel_images);
        fraVideo = findView(R.id.fra_check_add_video);
        ivVdieo = findView(R.id.iv_add_video, this);
        rl_photo = findView(R.id.rl_photo);

        //任务数据

        tasksAdapter = new TasksAdapter(this, taskDtoList);
        tasksAdapter.addRecycleItemDeleteListener(new TasksAdapter.OnItemDeleteListener() {
            @Override
            public void OnDeleteClick(int pos, Object o) {
                taskDtoList.remove(pos);
                tasksAdapter.notifyDataSetChanged();
                String str = "已关联 " + taskDtoList.size() + " 个";
                tvTaskSize.setTextColor(getColor(R.color.black));
                tvTaskSize.setText(str);
                modify_flag = "5";
                modifyMissionInfo();
            }
        });
        VerticalNoItemRecyclerView.initialize(rvTasks).setAdapter(tasksAdapter);

        //构件数据

        materialsAdapter = new MaterialsAdapter(this, materialsDtoList);
        materialsAdapter.addRecycleItemDeleteListener(new MaterialsAdapter.OnItemDeleteListener() {
            @Override
            public void OnDeleteClick(int pos, Object o) {
                materialsDtoList.remove(pos);
                materialsAdapter.notifyDataSetChanged();
                String str = "已关联 " + materialsDtoList.size() + " 个";
                tvMaterilaSize.setTextColor(getColor(R.color.black));
                tvMaterilaSize.setText(str);
                modify_flag = "4";
                modifyMissionInfo();
            }
        });
        VerticalNoItemRecyclerView.initialize(rvMaterials).setAdapter(materialsAdapter);

        //照片数据
        checkPhotoAdapter = new CheckPhotoAdapter(photoDtoList, this);
        checkPhotoAdapter.addItemClickListener(new CheckPhotoAdapter.OnItemClickListener() {
            @Override
            public void OnItemClick(int pos, Object o) {
                if (photoDtoList.size() != 0) {
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
                    bundle.putInt(CustomParam.PhotoPosition, pos);
                    Intent intent = new Intent(CheckDetailsActivity.this, PhotoViewActivity.class);
                    intent.putExtras(bundle);
                    startActivity(intent);
                }
            }
        });

        initRecycleview();

    }

    private String getDay(Date date) {//可根据需要自行截取数据显示
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    /**
     * 获取检查详情
     */
    private void getCheckDetail(String id) {
        clearData();
        HashMap<String, String> params = new HashMap<>();
        params.put("examineID", id);
        params.put("token", Hawk.get(CustomParam.Token));
        checkController.getCheckDetail(params,
                new CallBack<CheckDetailsDto>() {

                    @Override
                    public void onSuccess(CheckDetailsDto checkDetailResult) {

                        if (checkDetailResult.getRet() == -1) {
                            showMsg(checkDetailResult.getMsg());

                            //finish();
                            return;
                        }

                        displayResult(checkDetailResult.getData());
                        //整改人员
                        displayZgrPeople(checkDetailResult.getData().getList().getRelationMember_Users());

                        //验收人员
                        displayYsrPeople(checkDetailResult.getData().getList().getPrincipal_Users());

                        //获取新建时相关图片
                        if (checkDetailResult.getData().getList().getImges().size() != 0) {
                            rl_check_photo.setVisibility(View.VISIBLE);
                            for (int i = 0; i < checkDetailResult.getData().getList().getImges().size(); i++) {
                                if (checkDetailResult.getData().getList().getImges().get(i).getBf_path().split("\\.")[1].equals("mp4")) {
                                    videoUrl = Hawk.get(CustomParam.Base_URL) + "/" + checkDetailResult.getData().getList().getImges().get(i).getBf_path();
                                    fraVideo.setVisibility(View.VISIBLE);
                                    getVideoScreenshot(videoUrl, ivVdieo);
                                    break;
                                }
                            }
                            if (TextUtils.isEmpty(videoUrl)) {
                                rl_photo.setVisibility(View.VISIBLE);
                                displayNewPhoto(checkDetailResult.getData().getList().getImges());

                            }

                        }


                        //获取检查图片
                        if (!checkDetailResult.getData().getList().getRecordWithAttachments().isEmpty()) {

                            displayPhoto(
                                    checkDetailResult.getData().getList()
                                            .getRecordWithAttachments()
                                            .get(checkDetailResult.getData().getList().getRecordWithAttachments().size() - 1)
                                            .getAttachments());
                        }
                        //获取检查记录
                        checkDetailData = checkDetailResult;

                        //获取任务数据
                        getTaskData(checkDetailResult.getData().getList().getTasks());

                        //获取构件数据
                        getMaterialsData(checkDetailResult.getData().getList().getMaterials());
                    }

                    @Override
                    public void onFail(String erroMsg) {

                    }
                });
    }

    /**
     * 展示新建关联图片
     */
    private void displayNewPhoto(List<CheckDetailsDto.DataBean.ListBean.ImgesDTO> imagesBeanList) {
        if (photoNewDtoList.size() != 0 || newPhotoList.size() != 0) {
            newPhotoList.clear();
            photoNewDtoList.clear();
        }

        for (CheckDetailsDto.DataBean.ListBean.ImgesDTO bean : imagesBeanList) {

            String imgUrl = Hawk.get(CustomParam.Base_URL) + "/" + bean.getBf_path();
            photoNewDtoList
                    .add(new PhotoDto(imgUrl));
            newPhotoList.add(imgUrl);
        }
        newPhotoAdapter.notifyDataSetChanged();

    }

    /**
     * 获取构件数据
     */
    private void getMaterialsData(List<CheckDetailsDto.DataBean.ListBean.MaterialsBean> materials) {
        tvMaterilaSize.setText("已关联 " + materials.size() + "个");
        materialsDtoList.addAll(materials);
        materialsAdapter.notifyDataSetChanged();
    }

    /**
     * 获取任务数据
     */
    private void getTaskData(List<CheckDetailsDto.DataBean.ListBean.TasksBean> tasks) {
        tvTaskSize.setText("已关联 " + tasks.size() + "个");
        taskDtoList.addAll(tasks);
        tasksAdapter.notifyDataSetChanged();
    }

    /**
     * 获取文件类型
     */
    private boolean isVideoType(String fileType) {

        //选取视频
        return fileType.equalsIgnoreCase(".mp4") || fileType.equalsIgnoreCase(".avi");
    }

    /**
     * 展示数据
     */
    private void displayResult(CheckDetailsDto.DataBean checkDetail) {

        //检查人的内容
        if (!checkDetail.getList().getRecordWithAttachments().isEmpty()) {

            String contentStr =
                    checkDetail.getList().getRecordWithAttachments()
                            .get(checkDetail.getList().getRecordWithAttachments().size() - 1)
                            .getRectificationRemark();
            tvCheckStr.setText(contentStr);
        }

        //刚发起的现场数据没有的数据 隐藏
        if (TextUtils.isEmpty(checkDetail.getList().getAede_severitylevel())) {
            //linCreateCheck.setVisibility(View.GONE);
        } else {
            //linCreateCheck.setVisibility(View.VISIBLE);
        }
        //任务数据 构件数据为可选选项
        if (checkDetail.getList().getTasks().isEmpty()) {
            //linCheckTask.setVisibility(View.GONE);
        } else {
            //linCheckTask.setVisibility(View.VISIBLE);
        }
        if (checkDetail.getList().getMaterials().isEmpty()) {
            //linCheckGouJian.setVisibility(View.GONE);
        } else {
            // linCheckGouJian.setVisibility(View.VISIBLE);
        }

        tvType.setText(checkDetail.getList().getAedt_name());
        //严重等级
        tvSeverityLevel.setText(
                TextUtils.isEmpty(checkDetail.getList().getAede_severitylevel()) ? "严重等级"
                        : checkDetail.getList().getAede_severitylevel());
        //截止日期
        tvEndTime.setText(TextUtils.isEmpty(checkDetail.getList().getRectificateDate()) ? "截止日期"
                : checkDetail.getList().getRectificateDate().split(" ")[0]);
        //开始日期
        tvStartTime.setText(TextUtils.isEmpty(checkDetail.getList().getExamineDate()) ? "开始日期"
                : checkDetail.getList().getExamineDate().split(" ")[0]);
        //检查结果
        tvStatus.setText(
                TextUtils.isEmpty(checkDetail.getList().getExamineResult_CH()) ? "检查结果"
                        : checkDetail.getList().getExamineResult_CH());
        remark = checkDetail.getList().getExamineRemark();
        //检查标题
        tvTittle.setText(TextUtils.isEmpty(remark) ? "数据丢失"
                : remark);
        //检查人
        tvCheckPeople.setText(
                TextUtils.isEmpty(checkDetail.getList().getCheckerName()) ? "检查人" : checkDetail.getList().getCheckerName());

        //判断待检查时 隐藏流转记录UI
        if (checkDetail.getList().getExamineResult().equals("A_ToBeCheck")) {
            btnConfirm.setVisibility(View.VISIBLE);
            linCheckIntro.setVisibility(View.GONE);
            linCheckLevel.setVisibility(View.GONE);
            linCheckPeople.setVisibility(View.GONE);
            rlBottomHasCheck.setVisibility(View.GONE);

            if (checkDetail.getList().getCheckerName().equals(Hawk.get(CustomParam.RealName))) {
                //登陆人为检查人 按钮可点击
                btnConfirm.setOnClickListener(this);
            } else {
                //禁掉 按钮点击状态
                btnConfirm.setOnClickListener(null);
                btnConfirm.setBackground(getDrawable(R.drawable.bg_uncheck_btn_confirm));
            }

            tvStatus.setBackground(getDrawable(R.drawable.bg_tag_waitcheck));

            //登录人为创建人
            loginAsCreater();


        }
        //待整改时 判断登陆人是否有权限编辑页面
        if (checkDetail.getList().getExamineResult().equals("B_ToBeRectified")) {
            rlBottomHasCheck.setVisibility(View.VISIBLE);
            btnConfirm.setVisibility(View.GONE);
            tvCheckPhase.setText(checkDetail.getList().getExamineResult_CH());
            if (!checkDetail.getList().getUser_InRelation()) {

                linHasChcek.setBackground(getDrawable(R.drawable.bg_uncheck_btn_confirm));
                linHasChcek.setOnClickListener(null);
            }
            tvStatus.setBackground(getDrawable(R.drawable.bg_tag_rectification));
        }
        //待验收时
        if (checkDetail.getList().getExamineResult().equals("C_ToBeRecheck")) {
            btnConfirm.setVisibility(View.GONE);
            tvCheckPhase.setText(checkDetail.getList().getExamineResult_CH());
            if (!checkDetail.getList().getUser_InPrincipal()) {
                linHasChcek.setBackground(getDrawable(R.drawable.bg_uncheck_btn_confirm));
                linHasChcek.setOnClickListener(null);
            }
            tvStatus.setBackground(getDrawable(R.drawable.bg_tag_applying));
        }

        //已经合格
        if (checkDetail.getList().getExamineResult().equals("D_Qualified")) {
            btnConfirm.setVisibility(View.GONE);
            tvCheckPhase.setText(checkDetail.getList().getExamineResult_CH());
            linHasChcek.setOnClickListener(null);
            tvStatus.setBackground(getDrawable(R.drawable.bg_tag_ok));
            linCheckLevel.setVisibility(View.GONE);
            linCheckPeople.setVisibility(View.GONE);
        }


        //已经关闭
        if (checkDetail.getList().getExamineResult().equals("E_Closed")) {
            btnConfirm.setVisibility(View.GONE);
            tvCheckPhase.setText(checkDetail.getList().getExamineResult_CH());
            linHasChcek.setBackground(getDrawable(R.drawable.bg_closed_btn_confirm));
            linHasChcek.setOnClickListener(null);
            tvStatus.setBackground(getDrawable(R.drawable.bg_tag_closed));
        }

    }

    /**
     * 重置数据
     */
    private void clearData() {

        headList.clear();
        headIdList.clear();
        joinList.clear();
        joinIDList.clear();
        taskDtoList.clear();
        materialsDtoList.clear();
        photoDtoList.clear();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        newPhotoAdapter = new NewPhoto2CheckAdapter(newPhotoList);
        HorizontalNoItemRecyclerView.initialize(rvPhoto).setAdapter(newPhotoAdapter);
        newPhotoAdapter.addItemClickListener(new NewPhoto2CheckAdapter.OnItemClickListener() {
            @Override
            public void OnItemClick(int pos) {
                if (photoNewDtoList.size() != 0) {
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoNewDtoList);
                    bundle.putInt(CustomParam.PhotoPosition, pos);
                    Intent intent = new Intent(CheckDetailsActivity.this, PhotoViewActivity.class);
                    intent.putExtras(bundle);
                    startActivity(intent);
                }
            }
        });


    }

    /**
     * 去除重复数据
     */
    public List<String> removeStringListDupli(List<String> stringList) {
        Set<String> set = new LinkedHashSet<>();
        set.addAll(stringList);

        stringList.clear();

        stringList.addAll(set);
        return stringList;
    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onActivityReenter(int resultCode, Intent data) {
        super.onActivityReenter(resultCode, data);
    }

 /* @OnClick({
      R.id.view_back, R.id.iv_delete_photo,
      R.id.iv_again_upload_photo,
      R.id.lin_select_severity, R.id.lin_select_join, R.id.lin_check_result,
      R.id.iv_upload, R.id.lin_select_head, R.id.tv_record, R.id.lin_check,
      R.id.btn_check_details_confirm
  })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.view_back:
        finish();
        break;
      case R.id.iv_delete_photo:
        //删除
        //deleteRelevance(bannerPhotoIdList.get(selectedPosition));
        if (!bannerList.isEmpty() || bannerList.size() > 0) {
          deleteCheckPhoto(bannerPhotoIdList.get(selectedPosition), selectedPosition);
        }

        break;
      case R.id.iv_again_upload_photo:
      case R.id.iv_upload:
        getPhoto();
        break;

      case R.id.lin_limit_time:
        pvTime.show();
        break;

      case R.id.lin_select_severity:

        showLevelPop();
        break;
      case R.id.lin_select_join:
        showJoinPop(DeepCopyUtils.deepCopy(joinList), DeepCopyUtils.deepCopy(joinIDList));
        break;
      case R.id.lin_select_ps:
        Intent intent = new Intent(this, ProjectStructureActivity.class);
        startActivityForResult(intent, code_ps);
        break;

      case R.id.lin_select_head:
        //showHeadPop(DeepCopyUtils.deepCopy(headList), DeepCopyUtils.deepCopy(headIdList));
        break;
      case R.id.tv_record:
        //流转记录
        Intent intent3 = new Intent(CheckDetailsActivity.this, CheckRecordListActivity.class);
        intent3.putExtra(CustomParam.CheckRecordList, checkDetailData);
        startActivity(intent3);

        break;
      case R.id.lin_check:
      case R.id.btn_check_details_confirm:
        //根据状态 和 人员 确定 检查分类
        // 状态————————》》     待检查   待验收   验收
        // 人员————————》》     检查人   整改人   验收人

        //  A_ToBeCheck   B_ToBeRectified  C_ToBeRecheck  D_Qualified

        switch (checkDetailData.getData().getExamineResult()) {

          case "A_ToBeCheck":
            //检查
            if (checkDetailData.getData().isUser_IsChecker()) {

              checkType = 0;
            } else {
              showMsg("您不是检查人，无法检查");
              return;
            }

            break;
          case "B_ToBeRectified":
            //整改
            if (checkDetailData.getData().isUser_InRelation()) {
              checkType = 1;
            } else {
              showMsg("您不是整改人，无法申请复检");
              return;
            }

            break;
          case "C_ToBeRecheck":
            //验收
            if (checkDetailData.getData().isUser_InPrincipal()) {

              checkType = 2;
            } else {

              showMsg("您不是验收人，无法验收");
              return;
            }

            //待验收
            break;
          case "D_Qualified":
            //已合格
            break;

          default:
            break;
        }
        Intent intent1 = new Intent(this, CheckActivity.class);
        intent1.putExtra(CustomParam.Examineid, examineId);
        intent1.putExtra(CustomParam.CheckType, checkType);
        startActivity(intent1);
        break;
      default:
        break;
    }
  }
*/

    @Override
    public void onClick(View v) {
        Intent intent;
        CloseKeybordUtils.closeKeybord(CheckDetailsActivity.this);
        switch (v.getId()) {

            case R.id.btn_check_details_confirm:
                //根据状态 和 人员 确定 检查分类
                // 状态————————》》     待检查   待验收   验收
                // 人员————————》》     检查人   整改人   验收人

                //  A_ToBeCheck   B_ToBeRectified  C_ToBeRecheck  D_Qualified

                switch (checkDetailData.getData().getList().getExamineResult()) {

                    case "A_ToBeCheck":
                        //检查
                        if (checkDetailData.getData().getList().getUser_IsChecker()) {

                            checkType = 0;
                        } else {
                            showMsg("您不是检查人，无法检查");
                            return;
                        }

                        break;
                    case "B_ToBeRectified":
                        //整改
                        if (checkDetailData.getData().getList().getUser_InRelation()) {
                            checkType = 1;
                        } else {
                            showMsg("您不是整改人，无法申请复检");
                            return;
                        }

                        break;
                    case "C_ToBeRecheck":
                        //验收
                        if (checkDetailData.getData().getList().getUser_InPrincipal()) {

                            checkType = 2;
                        } else {

                            showMsg("您不是验收人，无法验收");
                            return;
                        }

                        //待验收
                        break;
                    case "D_Qualified":
                        //已合格
                        break;

                    default:
                        break;
                }
                intent = new Intent(this, CheckActivity.class);
                intent.putExtra(CustomParam.Examineid, examineId);
                intent.putExtra(CustomParam.CheckType, checkType);
                startActivity(intent);
                break;
            case R.id.lin_has_check:
                switch (checkDetailData.getData().getList().getExamineResult()) {

                    case "B_ToBeRectified":
                        //整改
                        if (checkDetailData.getData().getList().getUser_InRelation()) {
                            checkType = 1;
                        } else {
                            showMsg("您不是整改人，无法申请复检");
                            return;
                        }

                        break;
                    case "C_ToBeRecheck":
                        //验收
                        if (checkDetailData.getData().getList().getUser_InPrincipal()) {

                            checkType = 2;
                        } else {

                            showMsg("您不是验收人，无法验收");
                            return;
                        }

                        //待验收
                        break;
                    case "D_Qualified":
                        //已合格
                        break;

                    default:
                        break;
                }
                intent = new Intent(this, CheckActivity.class);
                intent.putExtra(CustomParam.Examineid, examineId);
                intent.putExtra(CustomParam.CheckType, checkType);
                startActivity(intent);

                break;
            case R.id.view_back:
                finish();
                break;
            case R.id.tv_check_record:
                //流转记录
                intent = new Intent(CheckDetailsActivity.this, CheckRecordListActivity.class);
                intent.putExtra(CustomParam.CheckRecordList, checkDetailData);
                startActivity(intent);

                break;
            case R.id.img_check_left:
            case R.id.img_check_middle:
            case R.id.aplh_view:
                if (photoDtoList.size() != 0) {
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
                    bundle.putInt(CustomParam.PhotoPosition, 0);
                    intent = new Intent(CheckDetailsActivity.this, PhotoViewActivity.class);
                    intent.putExtras(bundle);
                    startActivity(intent);
                }
                break;
            case R.id.iv_check_video_play:
            case R.id.iv_add_video:
                intent = new Intent(this, Video4DetailsActivity.class);
                intent.putExtra(CustomParam.VIDEOURL, videoUrl);
                startActivity(intent);
                break;
            case R.id.lin_select_material:
                intent = new Intent(this, AllMaterialListActivity.class);
                intent.putExtra("isCheckDetails", true);
                startActivityForResult(intent, code_select_materials);
                break;
            case R.id.lin_select_task:
                intent = new Intent(this, AllTaskListActivity.class);
                intent.putExtra("isCheckDetails", true);
                startActivityForResult(intent, code_select_task);
                break;
            default:
                break;
        }
    }

    /**
     * 选取照片
     */
    private void getPhoto() {
        Matisse.from(this)
                .choose(MimeType.ofAll())
                .countable(true)
                .capture(true)
                .captureStrategy(
                        new CaptureStrategy(true, getPackageName()))
                .maxSelectable(9)
                .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
                .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
                .thumbnailScale(0.85f)
                .imageEngine(new GlideEngine())
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                .forResult(code_photo);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != RESULT_OK) {
            return;
        }
        switch (requestCode) {

            case code_photo:
                photoFileList.clear();
                for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {

                    File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
                    File fileCompress = CompressHelper.getDefault(getApplicationContext())
                            .compressToFile(fileOrigin);

                    photoFileList.add(fileCompress);


          /*photoDtoList.add(new PhotoDto(fileCompress.getAbsolutePath()));

          photoFileList.add(fileCompress);*/

                    //bannerList.add(fileCompress.getAbsolutePath());
                }

                if (!photoFileList.isEmpty()) {
                    UploadPhoto();
                }
       /* banner.setImages(bannerList)
            .setImageLoader(new GlideImageLoader())
            .setIndicatorGravity(BannerConfig.CENTER)
            .isAutoPlay(false)
            .start();*/
                break;


            case code_select_people:

                CheckPeopleDto checkPeopleDto = (CheckPeopleDto) data.getExtras().getSerializable(CustomParam.StartForResult);
                //检查人 唯一
                if (!checkPeopleDto.getName().isEmpty() && !checkPeopleDto.getUserId().isEmpty()) {

                    modifyMissionCheckUser(checkPeopleDto);
                }
                break;
            case code_select_materials:

                List<MateriaOriginListBean.DataBean.ListBean> materialListData =
                        (List<MateriaOriginListBean.DataBean.ListBean>) data.getExtras()
                                .getSerializable(CustomParam.StartForResult);
                //构件数据
                if (!materialListData.isEmpty()) {
                    materialsDtoList.clear();
                    for (MateriaOriginListBean.DataBean.ListBean beans : materialListData) {
                        CheckDetailsDto.DataBean.ListBean.MaterialsBean bean = new CheckDetailsDto.DataBean.ListBean.MaterialsBean();
                        bean.setBm_guid(beans.getBm_guid());
                        bean.setBm_materialname(beans.getBm_materialname());
                        bean.setBm_materialcode(beans.getBm_materialcode());
                        bean.setBc_guid_materialtype(beans.getBc_guid_materialtype());
                        bean.setBm_updatetime(beans.getBm_updatetime());
                        bean.setBc_guid_materialstatus(beans.getBc_guid_materialstatus());
                        bean.setBm_extjson(beans.getBm_extjson());
                        bean.setBm_materialcount((int) beans.getBm_materialcount());
                        bean.setBm_materialfac(beans.getBm_materialfac());
                        bean.setBm_materialmodel(beans.getBm_materialmodel());
                        materialsDtoList.add(bean);
                    }
                    String str = "已关联 " + materialsDtoList.size() + " 个";
                    tvMaterilaSize.setTextColor(getColor(R.color.black));
                    tvMaterilaSize.setText(str);
                    materialsAdapter.notifyDataSetChanged();
                    modify_flag = "4";
                    modifyMissionInfo();
                }

                break;

            case code_select_task:

                List<AllTaskItemResult.DataBean.TasksBean> taskListData =
                        (List<AllTaskItemResult.DataBean.TasksBean>) data.getExtras()
                                .getSerializable(CustomParam.StartForResult);
                //任务数据
                if (!taskListData.isEmpty()) {
                    taskDtoList.clear();
                    for (AllTaskItemResult.DataBean.TasksBean beans : taskListData) {
                        CheckDetailsDto.DataBean.ListBean.TasksBean bean = new CheckDetailsDto.DataBean.ListBean.TasksBean();
                        bean.setUID_(beans.getUID_());
                        bean.setNAME_(beans.getNAME_());
                        taskDtoList.add(bean);
                    }
                    String str = "已关联 " + taskListData.size() + " 个";
                    tvTaskSize.setTextColor(getColor(R.color.black));
                    tvTaskSize.setText(str);
                    tasksAdapter.notifyDataSetChanged();
                    modify_flag = "5";
                    modifyMissionInfo();
                }

                break;


            default:
                break;
        }
    }

    /**
     * 获取系统当前时间
     */
    private String getSystemDate() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd  HH:mm:ss");
        return format.format(new Date(System.currentTimeMillis()));
    }

    private void NoAuthModify() {
        NoAuthAdapter();
    /*ivAgainUploadPhoto.setOnClickListener(null);
    ivDeletePhoto.setOnClickListener(null);
    //linSelectType.setOnClickListener(null);
    linSelectSeverity.setOnClickListener(null);
    gridHead.setOnItemClickListener(null);
    gridJoin.setOnItemClickListener(null);
    linLimitTime.setOnClickListener(null);
    linCheckResult.setOnClickListener(null);
    linSelectHead.setOnClickListener(null);
    linSelectJoin.setOnClickListener(null);*/
        //linSelectPs.setOnClickListener(null);
    }

    // 可以检查
    private void AuthModify() {
        AuthAdapter();
    }

    //无权限适配器
    private void NoAuthAdapter() {
        problemHeadAdapter = new ProblemHeadAdapter(headList, this, true);
        //gridHead.setAdapter(problemHeadAdapter);
        problemJoinAdapter = new ProblemJoinAdapter(joinList, this, true);
        // gridJoin.setAdapter(problemJoinAdapter);
        getProjectUsers();
    }

    //权限适配器
    private void AuthAdapter() {
        problemHeadAdapter = new ProblemHeadAdapter(headList, this, true);
        //gridHead.setAdapter(problemHeadAdapter);
        problemJoinAdapter = new ProblemJoinAdapter(joinList, this, true);
        //gridJoin.setAdapter(problemJoinAdapter);
        getProjectUsers();
    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getProjectID();
        }
    }

    @Override
    public void onPageScrolled(int i, float v, int i1) {

    }

    @Override
    public void onPageSelected(int i) {
        selectedPosition = i;
    }

    @Override
    public void onPageScrollStateChanged(int i) {

    }

    /**
     * 请选择施严重等级
     */
    private void showLevelPop() {

        View view = LayoutInflater.from(CheckDetailsActivity.this)
                .inflate(R.layout.popwindow_new_check_level, null);
        levelPop = new PopupWindow(CheckDetailsActivity.this);
        levelPop.setContentView(view);
        levelPop.setFocusable(true);
        levelPop.setOutsideTouchable(true);
        levelPop.setBackgroundDrawable(new BitmapDrawable());
        levelPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        levelPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setBackgroundAlpha(0.5f);
        levelWheelView = view.findViewById(R.id.level_wheel_view);
        levelWheelView.setOffset(1);
        levelWheelView.setItems(wheelLevelList);
        tvWheelLevel = view.findViewById(R.id.tv_check_Level);
        tvWheelConfirm = view.findViewById(R.id.tv_confirm);
        initLevleWheelView();
        levelPop.setAnimationStyle(R.style.picker_view_slide_anim);
        levelPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });

        // levelPop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
    }

    /**
     * 初始化严重等级
     */
    private void initLevleWheelView() {

        wheelLevelSelecde = wheelLevelList.get(0);

        levelWheelView.setOnWheelViewListener(new WheelView.OnWheelViewListener() {
            @Override
            public void onSelected(int selectedIndex, String item) {
                wheelLevelSelecde = item;
                //majorIdSelected = wheelMajorIdList.get(selectedIndex - 1);
                //TypeIdSelected = wheelTypeIDList.get(selectedIndex - 1);
            }
        });

        tvWheelConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                tvWheelLevel.setText(wheelLevelSelecde);
                //tvCheckSeverity.setText(wheelLevelSelecde);
                //tvCheckSeverity.setTextColor(getResources().getColor(R.color.black));
                modifyLevel();
                UIUtils.postTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        levelPop.dismiss();
                    }
                }, 200);
            }
        });
    }

    /**
     * 修改参数
     *
     * "Token": "string",
     * "ExamineID": "string",
     * "CategoryID": "string",
     * "ExamineResult": "string",
     * "RectificateDate": "string",
     * "PrincipalID": "string",
     * "aede_checkeruserids": "string",
     * "aede_examinetype": "string",
     * "aede_severitylevel": "string"
     */

    /**
     * 设置popwindow默认背景变灰
     */
    public void setBackgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha;
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        getWindow().setAttributes(lp);
    }

    /**
     * 修改整改人  验收人
     */
    private void modifyCheckPeople(boolean isZhengGaiRen, String peopleStr) {
        /**
         *
         * Token (string, optional): 口令 ,
         * RelationMemberID (string, optional): 多个整改人 RelationMemberID 与 PrincipalID 不能同时为空，哪个不为空改哪个，都不为空只修改 RelationMemberID. ,
         * PrincipalID (string, optional): 多个验收人 RelationMemberID 与 PrincipalID 不能同时为空，哪个不为空改哪个，都不为空只修改 RelationMemberID. ,
         * ExamineID (string, optional): 哪条数据 ,
         * OrganizeId (string, optional): 哪个项目ID
         */
        if (checkDetailData.getData().getList().getExamineResult().equals("B_ToBeRectified")
                && checkDetailData.getData().getList().getUser_IsChecker()) {

            //状态为 待整改 登陆人为检查人

            HashMap<String, String> params = new HashMap<>();
            params.put("Token", Hawk.get(CustomParam.Token));
            if (isZhengGaiRen) {
                //整改人
                params.put("RelationMemberID", peopleStr);
            } else {
                //验收人
                params.put("PrincipalID", peopleStr);
            }
            params.put("ExamineID", examineId);
            params.put("OrganizeId", organizeId);
            checkController.modifyCheckPeople(params, new CallBack<CheckResult>() {
                @Override
                public void onSuccess(CheckResult checkResult) {
                    if (checkResult.getRet() == 1) {
                        getCheckDetail(examineId);
                    }
                }

                @Override
                public void onFail(String erroMsg) {

                }
            });
        }
    }

    /**
     * 获取项目所有人员
     */
    private void getProjectUsers() {

        HashMap<String, String> params = new HashMap<>();
        params.put(CustomParam.Token, Hawk.get(CustomParam.Token));
        params.put("ProjectID", organizeId);
        params.put("RoleId", "-1");
        params.put("encodedKeyWord", "");
        checkController.getCheckJoiner(params, new CallBack<IssueJoinerResult>() {

            @Override
            public void onSuccess(IssueJoinerResult issueJoinerResult) {
                projectRoleList.clear();
                for (IssueJoinerResult.DataBean dataBean : issueJoinerResult.getData()) {
                    projectRoleList.add(dataBean);
                }
                problemJoinAdapter.notifyDataSetChanged();
                problemHeadAdapter.notifyDataSetChanged();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //删除问题照片
    private void deleteCheckPhoto(String photoFileId, int pos) {
        //移除附件：Token, ExamineAttachmentIDs， 英文逗号分隔的多个 ExamineAttachmentID。 追加多张图片，参数：File1, File2 ... FileN， ExamineID， Token
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ExamineID", examineId);
        params.put("ExamineAttachmentIDs", photoFileId);
        checkController.deleteCheckPhoto(params, new CallBack<DeleteCheckPhotoResult>() {

            @Override
            public void onSuccess(DeleteCheckPhotoResult deleteCheckPhotoResult) {
                if (deleteCheckPhotoResult.getRet() == 1) {
                    getCheckDetail(examineId);
                    showMsg("删除照片成功");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 上传图片
     */
    private void UploadPhoto() {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ExamineID", examineId);
        checkController.updateCheckPhoto(photoFileList, params, new CallBack<UpdateCheckPhotoResult>() {

            @Override
            public void onSuccess(UpdateCheckPhotoResult updateCheckPhotoResult) {
                if (updateCheckPhotoResult.getRet() == 1) {
                    photoFileList.clear();
                    bannerList.clear();
                    bannerPhotoIdList.clear();
                    photoDtoList.clear();
                    getCheckDetail(examineId);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /***
     * 修改严重等级
     */
    private void modifyLevel() {

        if (checkDetailData.getData().getList().getExamineResult().equals("B_ToBeRectified")
                && checkDetailData.getData().getList().getUser_IsChecker()) {

            HashMap<String, String> params = new HashMap<>();
            params.put("Token", Hawk.get(CustomParam.Token));
            params.put("aede_severitylevel", wheelLevelSelecde);
            params.put("ExamineID", examineId);
            params.put("OrganizeId", organizeId);
            checkController.modifyCheckSe(params, new CallBack<CheckResult>() {
                @Override
                public void onSuccess(CheckResult checkResult) {
                    if (checkResult.getRet() == 1) {
                        getCheckDetail(examineId);
                    }
                }

                @Override
                public void onFail(String erroMsg) {

                }
            });
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        getCheckDetail(examineId);
    }

    /**
     * 图片展示
     */
    public void displayPhoto(
            List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean.AttachmentsBean> list) {

        if (list.isEmpty()) {

            return;

        }

        if (isVideoType(list.get(0).getAttachmentType())) {

            fraCheckVideo.setVisibility(View.VISIBLE);
            videoUrl = Hawk.get(CustomParam.Base_URL) + "/" + list.get(0)
                    .getAttachmentUrl();
            getVideoScreenshot(videoUrl, ivCheckVideo);

        } else {
            linCheckMoreImg.setVisibility(View.VISIBLE);
            photoDtoList.clear();
            for (CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean.AttachmentsBean attachmentsBean : list) {
                String url = Hawk.get(CustomParam.Base_URL) + attachmentsBean.getAttachmentUrl();
                photoDtoList.add(new PhotoDto(url));
            }

        }

        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        //String url = Hawk.get(CustomParam.Base_URL) + bean.getAttachmentUrl();
        if (list.size() == 1) {

            Glide.with(this).load(Hawk.get(CustomParam.Base_URL) + "/" + list.get(0).getAttachmentUrl()).apply(RequestOptions.overrideOf(displayMetrics.widthPixels - dpToPx(10), dpToPx(184)).centerCrop())
                    .into(ivPhotoLeft);
            ivPhotoMiddle.setVisibility(View.GONE);
            ivPhotoRight.setVisibility(View.GONE);
        } else if (list.size() == 2) {

            int imageWidth = (displayMetrics.widthPixels - dpToPx(20)) / 2;

            Glide.with(this).load(Hawk.get(CustomParam.Base_URL) + "/" + list.get(0).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(184)).centerCrop())
                    .into(ivPhotoLeft);
            Glide.with(this).load(Hawk.get(CustomParam.Base_URL) + "/" + list.get(1).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(184)).centerCrop())
                    .into(ivPhotoMiddle);

            ivPhotoRight.setVisibility(View.GONE);
        } else if (list.size() >= 3) {

            ivPhotoAplh.setVisibility(View.VISIBLE);
            tvPhotoSize.setText(list.size() + "");
            int imageWidth = (displayMetrics.widthPixels - dpToPx(30)) / 3;

            Glide.with(this)
                    .load(R.mipmap.bg_img_size).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)).centerCrop())
                    .into(ivPhotoAplh);
            Glide.with(this).load(Hawk.get(CustomParam.Base_URL) + "/" + list.get(0).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)).centerCrop())
                    .into(ivPhotoLeft);
            Glide.with(this).load(Hawk.get(CustomParam.Base_URL) + "/" + list.get(1).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)).centerCrop())
                    .into(ivPhotoMiddle);
            Glide.with(this).load(Hawk.get(CustomParam.Base_URL) + "/" + list.get(2).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)).centerCrop())
                    .into(ivPhotoRight);
        }
    }

    public int dpToPx(float dp) {
        float px = getResources().getDisplayMetrics().density;
        return (int) (dp * px + 0.5f);
    }

    /**
     * 整改人 人员展示
     */
    private void displayZgrPeople(List<CheckDetailsDto.DataBean.ListBean.RelationMemberUsersBean> list) {

        tvZgrSize.setText("已选择" + list.size() + "人");

        TagAdapter<List<CheckDetailsDto.DataBean.ListBean.RelationMemberUsersBean>> adapter =
                new TagAdapter(list) {
                    @Override
                    public View getView(FlowLayout parent, int position, Object o) {
                        TextView tv = (TextView) LayoutInflater.from(CheckDetailsActivity.this)
                                .inflate(R.layout.item_flow_people,
                                        parent, false);
                        tv.setText(list.get(position).getRealName());
                        return tv;
                    }
                };

        flowZgr.setAdapter(adapter);
        flowZgr.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {

                return true;
            }
        });
        flowZgr.setOnSelectListener(new TagFlowLayout.OnSelectListener() {
            @Override
            public void onSelected(Set<Integer> selectPosSet) {

            }
        });
    }

    /**
     * 整改人 人员展示
     */
    private void displayYsrPeople(List<CheckDetailsDto.DataBean.ListBean.PrincipalUsersBean> list) {

        tvYsrSize.setText("已选择" + list.size() + "人");
        TagAdapter<List<CheckDetailsDto.DataBean.ListBean.PrincipalUsersBean>> adapter =
                new TagAdapter(list) {
                    @Override
                    public View getView(FlowLayout parent, int position, Object o) {
                        TextView tv = (TextView) LayoutInflater.from(CheckDetailsActivity.this)
                                .inflate(R.layout.item_flow_people,
                                        parent, false);
                        tv.setText(list.get(position).getRealName());
                        return tv;
                    }
                };

        flowYsr.setAdapter(adapter);
        flowYsr.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {

                return true;
            }
        });
        flowYsr.setOnSelectListener(new TagFlowLayout.OnSelectListener() {
            @Override
            public void onSelected(Set<Integer> selectPosSet) {

            }
        });
    }

    /**
     * 获取视频第一帧功能
     *
     * @param uri
     * @param imageView
     */
    public void getVideoScreenshot(String uri, ImageView imageView) {
        RequestOptions requestOptions = RequestOptions.frameOf(0);
        requestOptions.set(FRAME_OPTION, MediaMetadataRetriever.OPTION_CLOSEST);
        requestOptions.transform(new BitmapTransformation() {
            @Override
            protected Bitmap transform(@NonNull BitmapPool pool, @NonNull Bitmap toTransform, int outWidth, int outHeight) {
                return toTransform;
            }

            @Override
            public void updateDiskCacheKey(MessageDigest messageDigest) {
                try {
                    messageDigest.update((getPackageName() + "RotateTransform").getBytes(StandardCharsets.UTF_8));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        Glide.with(this).load(uri).apply(requestOptions).into(imageView);
    }

    /**
     * 时间选择器
     */
    private void TimePicker(TextView tvTime) {

        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);
        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                timeStr = getDay(date);
                tvTime.setText(timeStr);

            }
        }).setCancelText("清空")
                .setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false})
                .setContentTextSize(16)
                .setLabel("", "", "", "", "", "")
                .isCenterLabel(true)
                .setLineSpacingMultiplier(3.0f)
                .setDividerColor(R.color.divider_color)
                .setDecorView(null)
                .setRangDate(startDate, endDate)
                .setTextColorCenter(Color.parseColor("#283A4F"))
                .setTextColorOut(Color.parseColor("#A6AEB6"))
                //容器
                //.setDecorView(linContainer)
                .setLayoutRes(R.layout.pickerview_newcheck,
                        new CustomListener() {
                            @Override
                            public void customLayout(View v) {

                                TextView tvDateTittle = v.findViewById(R.id.tv_date_title);
                                if (tvTime.getId() == R.id.tv_select_start_time) {

                                    tvDateTittle.setText("请选择开始日期");
                                } else {
                                    tvDateTittle.setText("请选择截止日期");
                                }

                                TextView btnConfirm = v.findViewById(R.id.btn_confirm);
                                btnConfirm.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        pvTime.returnData();
                                        pvTime.dismiss();
                                        //修改时间
                                        if (tvTime.getId() == R.id.tv_select_start_time) {
                                            modify_flag = "2";
                                            startTimeStr = timeStr;
                                            modifyMissionInfo();
                                        } else {
                                            modify_flag = "2";
                                            endTimeStr = timeStr;
                                            modifyMissionInfo();
                                        }


                                    }
                                });

                                ImageView ivClose = (ImageView) v.findViewById(R.id.iv_close);
                                ivClose.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        //pvTime.returnData();
                                        pvTime.dismiss();
                                    }
                                });
                            }
                        })
                .build();
        pvTime.show();
    }

    /**
     * 登录人修改部分数据
     */
    private void modifyMissionInfo() {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ExamineID", examineId);
        //检查类别guid
        params.put("aedt_guid", typeIDSelected);
        //标题
        String title = tvTittle.getText().toString().trim();
        params.put("Title", title);
        //organizeId
        params.put("organizeId", organizeId);
        //开始日期
        params.put("BeginTimeStr", startTimeStr);
        //结束日期
        params.put("EndTimeStr", endTimeStr);

        params.put("modify_flag", modify_flag);
        // 构件数据
        params.put("rel_materialjson", JsonHelper.toJson(materialsDtoList));
        //
        params.put("rel_taskjson", JsonHelper.toJson(taskDtoList));
        checkController.modifyMissionInfo(params, new CallBack<CheckResult>() {
            @Override
            public void onSuccess(CheckResult checkResult) {
                if (checkResult.getRet() == 1) {

                    showMsg("修改成功");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 登录人创建人
     */

    private void loginAsCreater() {

        //待检查时 创建人可修改部分数据
        if (Hawk.get(CustomParam.UserId).equals(createrId)) {
            //showMsg("当前登录人为创建人");
//            tvTittle.setFocusableInTouchMode(true);
//            tvTittle.setFocusable(true);
//            tvTittle.requestFocus();
            tvTittle.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {

                    //修改标题
                    if (TextUtils.isEmpty(s.toString())) {
                        return;
                    }
                    if (!s.toString().equals(remark)) {
                        modify_flag = "1";
                        modifyMissionInfo();
                    }

                }
            });

            tvStartTime.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CloseKeybordUtils.closeKeybord(CheckDetailsActivity.this);
                    TimePicker(tvStartTime);
                }
            });
            tvEndTime.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CloseKeybordUtils.closeKeybord(CheckDetailsActivity.this);
                    TimePicker(tvEndTime);
                }
            });
        }
        linCheckType.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CloseKeybordUtils.closeKeybord(CheckDetailsActivity.this);
                getCheckType();
            }
        });
        linSelectPeople.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(CheckDetailsActivity.this, AllPeopleListActivity.class);
                intent.putExtra("isCheckDetails", true);
                startActivityForResult(intent, code_select_people);
            }
        });

    }

    /**
     * 获检查分类
     */
    private void getCheckType() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", organizeId);
        if (linkType.equals("1")) {
            params.put("aedtType", "quality");
        } else {
            params.put("aedtType", "security");
        }
        checkController.getCheckType(params, new CallBack<CheckTypeResult>() {
            @Override
            public void onSuccess(CheckTypeResult checkTypeResult) {
                linCheckType.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        CheckTypePopWindow.getInstance()
                                .showPopWindow(CheckDetailsActivity.this, viewPop, checkTypeResult.getData().getList(),
                                        new CheckTypePopWindow.onSelectListener() {
                                            @Override
                                            public void OnSelected(int postion) {

                                                typeIDSelected = checkTypeResult.getData().getList().get(postion).getAedtGuid();
                                                tvType.setText(checkTypeResult.getData().getList().get(postion).getAedtName());
                                                //修改分类
                                                modify_flag = "3";
                                                modifyMissionInfo();


                                            }
                                        });
                    }
                });
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 登录人修改部分数据
     */
    private void modifyMissionCheckUser(CheckPeopleDto dataBean) {

        String checkerUserId = dataBean.getUserId();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ExamineID", examineId);
        //organizeId
        params.put("OrganizeId", organizeId);
        params.put("CheckerUserId", checkerUserId);
        checkController.modifyMissionCheckUser(params, new CallBack<CheckResult>() {
            @Override
            public void onSuccess(CheckResult checkResult) {
                if (checkResult.getRet() == 1) {
                    tvCheckPeople.setText(dataBean.getName());
                    showMsg("修改成功");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

}

