package com.probim.bimenew.activity.schedule.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.result.IssueDetailResult;

import java.util.List;

public class SpinnerCommentAdapter extends BaseAdapter {
    private List<IssueDetailResult.DataBean.JoinersBean> dataList;


    public SpinnerCommentAdapter(List<IssueDetailResult.DataBean.JoinersBean> dataList) {
        this.dataList = dataList;
    }

    @Override
    public int getCount() {
        return dataList.size();
    }

    @Override
    public Object getItem(int i) {
        return dataList.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(parent.getContext()).inflate(R.layout.spinner_plan, parent, false);
        }
        TextView textView = convertView.findViewById(R.id.tv_plan_name);
        IssueDetailResult.DataBean.JoinersBean data = dataList.get(position);
        textView.setText(data.getRealName());
        return convertView;
    }
}
