package com.probim.bimenew.activity.check;

import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.EdtTitleActivity;
import com.probim.bimenew.adapter.CheckHeadAdapter;
import com.probim.bimenew.adapter.CheckJoinerAdapter;
import com.probim.bimenew.adapter.ProblemHeadAdapter;
import com.probim.bimenew.adapter.ProblemJoinAdapter;
import com.probim.bimenew.adapter.ProblemPopWindowHeadAdapter;
import com.probim.bimenew.adapter.ProblemPopWindowJoinAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.model.ProjectUser;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.CheckResult;
import com.probim.bimenew.result.CheckUploadPhotoResult;
import com.probim.bimenew.result.IssueJoinerResult;
import com.probim.bimenew.utils.DeepCopyUtils;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.StringUtils;
import com.probim.bimenew.utils.WheelView;
import com.probim.bimenew.utils.bannerLoader.GlideImageLoader;
import com.probim.bimenew.utils.view.MyGridView;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;
import com.youth.banner.listener.OnBannerListener;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * Description : 检查 复检 验收
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/08/16/14:40.
 */

public class CheckActivityBack extends BaseActivity implements OnBannerListener,
    ViewPager.OnPageChangeListener {
  @BindView(R.id.iv_back) ImageView ivBack;
  @BindView(R.id.tv_left) TextView tvLeft;
  @BindView(R.id.lin_back) LinearLayout linBack;
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.tv_right) TextView tvRight;
  @BindView(R.id.iv_upload) ImageView ivUpload;
  @BindView(R.id.edt_title) TextView edtTitle;
  @BindView(R.id.grid_head) MyGridView gridHead;
  @BindView(R.id.lin_select_head) LinearLayout linSelectHead;
  @BindView(R.id.grid_join) MyGridView gridJoin;
  @BindView(R.id.lin_select_join) LinearLayout linSelectJoin;
  @BindView(R.id.tv_check_result) TextView tvCheckResult;
  @BindView(R.id.lin_check_result) LinearLayout linCheckResult;
  @BindView(R.id.banner) Banner banner;
  @BindView(R.id.lin_select_photo) LinearLayout linSelectPhoto;
  @BindView(R.id.lin_all) LinearLayout linAll;
  @BindView(R.id.tv_check_level) TextView tvCheckLevel;
  @BindView(R.id.tv_select_yanshouren) TextView tvSelectYsr;
  @BindView(R.id.tv_select_zhenggairen) TextView tvSelectZgr;
  @BindView(R.id.lin_cc) LinearLayout linCC;
  @BindView(R.id.btn_confirm) Button btnConfirm;
  @BindView(R.id.lin_check_yanshou) LinearLayout linCheckYanShou;
  @BindView(R.id.tv_check_yanshou) TextView tvCheckYanShou;
  @BindView(R.id.iv_video) ImageView ivVideo;
  @BindView(R.id.rl_video) RelativeLayout rlVideo;
  private String titleResult;
  public static boolean isRecordAudio = false;
  private List<String> bannerList = new ArrayList<>();
  private final int code_photo = 3000;
  private int selectedPosition;
  private final int code_title = 1000;
  private final int code_intro = 2000;
  private final int code_ps = 7000;
  private CheckController checkController;
  private List<String> upLoadPhotoList = new ArrayList<>();
  private List<String> upLoadAudioList = new ArrayList<>();
  private String uploadFileId = "";
  private List<File> photoFileList = new ArrayList<>();
  private List<String> wheelResultList = new ArrayList<>();
  private List<String> wheelLevelList = new ArrayList<>();
  private WheelView resultWheelView;
  private String ResultSelected = "";
  private String LevelSelected = "";
  private TextView levelPoptvConfirm;
  private TextView tvWheelLevel;
  private WheelView levelWheelView;
  private PopupWindow levelPop;
  private PopupWindow statusPop;
  List<String> headList = new ArrayList<>();
  List<String> joinList = new ArrayList<>();
  List<String> joinIDList = new ArrayList<>();
  List<String> headIdList = new ArrayList<>();
  private List<IssueJoinerResult.DataBean> projectRoleList = new ArrayList<>();
  private List<ProjectUser.UsersBean> projectMemberList = new ArrayList<>();
  private ProblemPopWindowHeadAdapter problemPopWindowHeadAdapter;
  private ProblemPopWindowJoinAdapter problemPopWindowJoinAdapter;
  private GridView joinOrHead;
  private ProblemHeadAdapter problemHeadAdapter;
  private ProblemJoinAdapter problemJoinAdapter;
  private TextView resultrPoptvConfirm;
  private TextView tvWheelStatus;
  private String organizeId;
  private String examineId;
  private int checkType;
  private final static String videoType = "video";
  private final static String imgType = "img";
  private boolean isSelectedPhoto;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_check_back);
    ButterKnife.bind(this);
    initView();
    loadData();
  }

  @Override protected void loadData() {

    if (getIntent() != null) {
      examineId = getIntent().getStringExtra(CustomParam.Examineid);
      checkType = getIntent().getIntExtra(CustomParam.CheckType, 0);

      //布局切换

      initCheck(checkType);
      //获取数据库
      GetProjectDao();
      //获取项目所有人
      getProjectUsers();
    }
  }

  private void initCheck(int checkType) {

    switch (checkType) {

      case 0:
        //检查
        tvTitle.setText("检查");
        btnConfirm.setText("检查");
        break;
      case 1:
        //整改
        tvTitle.setText("申请复检");
        btnConfirm.setText("申请复检");
        linCC.setVisibility(View.GONE);
        break;
      case 2:
        //验收
        linCC.setVisibility(View.GONE);
        linCheckYanShou.setVisibility(View.VISIBLE);
        tvTitle.setText("验收");
        btnConfirm.setText("验收");
        break;
    }
  }

  @Override protected void initView() {

    wheelResultList.add("复检");
    wheelResultList.add("合格");

    //wheelLevelList.add("轻微");
    wheelLevelList.add("一般");
    wheelLevelList.add("严重");
    wheelLevelList.add("非常严重");

    checkController = new CheckController();
    linSelectPhoto.setVisibility(View.INVISIBLE);
    tvTitle.setText("检查");
    tvRight.setVisibility(View.GONE);
    banner.setOnBannerListener(this);
    banner.setOnPageChangeListener(this);

    //初始化负责人适配器
    problemHeadAdapter = new ProblemHeadAdapter(headList, this, true);
    gridHead.setAdapter(problemHeadAdapter);
    gridHead.setOnItemClickListener(new AdapterView.OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        showHeadPop(DeepCopyUtils.deepCopy(headList), DeepCopyUtils.deepCopy(headIdList));
      }
    });
    //初始化参与人适配器
    problemJoinAdapter = new ProblemJoinAdapter(joinList, this, true);
    gridJoin.setAdapter(problemJoinAdapter);
    gridJoin.setOnItemClickListener(new AdapterView.OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {

        showJoinPop(DeepCopyUtils.deepCopy(joinList), DeepCopyUtils.deepCopy(joinIDList));
      }
    });
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {
  }

  @Override protected void initRefresh() {

  }

  /**
   * 参加人popwindow
   */
  private void showJoinPop(List<String> copyJoinList, List<String> copyJoinIdList) {
    View view = LayoutInflater.from(CheckActivityBack.this)
        .inflate(R.layout.popwindow_issue_joiner, null);
    PopupWindow mPeoplePop = new PopupWindow(CheckActivityBack.this);
    mPeoplePop.setContentView(view);
    mPeoplePop.setFocusable(true);
    mPeoplePop.setOutsideTouchable(true);
    mPeoplePop.setBackgroundDrawable(new BitmapDrawable());
    joinOrHead = view.findViewById(R.id.grid_joinOrhead);
    problemPopWindowJoinAdapter = new ProblemPopWindowJoinAdapter(copyJoinList, this);
    joinOrHead.setAdapter(problemPopWindowJoinAdapter);
    joinOrHead.setOnItemClickListener(new AdapterView.OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        copyJoinList.remove(i);
        copyJoinIdList.remove(i);
        problemPopWindowJoinAdapter.notifyDataSetChanged();
      }
    });
    RecyclerView rvJoiner = view.findViewById(R.id.rv_member);
    TextView tv_select_all = view.findViewById(R.id.tv_select_all);
    TextView tv_confirm = view.findViewById(R.id.tv_confirm);
    TextView tv_title = view.findViewById(R.id.tv_select_title);
    tv_title.setText("请选择验收人");
    setBackgroundAlpha(0.5f);

    //成员列表

    rvJoiner.setLayoutManager(new LinearLayoutManager(this));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvJoiner.addItemDecoration(
        new DividerItemDecoration((CheckActivityBack.this), DividerItemDecoration.VERTICAL));
    //动画
    rvJoiner.setItemAnimator(new DefaultItemAnimator());

    CheckJoinerAdapter joinerAdapter = new CheckJoinerAdapter(this, projectRoleList);

    joinerAdapter.addRecycleItemListener(new CheckJoinerAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        IssueJoinerResult.DataBean usersBean = (IssueJoinerResult.DataBean) o;
        //弹出负责人pop
        copyJoinList.add(usersBean.getRealName());
        copyJoinIdList.add(usersBean.getUserId());
        removeStringListDupli(copyJoinList);
        removeStringListDupli(copyJoinIdList);
        problemPopWindowJoinAdapter.notifyDataSetChanged();
      }
    });
    rvJoiner.setAdapter(joinerAdapter);

    mPeoplePop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
    mPeoplePop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
    tv_select_all.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {

        for (ProjectUser.UsersBean name : projectMemberList) {

          copyJoinList.add(name.getRealName());
          copyJoinIdList.add(name.getUserId());
          removeStringListDupli(copyJoinList);
          removeStringListDupli(copyJoinIdList);
        }

        problemPopWindowJoinAdapter.notifyDataSetChanged();
      }
    });

    tv_confirm.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        joinList.clear();
        joinIDList.clear();
        joinList.addAll(copyJoinList);
        joinIDList.addAll(copyJoinIdList);
        problemJoinAdapter.notifyDataSetChanged();
        if (joinList.isEmpty()) {
          tvSelectYsr.setVisibility(View.VISIBLE);
        } else {
          tvSelectYsr.setVisibility(View.GONE);
        }
        mPeoplePop.dismiss();
      }
    });
    mPeoplePop.setOnDismissListener(new PopupWindow.OnDismissListener() {
      @Override
      public void onDismiss() {
        setBackgroundAlpha(1.0f);
      }
    });
    mPeoplePop.setAnimationStyle(R.style.picker_view_slide_anim);
    mPeoplePop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
  }

  /**
   * 负责人popwindow
   */
  private void showHeadPop(List<String> copyHeadList, List<String> copyHeadIdList) {
    View view = LayoutInflater.from(CheckActivityBack.this)
        .inflate(R.layout.popwindow_issue_joiner, null);
    PopupWindow mPeoplePop = new PopupWindow(CheckActivityBack.this);
    mPeoplePop.setContentView(view);
    mPeoplePop.setFocusable(true);
    mPeoplePop.setOutsideTouchable(true);
    mPeoplePop.setBackgroundDrawable(new BitmapDrawable());
    joinOrHead = view.findViewById(R.id.grid_joinOrhead);
    problemPopWindowHeadAdapter = new ProblemPopWindowHeadAdapter(copyHeadList, this);
    joinOrHead.setAdapter(problemPopWindowHeadAdapter);
    joinOrHead.setOnItemClickListener(new AdapterView.OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        copyHeadList.remove(i);
        copyHeadIdList.remove(i);
        problemPopWindowHeadAdapter.notifyDataSetChanged();
      }
    });
    RecyclerView rvJoiner = view.findViewById(R.id.rv_member);
    TextView tv_select_all = view.findViewById(R.id.tv_select_all);
    TextView tv_confirm = view.findViewById(R.id.tv_confirm);
    TextView tv_title = view.findViewById(R.id.tv_select_title);
    tv_title.setText("请选择整改人");
    setBackgroundAlpha(0.5f);

    //成员列表

    rvJoiner.setLayoutManager(new LinearLayoutManager(this));
    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    rvJoiner.addItemDecoration(
        new DividerItemDecoration((CheckActivityBack.this), DividerItemDecoration.VERTICAL));
    //动画
    rvJoiner.setItemAnimator(new DefaultItemAnimator());

    CheckHeadAdapter haeadAdapter = new CheckHeadAdapter(this, projectRoleList);

    haeadAdapter.addRecycleItemListener(new CheckHeadAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        IssueJoinerResult.DataBean usersBean = (IssueJoinerResult.DataBean) o;
        //弹出负责人pop
        copyHeadList.add(usersBean.getRealName());
        copyHeadIdList.add(usersBean.getUserId());
        removeStringListDupli(copyHeadList);
        removeStringListDupli(copyHeadIdList);
        problemPopWindowHeadAdapter.notifyDataSetChanged();
      }
    });
    rvJoiner.setAdapter(haeadAdapter);

    mPeoplePop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
    mPeoplePop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
    tv_select_all.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {

        for (ProjectUser.UsersBean name : projectMemberList) {

          copyHeadList.add(name.getRealName());
          copyHeadIdList.add(name.getUserId());
          removeStringListDupli(copyHeadList);
          removeStringListDupli(copyHeadIdList);
        }

        problemPopWindowHeadAdapter.notifyDataSetChanged();
      }
    });

    tv_confirm.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        headList.clear();
        headIdList.clear();
        headList.addAll(copyHeadList);
        headIdList.addAll(copyHeadIdList);
        problemHeadAdapter.notifyDataSetChanged();
        if (headIdList.isEmpty()) {
          tvSelectZgr.setVisibility(View.VISIBLE);
        } else {
          tvSelectZgr.setVisibility(View.GONE);
        }
        mPeoplePop.dismiss();
      }
    });
    mPeoplePop.setOnDismissListener(new PopupWindow.OnDismissListener() {
      @Override
      public void onDismiss() {
        setBackgroundAlpha(1.0f);
      }
    });
    mPeoplePop.setAnimationStyle(R.style.picker_view_slide_anim);
    mPeoplePop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
  }

  /**
   * 去除重复数据
   */
  public List<String> removeStringListDupli(List<String> stringList) {
    Set<String> set = new LinkedHashSet<>();
    set.addAll(stringList);

    stringList.clear();

    stringList.addAll(set);
    return stringList;
  }

  /**
   * 获取项目所有人员
   */
  private void getProjectUsers() {

    HashMap<String, String> params = new HashMap<>();
    params.put(CustomParam.Token, Hawk.get(CustomParam.Token));
    params.put("ProjectID", organizeId);
    params.put("RoleId", "-1");
    params.put("encodedKeyWord", "");
    checkController.getCheckJoiner(params, new CallBack<IssueJoinerResult>() {

      @Override public void onSuccess(IssueJoinerResult issueJoinerResult) {
        projectRoleList.clear();
        for (IssueJoinerResult.DataBean dataBean : issueJoinerResult.getData()) {
          projectRoleList.add(dataBean);
        }
        problemJoinAdapter.notifyDataSetChanged();
        problemHeadAdapter.notifyDataSetChanged();
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }

  @OnClick({
      R.id.lin_back,
      R.id.lin_select_head, R.id.lin_select_join,
      R.id.lin_check_result,
      R.id.iv_upload, R.id.iv_again_upload_photo,
      R.id.iv_delete_photo, R.id.edt_title, R.id.lin_check_levle, R.id.fra_commit, R.id.btn_confirm,
      R.id.lin_check_yanshou, R.id.iv_delete_video, R.id.iv_video
  }) public void onViewClicked(View view) {

    Intent intent = null;
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.iv_again_upload_photo:
        getPhoto();
        break;
      case R.id.iv_video:
        if (!photoFileList.isEmpty()) {
          String videoUrl = photoFileList.get(0).getAbsolutePath();
          intent = new Intent(this, FullScreenActivity.class);
          intent.putExtra(CustomParam.VIDEOURL, videoUrl);
          startActivity(intent);
        }
        break;
      case R.id.iv_delete_photo:

        bannerList.remove(selectedPosition);

        photoFileList.remove(selectedPosition);

        if (bannerList.size() == 0 || bannerList.isEmpty()) {

          linSelectPhoto.setVisibility(View.INVISIBLE);

          banner.setVisibility(View.INVISIBLE);

          return;
        }

        banner.setImages(bannerList).start();

        break;
      case R.id.iv_delete_video:
        rlVideo.setVisibility(View.GONE);
        break;

      case R.id.lin_check_levle:
        showLevelPop();
        break;
      case R.id.lin_check_yanshou:
        //验收情况

        showCheckResultPop(true);
        break;
      case R.id.lin_select_head:
        showHeadPop(DeepCopyUtils.deepCopy(headList), DeepCopyUtils.deepCopy(headIdList));
        break;
      case R.id.lin_select_join:
        showJoinPop(DeepCopyUtils.deepCopy(joinList), DeepCopyUtils.deepCopy(joinIDList));
        break;
      case R.id.lin_select_time:
        //pvTime.show();
        break;
      case R.id.lin_select_check_people:
        break;
      case R.id.lin_check_result:
        showCheckResultPop(false);
        break;
      case R.id.iv_record:
        break;
      case R.id.fra_commit:
        //标题
        if (TextUtils.isEmpty(titleResult)) {
          showMsg("标题不能为空");
          return;
        }
        /*if (TextUtils.isEmpty(danWeiStr)) {
          showMsg("请选择单位工程");
          return;
        }*/
        if (TextUtils.isEmpty(StringUtils.dataToString(headList))) {
          showMsg("请选择整改人");
          return;
        }
        if (TextUtils.isEmpty(ResultSelected)) {
          showMsg("请选择检查结果");
          return;
        }

        if (photoFileList.isEmpty()) {
          showMsg("请选择图片");
          return;
        }
        UploadPhoto();

        break;
      case R.id.iv_upload:
        getPhoto();
        break;
      case R.id.edt_title:

        if (!TextUtils.isEmpty(titleResult)) {
          Bundle bundle = new Bundle();
          bundle.putString(CustomParam.TitleStr, titleResult);
          intent = new Intent(this, EdtTitleActivity.class);
          intent.putExtras(bundle);
          startActivityForResult(intent, code_title);
        } else {
          startActivityForResult(new Intent(CheckActivityBack.this, EdtTitleActivity.class),
              code_title);
        }
        break;
      case R.id.tv_play_media:
        //playMedia();
        break;
      case R.id.btn_confirm:

        switch (checkType) {
          case 0:
            //检查
            if (TextUtils.isEmpty(titleResult)) {

              showMsg("检查标题不能为空");
              return;
            } else if (TextUtils.isEmpty(ResultSelected)) {

              showMsg("状态不能为空");
              return;
            } else if (headIdList.isEmpty()) {

              showMsg("整改人不能为空");
              return;
            }

            checkData();
            break;
          case 1:
            // 整改 /  复检
            if (TextUtils.isEmpty(titleResult)) {

              showMsg("检查标题不能为空");
              return;
            }
            applyCheckData();
            break;
          case 2:
            //验收
            if (TextUtils.isEmpty(titleResult)) {

              showMsg("检查标题不能为空");
              return;
            } else if (TextUtils.isEmpty(ResultSelected)) {

              showMsg("状态不能为空");
              return;
            }
            acceptCheckData();
            break;
          default:
            break;
        }

        break;
      default:
        break;
    }
  }

  /**
   * 选取照片
   */
  private void getPhoto() {

    Set<MimeType> set = new HashSet();
    if (isSelectedPhoto) {
      set.add(MimeType.JPEG);
      set.add(MimeType.PNG);
    } else {
      set.addAll(MimeType.ofAll());
    }
    Matisse.from(this)
        .choose(set)
        .countable(true)
        .capture(true)
        .captureStrategy(
            new CaptureStrategy(true, getPackageName()))
        .maxSelectable(9)
        .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
        .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
        .thumbnailScale(0.85f)
        .imageEngine(new GlideEngine())
        .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
        .forResult(code_photo);
  }

  /**
   * 获取文件类型
   */
  private String getFileType(String filePath) {

    if (filePath.contains(".")) {

      String type = filePath.substring(filePath.lastIndexOf("."));

      if (type.equalsIgnoreCase(".mp4") || type.equalsIgnoreCase(".avi")) {

        //选取视频
        return videoType;
      } else if (type.equalsIgnoreCase(".jpg")
          || type.equalsIgnoreCase(".jpeg")
          || type.equalsIgnoreCase(".png")) {

        //选取图片
        return imgType;
      }
    }

    return "";
  }

  @Override protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode != RESULT_OK) {
      return;
    }
    switch (requestCode) {

      case code_title:
        titleResult = data.getExtras()
            .getString(CustomParam.StartForResult);
        edtTitle.setText(titleResult);
        edtTitle.setTextColor(getResources().getColor(R.color.black));
        break;

      case code_photo:

        for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {

          File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));

          switch (getFileType(fileOrigin.getName())) {

            case videoType:
              Glide
                  .with(this)
                  .load(Uri.fromFile(fileOrigin))
                  .into(ivVideo);
              photoFileList.add(fileOrigin);
              isSelectedPhoto = false;
              rlVideo.setVisibility(View.VISIBLE);
              break;
            case imgType:
              File fileCompress = CompressHelper.getDefault(getApplicationContext())
                  .compressToFile(fileOrigin);
              photoFileList.add(fileCompress);
              bannerList.add(fileCompress.getAbsolutePath());
              isSelectedPhoto = true;
              break;

            default:
              break;
          }
        }

        if (isSelectedPhoto) {
          if (!bannerList.isEmpty()) {

            banner.setVisibility(View.VISIBLE);

            linSelectPhoto.setVisibility(View.VISIBLE);
          }

          banner.setImages(bannerList)
              .setImageLoader(new GlideImageLoader())
              .setIndicatorGravity(BannerConfig.CENTER)
              .isAutoPlay(false)
              .start();
        }
        break;
      case code_ps:

        break;
      default:

        break;
    }
  }

  @Override public void OnBannerClick(int position) {

  }

  @Override
  public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

  }

  @Override public void onPageSelected(int position) {
    selectedPosition = position;
  }

  @Override public void onPageScrollStateChanged(int state) {

  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      String projectID = bean.getProjectID();
      organizeId = bean.getBimProjectId();
    }
  }

  /**
   * 初始化严重等级
   */
  private void initLevelWheelView() {
    LevelSelected = wheelLevelList.get(0);
    levelWheelView.setOnWheelViewListener(new WheelView.OnWheelViewListener() {
      @Override
      public void onSelected(int selectedIndex, String item) {
        LevelSelected = item;
      }
    });

    levelPoptvConfirm.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        tvWheelLevel.setText(LevelSelected);
        tvCheckLevel.setText(LevelSelected);
        tvCheckLevel.setTextColor(getResources().getColor(R.color.black));
        levelPop.dismiss();
      }
    });
  }

  /**
   * 初始化状态数据
   */
  private void initResultWheelView() {

    ResultSelected = wheelResultList.get(0);
    resultWheelView.setOnWheelViewListener(new WheelView.OnWheelViewListener() {
      @Override
      public void onSelected(int selectedIndex, String item) {
        ResultSelected = item;
      }
    });

    resultrPoptvConfirm.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        tvWheelStatus.setText(ResultSelected);
        tvCheckResult.setText(ResultSelected);
        tvCheckResult.setTextColor(getResources().getColor(R.color.black));
        tvCheckYanShou.setText(ResultSelected);
        tvCheckYanShou.setTextColor(getResources().getColor(R.color.black));
        statusPop.dismiss();
      }
    });
  }

  /**
   * 选择状态pop
   */
  private void showCheckResultPop(boolean isYanShou) {

    View view;
    if (isYanShou) {

      view = LayoutInflater.from(CheckActivityBack.this)
          .inflate(R.layout.popwindow_check_yanshou, null);
    } else {

      view = LayoutInflater.from(CheckActivityBack.this)
          .inflate(R.layout.popwindow_check_status, null);
    }
    statusPop = new PopupWindow(CheckActivityBack.this);

    statusPop.setContentView(view);
    statusPop.setFocusable(true);
    statusPop.setOutsideTouchable(true);
    statusPop.setBackgroundDrawable(new BitmapDrawable());
    statusPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
    statusPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
    setBackgroundAlpha(0.5f);
    resultWheelView = view.findViewById(R.id.result_wheel_view);
    resultWheelView.setOffset(1);
    resultrPoptvConfirm = view.findViewById(R.id.tv_confirm);
    tvWheelStatus = view.findViewById(R.id.tvSelecteResult);
    statusPop.setAnimationStyle(R.style.picker_view_slide_anim);
    statusPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
      @Override
      public void onDismiss() {
        setBackgroundAlpha(1.0f);
      }
    });

    initResultWheelView();
    resultWheelView.setItems(wheelResultList);
    statusPop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
  }

  /**
   * 严重等级
   */
  private void showLevelPop() {

    View view = LayoutInflater.from(CheckActivityBack.this)
        .inflate(R.layout.popwindow_new_check_level, null);
    levelPop = new PopupWindow(CheckActivityBack.this);
    levelPop.setContentView(view);
    levelPop.setFocusable(true);
    levelPop.setOutsideTouchable(true);
    levelPop.setBackgroundDrawable(new BitmapDrawable());
    levelPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
    levelPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
    setBackgroundAlpha(0.5f);
    levelWheelView = view.findViewById(R.id.level_wheel_view);
    levelWheelView.setOffset(1);
    tvWheelLevel = view.findViewById(R.id.tv_check_Level);
    levelPoptvConfirm = view.findViewById(R.id.tv_confirm);
    levelPop.setAnimationStyle(R.style.picker_view_slide_anim);
    levelPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
      @Override
      public void onDismiss() {
        setBackgroundAlpha(1.0f);
      }
    });
    initLevelWheelView();
    levelWheelView.setItems(wheelLevelList);
    levelPop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
  }

  /**
   * 设置popwindow默认背景变灰
   */
  public void setBackgroundAlpha(float bgAlpha) {
    WindowManager.LayoutParams lp = getWindow().getAttributes();
    lp.alpha = bgAlpha;
    getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
    getWindow().setAttributes(lp);
  }

  /**
   * 上传图片
   */
  private void UploadPhoto() {

    HashMap<String, String> params = new HashMap<>();
    params.put(CustomParam.Token, Hawk.get(CustomParam.Token));
    checkController.uploadCheckPhoto(photoFileList, params, new CallBack<CheckUploadPhotoResult>() {
      @Override public void onSuccess(CheckUploadPhotoResult issueUploadPhotoResult) {
        if (issueUploadPhotoResult.getRet() == 1) {
          StringBuilder stringBuilder = new StringBuilder();
          for (CheckUploadPhotoResult.DataBean dataBean : issueUploadPhotoResult.getData()) {
            stringBuilder.append(dataBean.getBf_guid());
            stringBuilder.append(",");
          }
          uploadFileId = stringBuilder.toString();
          //
        }
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 对PC创建的任务进行检查操作
   * 【ExamineID】:哪个检查任务,
   * 【Token】:口令，
   * 【RectificationRemark】:填写的检查的标题,
   * 【aede_severitylevel】:一般，严重，非常严重,
   * 【IsPassed】:1为合格，否则为不合格，
   * 【RelationMemberID】:指定的整改人，英文逗号分隔userId不可为空,
   * 【PrincipalID】 指定的验收人（英文逗号分隔UserId，为空时，使用Token代表的人）
   * 【OrganizeId】项目ID
   * 【文件】
   */
  private void checkData() {
    mLoading.show();
    HashMap p = new HashMap<String, String>();
    p.put("ExamineID", examineId);
    p.put("Token", Hawk.get(CustomParam.Token));
    p.put("RectificationRemark", titleResult);
    p.put("aede_severitylevel", LevelSelected);
    String IsPassed;
    if (ResultSelected.equals("合格")) {
      IsPassed = "1";
    } else {
      IsPassed = "0";
    }
    p.put("IsPassed", IsPassed);
    p.put("RelationMemberID", StringUtils.dataToString(headIdList));
    p.put("PrincipalID", StringUtils.dataToString(joinIDList));
    p.put("OrganizeId", organizeId);
    checkController.check(photoFileList, p, new CallBack<CheckResult>() {
      @Override public void onSuccess(CheckResult checkResult) {
        if (checkResult.getRet() == 1) {
          mLoading.dismiss();
          finish();
        }
      }

      @Override public void onFail(String erroMsg) {
        mLoading.dismiss();
      }
    });
  }

  /***
   *整改 复检
   */
  private void applyCheckData() {
    mLoading.show();
    HashMap p = new HashMap<String, String>();
    p.put("ExamineID", examineId);
    p.put("Token", Hawk.get(CustomParam.Token));
    p.put("RectificationRemark", titleResult);
    p.put("OrganizeId", organizeId);
    checkController.applyCheck(photoFileList, p, new CallBack<CheckResult>() {
      @Override public void onSuccess(CheckResult checkResult) {
        if (checkResult.getRet() == 1) {
          mLoading.dismiss();
          finish();
        }
      }

      @Override public void onFail(String erroMsg) {
        mLoading.dismiss();
      }
    });
  }

  /***
   * 验收操作
   */
  private void acceptCheckData() {
    mLoading.show();
    HashMap p = new HashMap<String, String>();
    p.put("ExamineID", examineId);
    p.put("Token", Hawk.get(CustomParam.Token));
    p.put("RectificationRemark", titleResult);

    String IsPassed;
    if (ResultSelected.equals("合格")) {
      IsPassed = "1";
    } else {
      IsPassed = "0";
    }
    p.put("IsPassed", IsPassed);
    p.put("OrganizeId", organizeId);
    checkController.acceptCheck(photoFileList, p, new CallBack<CheckResult>() {
      @Override public void onSuccess(CheckResult checkResult) {
        if (checkResult.getRet() == 1) {
          mLoading.dismiss();
          finish();
        }
      }

      @Override public void onFail(String erroMsg) {
        mLoading.dismiss();
      }
    });
  }

}
