package com.probim.bimenew.activity;

import android.os.Bundle;
import androidx.annotation.Nullable;

import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.R;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;
import com.probim.bimenew.utils.web.X5WebView;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/10/9:53.
 */
public class TestWebActivity extends BaseActivity {

  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.web_x)
  X5WebView webX;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_test_web);
    ButterKnife.bind(this);
    StatusBarUtils.transparencyBar(this);
    initView();
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {

    webX.loadUrl(
        "http://www.probim.cn:9999/Addons/Module_Form/#/zh-CN/?wf_guid=301dd5e1-71a0-4218-a93d-c25d612f1268");
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick(R.id.lin_back)
  public void onViewClicked() {
    finish();
  }
}
