package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.FenBuProjectAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.model.NewCheckConfig;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :分步--焚香--检验批
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/16:42.
 */

public class FenBuProjectActivity extends BaseActivity {
  @BindView(R.id.iv_back) ImageView ivBack;
  @BindView(R.id.tv_left) TextView tvLeft;
  @BindView(R.id.lin_back) LinearLayout linBack;
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.tv_right) TextView tvRight;
  @BindView(R.id.img_right) ImageView imgRight;
  @BindView(R.id.rv_step) RecyclerView rvStep;
  @BindView(R.id.iv_status) ImageView ivStatus;
  @BindView(R.id.tv_status) TextView tvStatus;
  @BindView(R.id.rv_status_layout) RelativeLayout rvStatusLayout;
  private List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX.ItemsBeanXX>
      list = new ArrayList<>();
  private FenBuProjectAdapter adapter;
  private List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX>
      unitProjectList;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_step_step);
    ButterKnife.bind(this);
    initRecycleview();
    initView();
  }

  @Override protected void loadData() {

  }

  @Override protected void initView() {
    tvTitle.setText("分部工程");

    if (getIntent() != null) {
      Bundle bundle = getIntent().getExtras();

      ArrayList list1 = bundle.getParcelableArrayList(CustomParam.ConfigSerializable);

      if (list1==null) {
        //数据为空 新建检查
        List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX>
            unitProjectList = Hawk.get(CustomParam.DanWeiSelected);
        for (int i = 0; i < unitProjectList.size(); i++) {
          if (unitProjectList.get(i).getTitle().equalsIgnoreCase("分部工程")) {
            list.addAll(unitProjectList.get(i).getItems());
          }
        }
      } else {
        //数据不为空  检查详情
        List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX>
            unitProjectList =
            (List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX>) list1
                .get(0);

        for (int i = 0; i < unitProjectList.size(); i++) {
          if (unitProjectList.get(i).getTitle().equalsIgnoreCase("分部工程")) {
            list.addAll(unitProjectList.get(i).getItems());
          }
        }
      }
    }

    adapter.notifyDataSetChanged();
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

    rvStep.setLayoutManager(new LinearLayoutManager(this));

    DividerItemDecoration itemDecoration =
        new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
    rvStep.addItemDecoration(itemDecoration);

    rvStep.setItemAnimator(new DefaultItemAnimator());

    adapter = new FenBuProjectAdapter(this, list);

    adapter.addRecycleItemListener(new FenBuProjectAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {

        Hawk.put(CustomParam.FenBuSelected, list.get(pos).getName());
        Hawk.put(CustomParam.FenBuIdSelected, list.get(pos).getValue());
        Bundle bundle = new Bundle();
        Intent intent = new Intent(FenBuProjectActivity.this, FenXiangProjectActivity.class);
        intent.putExtra(CustomParam.FenBuSelected, (Serializable) list.get(pos).getChildren());
        intent.putExtras(bundle);
        startActivity(intent);
        finish();
      }
    });
    rvStep.setAdapter(adapter);
  }

  @Override protected void initRefresh() {

  }

  @OnClick({ R.id.lin_back, R.id.tv_right }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        break;
    }
  }
}
