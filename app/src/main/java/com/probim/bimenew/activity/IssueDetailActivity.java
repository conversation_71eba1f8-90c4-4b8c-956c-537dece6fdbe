package com.probim.bimenew.activity;

import android.Manifest;
import android.app.AlertDialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup.LayoutParams;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.PopupWindow.OnDismissListener;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.BuildConfig;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.IssueJoinerAdapter;
import com.probim.bimenew.adapter.IssueTagsAdapter;
import com.probim.bimenew.adapter.Photo3Adapter;
import com.probim.bimenew.adapter.ProblemHeadAdapter;
import com.probim.bimenew.adapter.ProblemJoinAdapter;
import com.probim.bimenew.adapter.ProblemMemberRvAdapter;
import com.probim.bimenew.adapter.ProblemPopWindowHeadAdapter;
import com.probim.bimenew.adapter.ProblemPopWindowJoinAdapter;
import com.probim.bimenew.adapter.ProblemRoleRvAdapter;
import com.probim.bimenew.adapter.RelevanceDocAdapter;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.BaseDto;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.file.DocViewActivity;
import com.probim.bimenew.file.MyMimeMap;
import com.probim.bimenew.model.DocFileDto;
import com.probim.bimenew.model.DocRelevance;
import com.probim.bimenew.model.DrawingsModel;
import com.probim.bimenew.model.ModelPointViewDetails;
import com.probim.bimenew.model.PointViewDescModel;
import com.probim.bimenew.model.ProblemStatusModel;
import com.probim.bimenew.model.ProjectUser.UsersBean;
import com.probim.bimenew.model.RelevanceDto;
import com.probim.bimenew.model.RelevanceDto.RelationsBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.BaseResultDto;
import com.probim.bimenew.result.DeleteIssuePhotoResult;
import com.probim.bimenew.result.IssueDetailResult;
import com.probim.bimenew.result.IssueJoinerResult;
import com.probim.bimenew.result.IssueTagsResult;
import com.probim.bimenew.result.IssueTypeDto;
import com.probim.bimenew.result.IssueUploadPhotoResult;
import com.probim.bimenew.result.UpdateIssueDateResult;
import com.probim.bimenew.result.UpdateIssuePeopleResult;
import com.probim.bimenew.result.UpdateIssueTypeResult;
import com.probim.bimenew.utils.Base64Utils;
import com.probim.bimenew.utils.DataHolder;
import com.probim.bimenew.utils.DownloadUtil;
import com.probim.bimenew.utils.FileUtils;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.PermissionUtils;
import com.probim.bimenew.utils.StringUtils;
import com.probim.bimenew.utils.UIUtils;
import com.probim.bimenew.utils.WheelView;
import com.probim.bimenew.utils.WheelView.OnWheelViewListener;
import com.probim.bimenew.utils.dropdownmenu.bean.DropdownItemObject;
import com.probim.bimenew.utils.popwindow.QuestionPopWindow;
import com.probim.bimenew.utils.thread.ThreadUtils;
import com.probim.bimenew.utils.view.MyGridView;
import com.tencent.smtt.sdk.TbsReaderView;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.UUID;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Description :问题详情页面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/10/15:36.
 */
public class IssueDetailActivity extends BaseActivity implements TbsReaderView.ReaderCallback {

    public static boolean isFromWhere = false;
    private final int code_title = 1000;
    private final int code_intro = 2000;
    private final int code_photo = 3000;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_right)
    TextView tvRight;
    @BindView(R.id.tv_problem_title)
    TextView tvProblemTitle;
    @BindView(R.id.tv_problem_intro)
    TextView tvProblemIntro;
    List<PhotoDto> photoDtoList = new ArrayList<>();
    List<PhotoDto> viewPointOrNtationphotoList = new ArrayList<>();
    List<String> headList = new ArrayList<>();
    List<String> joinList = new ArrayList<>();
    List<String> joinIDList = new ArrayList<>();
    List<String> headIdList = new ArrayList<>();
    @BindView(R.id.grid_head)
    MyGridView gridHead;
    @BindView(R.id.grid_join)
    MyGridView gridJoin;
    @BindView(R.id.tv_date)
    TextView tvDate;
    @BindView(R.id.tv_tv_photo_size)
    TextView tvTvPhotoSize;
    @BindView(R.id.lin_all)
    LinearLayout linAll;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.tv_type)
    TextView tvType;
    @BindView(R.id.img_view)
    ImageView imgView;
    @BindView(R.id.tv_view)
    TextView tvView;
    @BindView(R.id.tv_view_intro)
    TextView tvViewIntro;
    @BindView(R.id.lin_view)
    LinearLayout linView;
    @BindView(R.id.rv_doc)
    RecyclerView rvDoc;
    @BindView(R.id.lin_doc)
    LinearLayout linDoc;
    @BindView(R.id.lin_select_head)
    LinearLayout linSelectHead;
    @BindView(R.id.lin_select_join)
    LinearLayout linSelectJoin;
    @BindView(R.id.lin_select_time)
    LinearLayout linSelectTime;
    @BindView(R.id.tv_add_photo)
    TextView tvAddPhoto;
    @BindView(R.id.tv_doc_about)
    TextView tvDocAbout;
    @BindView(R.id.tv_select_tag)
    TextView tvTag;
    @BindView(R.id.rv_photo)
    RecyclerView rvPhoto;
    @BindView(R.id.rl_select_viewpoint)
    RelativeLayout rlSelectViewpoint;
    @BindView(R.id.tv_drawings_more)
    TextView tvDrawingsMore;
    @BindView(R.id.img_drawings)
    ImageView imgDrawings;
    @BindView(R.id.tv_drawings_name)
    TextView tvDrawingsName;
    @BindView(R.id.tv_open_drawings)
    TextView tvOpenDrawings;
    @BindView(R.id.rl_select_drawings)
    RelativeLayout rlSelectDrawings;
    @BindView(R.id.tv_photo_more)
    TextView tvPhotoMore;
    @BindView(R.id.tv_view_more)
    TextView tvViewMore;
    @BindView(R.id.line_photo)
    View linePhoto;
    @BindView(R.id.tv_photo_title)
    TextView tvPhotoTitle;
    @BindView(R.id.tv_view_point)
    TextView tvViewPoint;


    private Photo3Adapter photoAdapter;
    private ProblemHeadAdapter problemHeadAdapter;
    private ProblemJoinAdapter problemJoinAdapter;
    private TimePickerView pvTime;
    private ProblemPopWindowHeadAdapter problemPopWindowHeadAdapter;
    private ProblemPopWindowJoinAdapter problemPopWindowJoinAdapter;
    private GridView joinOrHead;
    private TextView tv_status;
    private WheelView statusWheelView;
    private WheelView typeWheelView;
    private TextView tvConfirm;
    private String StatusSelected = "";
    private String TypeSelected = "";
    private String TypeIdSelected = "";
    private String StatusIdSelected = "";
    private TextView tv_title_select;
    private ArrayList<String> wheelStatusList = new ArrayList<>();
    private List<String> wheelStatusIDList = new ArrayList<>();
    private List<String> wheelTypeIDList = new ArrayList<>();
    private ArrayList<String> wheelTypeList = new ArrayList<>();
    private TextView tv_type;
    private View line_status;
    private View line_type;
    private String organizeId;
    private RecyclerView role_rv;
    private RecyclerView member_rv;
    private List<IssueJoinerResult.DataBean> projectRoleList = new ArrayList<>();
    private List<IssueTagsResult.DataBean> issueTagsList = new ArrayList<>();
    private List<UsersBean> projectMemberList = new ArrayList<>();
    private List<IssueDetailResult.DataBean.RelationFilesBean> docRelevanceList = new ArrayList<>();
    private ProblemRoleRvAdapter problemRoleRvAdapter;
    private ProblemMemberRvAdapter problemMemberRvAdapter;
    private boolean isHeadPop = true;
    private boolean isTypeSelected = false;
    private boolean isStatusSelected = false;
    private PopupWindow statusOrtypePop;
    private List<File> photoFileList = new ArrayList<>();
    private IssueController issueController;
    private String bimProjectId;
    private String titleResult;
    private String introResult;
    private String uploadFileId = "";
    private String modifyFileId = "";
    private String issueDate;
    private String viewPointId;
    private String modelId;

    private String projectId;
    private RelevanceDocAdapter relevanceDocAdapter;
    private String userId;
    private boolean isTypePopCanClick = true;
    private String issueId;
    private int roleType;
    private boolean isStatusPopCanClick = true;
    private RelevanceDto relevanceDto;
    private List<String> docTargetIdList;
    private List<String> docRelevanceIdList;
    private List<String> photoRelevanceIdList = new ArrayList<>();
    private IssueDetailResult.DataBean issueDetailResult;
    private boolean isUploadPhoto = false;
    private String statusTextFromNet;
    private String typeTextFromNet;
    private String imgModelId;
    private String imgId;
    private String imgPos;
    private boolean isDrawingSelected;
    private boolean isViewSelected;
    private String[] perms = new String[] {
            Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
    };

    /**
     * 使用自定义方法获得文件的MIME类型
     */
    private static String getMimeTypeFromFile(File file) {
        String type = "*/*";
        String fName = file.getName();
        //获取后缀名前的分隔符"."在fName中的位置。
        int dotIndex = fName.lastIndexOf(".");
        if (dotIndex > 0) {
            //获取文件的后缀名
            String end = fName.substring(dotIndex, fName.length()).toLowerCase(Locale.getDefault());
            //在MIME和文件类型的匹配表中找到对应的MIME类型。
            HashMap<String, String> map = MyMimeMap.getMimeMap();
            if (!TextUtils.isEmpty(end) && map.keySet().contains(end)) {
                type = map.get(end);
            }
        }
        return type;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_issue_detail);
        ButterKnife.bind(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
        issueController = new IssueController();

        //获取数据库数据
        GetProjectDao();
        //获取状态分类数据
        GetFilter();
        //获取问题标签数据
        getIssueTags();
        //初始化数据
        initData();
    }

    /**
     * 获取问题标签
     */
    private void getIssueTags() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", organizeId);
        params.put("token", Hawk.get(CustomParam.Token));
        issueController.getIssueTags(params, new CallBack<IssueTagsResult>() {

            @Override
            public void onSuccess(IssueTagsResult issueTagsResult) {
                issueTagsList.clear();
                for (IssueTagsResult.DataBean dataBean : issueTagsResult.getData()) {
                    issueTagsList.add(dataBean);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void initView() {
        userId = Hawk.get(CustomParam.UserId);
        tvTitle.setText("新建问题");
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("评论");
        tvLeft.setText("取消");
        tvTvPhotoSize.setText("0");
        TimePicker();
        //初始化负责人适配器
   /* problemHeadAdapter = new ProblemHeadAdapter(headList, this);
    gridHead.setAdapter(problemHeadAdapter);*/
        gridHead.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                showHeadPop();
            }
        });
        //初始化参与人适配器
        problemJoinAdapter = new ProblemJoinAdapter(joinList, this, true);
        gridJoin.setAdapter(problemJoinAdapter);
        gridJoin.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                showJoinPop();
            }
        });
        //初始化照片适配器
        //为 RecyclerView 设置布局管理器

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rvPhoto.setLayoutManager(linearLayoutManager);
        //动画
        rvPhoto.setItemAnimator(new DefaultItemAnimator());

        //初始化照片适配器
        photoAdapter = new Photo3Adapter(photoDtoList, this);
        photoAdapter.addItemClickListener(new Photo3Adapter.OnItemClickListener() {
            @Override
            public void OnItemClick(int pos, Object o) {

                if (photoDtoList.size() != 0) {
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
                    bundle.putInt(CustomParam.PhotoPosition, pos);
                    Intent intent = new Intent(IssueDetailActivity.this, PhotoViewActivity.class);
                    intent.putExtras(bundle);
                    startActivity(intent);
                }
            }
        });

        photoAdapter.addItemDeleteListener(new Photo3Adapter.OnItemDeleteListener() {
            @Override
            public void OnItemDelete(int pos, Object o) {

                //photoDtoList.remove(pos);
                //photoFileList.remove(pos);
                //tvTvPhotoSize.setText(photoDtoList.size() + "");
                //photoAdapter.notifyDataSetChanged();
                //if (photoDtoList.size() == 0) {
                //  linePhoto.setVisibility(View.GONE);
                //}

                deleteIssuePhoto(photoDtoList.get(pos).getImgId(), pos);
            }
        });
        rvPhoto.setAdapter(photoAdapter);
        //初始化popwindow中选择人适配器

        //初始化popwindow中选择人适配器

        //为 RecyclerView 设置布局管理器
        rvDoc.setLayoutManager(new LinearLayoutManager(this));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvDoc.addItemDecoration(
                new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        //动画
        rvDoc.setItemAnimator(new DefaultItemAnimator());

        relevanceDocAdapter = new RelevanceDocAdapter(this, docRelevanceList);

        rvDoc.setAdapter(relevanceDocAdapter);

        relevanceDocAdapter.addRecycleItemListener(new RelevanceDocAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                //文档预览
                IssueDetailResult.DataBean.RelationFilesBean bean = (IssueDetailResult.DataBean.RelationFilesBean) o;
                openFile(bean.getFileId(), bean.getFileName());

            }

            @Override
            public void OnDelete(int pos, Object o) {
//                IssueDetailResult.DataBean.RelationFilesBean bean = (IssueDetailResult.DataBean.RelationFilesBean) o;
//                deleteIssuePhoto(bean.getFileId(), pos);
       /* //删除
        String relevancerId = "";
        //循环判断    根据TargetID 获取 RelevanceID 删除相应文件
        for (DocsBean docsBean : problemData.getDocs()) {

          if (docRelevanceList.get(pos).getFileId().equals(docsBean.getTargetID())) {
            relevancerId = docsBean.getRelationID();
          }
        }
        DeleteRelevance(relevancerId, 1, pos);*/
            }
        });
    }

    /**
     * 在线预览文件
     */
    private void openFile(String fileId, String fileName) {
        try {
            String firstUrl = Hawk.get(CustomParam.Doc_URL) + "/api/v1/attach/preview?id=" + fileId;
            String docUrl = BaseApp.getDocUrl() + "/Content/PDFJS/web/viewer.html?file=" + URLEncoder.encode(firstUrl, "UTF-8") + "|" + "";
            Intent intent = new Intent(this, DocViewActivity.class);
            intent.putExtra(CustomParam.DocUrl, docUrl);
            intent.putExtra(CustomParam.DocName, fileName);
            startActivity(intent);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void initData() {
        //初始化popUI
        //    initPopData();
        if (getIntent() != null) {
            issueId = getIntent().getStringExtra(CustomParam.IssueId);
            GetIssueDetail(issueId);
            GetProjectUser();
        }
    }

    /**
     * 初始化选择人 recycleview
     */
    @Override
    protected void initRecycleview() {

    /*//角色列表

    role_rv.setLayoutManager(new LinearLayoutManager(this));

    //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
    role_rv.addItemDecoration(
        new DividerItemDecoration((IssueDetailActivity.this), DividerItemDecoration.VERTICAL));
    //动画
    role_rv.setItemAnimator(new DefaultItemAnimator());

    problemRoleRvAdapter = new ProblemRoleRvAdapter(this, projectRoleList);
    problemRoleRvAdapter.addRecycleItemListener(new OnRecycleItemListener() {
      @Override
      public void OnRecycleItemClick(int pos, Object o) {

        List<UsersBean> usersBeanList = (List<UsersBean>) o;
        projectMemberList.clear();
        projectMemberList.addAll(usersBeanList);
        problemRoleRvAdapter.getIndex(pos);
        problemRoleRvAdapter.notifyDataSetChanged();
        problemMemberRvAdapter.notifyDataSetChanged();
      }
    });
    role_rv.setAdapter(problemRoleRvAdapter);
  */

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void onResume() {
        super.onResume();
        //重复修改问题 添加判断
        if (!TextUtils.isEmpty(MarkActivity.imgName)) {
            rlSelectDrawings.setVisibility(View.VISIBLE);
            tvDrawingsName.setText(MarkActivity.imgName);
            Glide.with(getApplicationContext())
                    .load(Base64Utils.decode(MarkActivity.img_url))
                    .into(imgDrawings);
            imgModelId = MarkActivity.img2d_ModelId;
            imgId = MarkActivity.img2dId;
            imgPos = MarkActivity.img2d_pos;
        }
    }

    @Nullable
    @OnClick({
            R.id.lin_back, R.id.tv_right,
            R.id.lin_status, R.id.lin_select_time, R.id.tv_add_photo, R.id.lin_type, R.id.lin_select_join,
            R.id.lin_select_head, R.id.tv_load_view, R.id.img_view, R.id.tv_view_more,
            R.id.tv_drawings_more, R.id.tv_photo_more, R.id.tv_open_drawings
    })
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_view_more:
                //已选择视点
                if (isViewSelected) {
                    QuestionPopWindow.getInstance().showPopWindwin(IssueDetailActivity.this, true,
                            new QuestionPopWindow.onSelectListener() {
                                @Override
                                public void OnDelete() {
                                    isViewSelected = false;
                                    SelectModelViewActivity.ViewId = "";
                                    ApiConstant.viewPointOrTwoDis = false;
                                    UpdateIssue(roleType, null, null, null, null, null);
                                }

                                @Override
                                public void OnSelect() {
                                    startActivity(SelectModelForNewQuestionActivity.class);
                                    ApiConstant.viewPointOrTwoDis = true;
                                }

                                @Override
                                public void OnCancle() {

                                }
                            });
                } else {
                    //未选择视点
                    ApiConstant.viewPointOrTwoDis = true;
                    startActivity(SelectModelForNewQuestionActivity.class);
                }
                break;
            case R.id.tv_drawings_more:
                ApiConstant.isCanTap = true;
                //选取图纸
                if (isDrawingSelected) {

                    QuestionPopWindow.getInstance().showPopWindwin(IssueDetailActivity.this, false,
                            new QuestionPopWindow.onSelectListener() {
                                @Override
                                public void OnDelete() {
                                    rlSelectDrawings.setVisibility(View.GONE);
                                    //删除图纸
                                    MarkActivity.img2d_pos = "";
                                    MarkActivity.img2dId = "";
                                    MarkActivity.img2d_ModelId = "";
                                    MarkActivity.img_url = "";
                                    MarkActivity.imgName = "";
                                    isDrawingSelected = false;
                                    imgModelId = "";
                                    imgId = "";
                                    imgPos = "";
                                    UpdateIssue(roleType, null, null, null, null, null);
                                }

                                @Override
                                public void OnSelect() {
                                    startActivity(SelectModelForNewQuestionActivity.class);
                                    NewIssueActivity.isFromWhere = false;
                                }

                                @Override
                                public void OnCancle() {

                                }
                            });
                } else {
                    ApiConstant.viewPointOrTwoDis = false;
                    startActivity(SelectModelForNewQuestionActivity.class);
                    NewIssueActivity.isFromWhere = false;
                }

                break;
            case R.id.tv_photo_more:
            case R.id.tv_add_photo:
              requestCameraPermission();
                break;

            case R.id.lin_back:
                finish();
                break;
            case R.id.tv_right:
                //评论
                Bundle bundle = new Bundle();
                bundle.putString(CustomParam.IssueId, issueId);
                bundle.putString(CustomParam.OrganizeId, organizeId);
                Intent intent = new Intent(this, CommentsActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);

                break;

            case R.id.lin_status:
                if (isStatusPopCanClick) {

                    showStatusOrTypePop();

                    if (isTypePopCanClick) {

                        initStatusWheelView();
                    } else {

                        initRoleStatusWheelView();
                    }
                }
                break;
            case R.id.lin_select_time:
                pvTime.show();
                break;
            case R.id.lin_type:
                if (isTypePopCanClick) {
                    showStatusOrTypePop();
                    initTypeWheelView();
                }
                break;
            case R.id.lin_select_head:
                showHeadPop();
                break;
            case R.id.lin_select_join:
                showJoinPop();
                break;
            case R.id.tv_load_view:
                Bundle bundle1 = new Bundle();
                bundle1.putString(CustomParam.ModelId, modelId);
                bundle1.putString(CustomParam.ViewPointId, viewPointId);
                bundle1.putString(CustomParam.TvLeft, "问题");
                Intent intent1 = new Intent(this, LoadModelViewActivity.class);
                intent1.putExtras(bundle1);
//                startActivity(intent1);
                break;
            case R.id.img_view:
                Intent intent3 = new Intent(this, LoadModelViewActivity.class);
                intent3.putExtra(CustomParam.ProjectId, projectId);
                intent3.putExtra(CustomParam.ViewPointId, viewPointId);
                intent3.putExtra(CustomParam.ModelId, modelId);
//                startActivity(intent3);

                //startActivity(NewQuestionPhotoActivity.class);

                //分类加载
       /* if ("视点".equalsIgnoreCase(new_question_type)) {
          Bundle bundle2 = new Bundle();
          bundle2
              .putSerializable(CustomParam.StartPhoto, (Serializable) viewPointOrNtationphotoList);
          Intent intent2 = new Intent(IssueDetailActivity.this,
              PhotoViewForPointViewActivity.class);
          intent2.putExtras(bundle2);
          startActivity(intent2);

        } else {

        }*/
                break;
            case R.id.tv_open_drawings:
                Bundle bundle2 = new Bundle();
                ApiConstant.isCanTap = false;
                //打开图纸
                NewIssueActivity.isFromWhere = true;
                bundle2.putString(CustomParam.TwoDimensId, imgId);
                bundle2.putString(CustomParam.ProjectId, organizeId);
                bundle2.putString(CustomParam.ModelId, imgModelId);
                bundle2.putString(CustomParam.DrawingPosition, imgPos);
                Intent intent2 = new Intent(this, MarkActivity.class);
                intent2.putExtras(bundle2);
                startActivity(intent2);
                break;

            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode != RESULT_OK) {
            return;
        }
        switch (requestCode) {

            case code_photo:

                for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {

                    isUploadPhoto = true;
                    linePhoto.setVisibility(View.VISIBLE);
                    File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
                    File fileCompress = CompressHelper.getDefault(getApplicationContext())
                            .compressToFile(fileOrigin);
                    photoFileList.add(fileCompress);
                }

                //新增图片 上传
                updateIssuePhoto(photoFileList);

                break;
            default:
                break;
        }
    }

    private String getDay(Date date) {//可根据需要自行截取数据显示
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    /**
     * 获取系统当前时间
     */
    private String getSystemDate() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd  HH:mm:ss");
        return format.format(new Date(System.currentTimeMillis()));
    }

    /**
     * 时间选择器
     */
    private void TimePicker() {


        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);

        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                issueDate = getDay(date);
                tvDate.setText(getDay(date));
                //修改截止日期
                updateIssueDate(getDay(date));
            }
        }).setCancelText("清空").setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false}).setContentTextSize(18)
                .setLabel("", "", "", "", "", "")
                .isCenterLabel(false).setLineSpacingMultiplier(3.0f)
                .setDividerColor(R.color.divider_color)
                .setDecorView(null)
                .setTitleText("请选择截止日期").setRangDate(startDate, endDate).setLayoutRes(R.layout.pickerview_custom_time,
                        new CustomListener() {
                            @Override
                            public void customLayout(View v) {

                                TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                                tv_clear.setOnClickListener(new OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        tvDate.setText("请选择日期");
                                        pvTime.dismiss();
                                    }
                                });

                                TextView tvConmit = (TextView) v.findViewById(R.id.tv_finish);
                                tvConmit.setOnClickListener(new OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        pvTime.returnData();
                                        pvTime.dismiss();
                                    }
                                });
                            }
                        })
                .build();
    }

    /**
     * 选取照片
     */
    private void GetPhoto() {
        Set<MimeType> mimeTypeSet = new HashSet<>();
        mimeTypeSet.add(MimeType.JPEG);
        mimeTypeSet.add(MimeType.PNG);
        Matisse.from(this)
                .choose(mimeTypeSet)
                .countable(true)
                .capture(true)
                .captureStrategy(
                        new CaptureStrategy(true, getPackageName()))
                .maxSelectable(9)
                .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
                .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
                .thumbnailScale(0.85f)
                .imageEngine(new GlideEngine())
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                .forResult(code_photo);
    }
    /**
     * 显示权限申请理由对话框
     */
    private void showPermissionRationale(String title, String message, Runnable onConfirm) {
        new AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("确定", (dialog, which) -> onConfirm.run())
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 显示跳转设置页面的对话框
     */
    private void showGoToSettingsDialog(String message) {
        new AlertDialog.Builder(this)
                .setTitle("权限设置")
                .setMessage(message)
                .setPositiveButton("去设置", (dialog, which) -> {
                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    Uri uri = Uri.fromParts("package", getPackageName(), null);
                    intent.setData(uri);
                    startActivity(intent);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 处理权限被拒绝的情况
     */
    private void handlePermissionDenied(String permissionName, boolean neverAskAgain) {
        if (neverAskAgain) {
            showGoToSettingsDialog(permissionName + "被永久拒绝，请在设置中手动开启");
        } else {
            Toast.makeText(this, permissionName + "被拒绝", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 申请相机权限示例
     */
    private void requestCameraPermission() {
        // 先检查权限是否已授予
        if (PermissionUtils.hasPermissions(this, perms)) {
            GetPhoto();
            return;
        }

        // 检查是否需要显示权限说明
        if (PermissionUtils.shouldShowRequestPermissionRationale(this, PermissionUtils.Permission.CAMERA)) {
            showPermissionRationale("相机权限", "需要相机权限才能拍照", () -> {
                PermissionUtils.requestPermissions(this, perms, new PermissionUtils.PermissionCallback() {
                    @Override
                    public void onPermissionGranted() {
                        GetPhoto();
                    }

                    @Override
                    public void onPermissionDenied(List<String> deniedPermissions, boolean neverAskAgain) {
                        handlePermissionDenied("相机权限", neverAskAgain);
                    }
                });
            });
        } else {
            // 直接申请权限
            PermissionUtils.requestPermissions(this, perms, new PermissionUtils.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    GetPhoto();
                }

                @Override
                public void onPermissionDenied(List<String> deniedPermissions, boolean neverAskAgain) {
                    handlePermissionDenied("相机权限", neverAskAgain);
                }
            });
        }
    }


    /**
     * 参加人popwindow
     */
    private void showJoinPop() {
        isHeadPop = false;
        View view = LayoutInflater.from(IssueDetailActivity.this)
                .inflate(R.layout.popwindow_issue_joiner, null);
        PopupWindow mPeoplePop = new PopupWindow(IssueDetailActivity.this);
        mPeoplePop.setContentView(view);
        mPeoplePop.setFocusable(true);
        mPeoplePop.setOutsideTouchable(true);
        mPeoplePop.setBackgroundDrawable(new BitmapDrawable());
        joinOrHead = view.findViewById(R.id.grid_joinOrhead);
        problemPopWindowJoinAdapter = new ProblemPopWindowJoinAdapter(joinList, this);
        joinOrHead.setAdapter(problemPopWindowJoinAdapter);
        joinOrHead.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                deleteIssueJoiner(i);
            }
        });
        RecyclerView rvJoiner = view.findViewById(R.id.rv_member);
        TextView tv_select_all = view.findViewById(R.id.tv_select_all);
        TextView tv_confirm = view.findViewById(R.id.tv_confirm);
        TextView tv_title = view.findViewById(R.id.tv_select_title);
        tv_title.setText("请选择参与人");
        setBackgroundAlpha(0.5f);
        initRecycleview();

        //成员列表

        rvJoiner.setLayoutManager(new LinearLayoutManager(this));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvJoiner.addItemDecoration(
                new DividerItemDecoration((IssueDetailActivity.this), DividerItemDecoration.VERTICAL));
        //动画
        rvJoiner.setItemAnimator(new DefaultItemAnimator());

        IssueJoinerAdapter joinerAdapter = new IssueJoinerAdapter(this, projectRoleList);
        joinerAdapter.addRecycleItemListener(new IssueJoinerAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                IssueJoinerResult.DataBean usersBean = (IssueJoinerResult.DataBean) o;

                //增加参与人
                addIssueJoiner(usersBean.getRealName(), usersBean.getUserId());
            }
        });
        rvJoiner.setAdapter(joinerAdapter);

        mPeoplePop.setHeight(LayoutParams.WRAP_CONTENT);
        mPeoplePop.setWidth(LayoutParams.MATCH_PARENT);
        tv_select_all.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {

                for (UsersBean name : projectMemberList) {

                    joinList.add(name.getRealName());
                    joinIDList.add(name.getUserId());
                    removeStringListDupli(joinList);
                    removeStringListDupli(joinIDList);
                }

                problemPopWindowJoinAdapter.notifyDataSetChanged();
            }
        });

        tv_confirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                mPeoplePop.dismiss();
       /* //removeStringListDupli(joinList);
        UpdateIssue(roleType, null, null, null, StringUtils.dataToString(copyJoinIdList), null);
        problemJoinAdapter.notifyDataSetChanged();
        mPeoplePop.dismiss();*/
            }
        });
        mPeoplePop.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });
        mPeoplePop.setAnimationStyle(R.style.picker_view_slide_anim);
        mPeoplePop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
    }

    /**
     * 负责人popwindow
     */
    private void showHeadPop() {
        isHeadPop = true;
        View view = LayoutInflater.from(IssueDetailActivity.this)
                .inflate(R.layout.popwindow_issue_joiner, null);
        PopupWindow mPeoplePop = new PopupWindow(IssueDetailActivity.this);
        mPeoplePop.setContentView(view);
        mPeoplePop.setFocusable(true);
        mPeoplePop.setOutsideTouchable(true);
        mPeoplePop.setBackgroundDrawable(new BitmapDrawable());
        problemPopWindowHeadAdapter = new ProblemPopWindowHeadAdapter(headList, this);
        joinOrHead = view.findViewById(R.id.grid_joinOrhead);
        joinOrHead.setAdapter(problemPopWindowHeadAdapter);
        joinOrHead.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                removeIssueTag(i);
            }
        });
        RecyclerView rvJoiner = view.findViewById(R.id.rv_member);
        TextView tv_select_all = view.findViewById(R.id.tv_select_all);
        TextView tv_confirm = view.findViewById(R.id.tv_confirm);
        TextView tv_title = view.findViewById(R.id.tv_select_title);
        tv_title.setText("请选择标签");
        setBackgroundAlpha(0.5f);
        initRecycleview();

        //成员列表

        rvJoiner.setLayoutManager(new LinearLayoutManager(this));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvJoiner.addItemDecoration(
                new DividerItemDecoration((IssueDetailActivity.this), DividerItemDecoration.VERTICAL));
        //动画
        rvJoiner.setItemAnimator(new DefaultItemAnimator());

        IssueTagsAdapter issueTagsAdapter = new IssueTagsAdapter(this, issueTagsList);
        issueTagsAdapter.addRecycleItemListener(new IssueTagsAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                IssueTagsResult.DataBean dataBean = (IssueTagsResult.DataBean) o;

                //增加标签
                addIssueTag(dataBean.getIt_name(), dataBean.getIt_guid());
            }
        });
        rvJoiner.setAdapter(issueTagsAdapter);

        mPeoplePop.setHeight(LayoutParams.WRAP_CONTENT);
        mPeoplePop.setWidth(LayoutParams.MATCH_PARENT);
        tv_select_all.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {

        /*for (UsersBean name : projectMemberList) {

          joinList.add(name.getRealName());
          joinIDList.add(name.getUserId());
          removeStringListDupli(joinList);
          removeStringListDupli(joinIDList);
        }

        problemPopWindowJoinAdapter.notifyDataSetChanged();*/
            }
        });

        tv_confirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                mPeoplePop.dismiss();
       /* //removeStringListDupli(joinList);
        UpdateIssue(roleType, null, null, null, StringUtils.dataToString(copyJoinIdList), null);
        problemJoinAdapter.notifyDataSetChanged();
        mPeoplePop.dismiss();*/
            }
        });
        mPeoplePop.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });
        mPeoplePop.setAnimationStyle(R.style.picker_view_slide_anim);
        mPeoplePop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
    }

    /**
     * 初始化弹窗数据
     */

    /**
     * 设置popwindow默认背景变灰
     */
    public void setBackgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha;
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        getWindow().setAttributes(lp);
    }

    /**
     * 状态和分类popwindow
     */

    private void showStatusOrTypePop() {
        View inflaterView = null;
        if (isTypePopCanClick) {
            inflaterView = LayoutInflater.from(IssueDetailActivity.this)
                    .inflate(R.layout.popwindow_new_problem, null);
        } else {

            inflaterView = LayoutInflater.from(IssueDetailActivity.this)
                    .inflate(R.layout.popwindow_update_status, null);
        }
        statusOrtypePop = new PopupWindow(IssueDetailActivity.this);
        statusOrtypePop.setContentView(inflaterView);
        statusOrtypePop.setFocusable(true);
        statusOrtypePop.setOutsideTouchable(true);
        statusOrtypePop.setBackgroundDrawable(new BitmapDrawable());
        statusOrtypePop.setHeight(LayoutParams.WRAP_CONTENT);
        statusOrtypePop.setWidth(LayoutParams.MATCH_PARENT);
        setBackgroundAlpha(0.5f);
        statusWheelView = inflaterView.findViewById(R.id.status_wheel_view);
        statusWheelView.setOffset(1);
        statusWheelView.setItems(wheelStatusList);
        if (isTypePopCanClick) {
            typeWheelView = inflaterView.findViewById(R.id.type_wheel_view);
            typeWheelView.setOffset(1);
            typeWheelView.setItems(wheelTypeList);
            tv_type = inflaterView.findViewById(R.id.tv_type);
            tv_type.setText(typeTextFromNet);
            line_type = inflaterView.findViewById(R.id.line_type);
        }
        tv_status = inflaterView.findViewById(R.id.tv_status);
        tv_status.setText(statusTextFromNet);
        tvConfirm = inflaterView.findViewById(R.id.tv_confirm);
        tv_title_select = inflaterView.findViewById(R.id.tv_title_select);
        line_status = inflaterView.findViewById(R.id.line_status);

        inflaterView.findViewById(R.id.lin_status).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isTypePopCanClick) {

                    initStatusWheelView();
                } else {

                    initRoleStatusWheelView();
                }
            }
        });
        if (isTypePopCanClick) {
            inflaterView.findViewById(R.id.lin_type).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    initTypeWheelView();
                }
            });
        }
        statusOrtypePop.setAnimationStyle(R.style.picker_view_slide_anim);
        statusOrtypePop.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });

        statusOrtypePop.showAtLocation(linAll, Gravity.BOTTOM, 0, 0);
    }

    /**
     * 初始化状态轮滑
     */
    private void initStatusWheelView() {
        statusWheelView.setVisibility(View.VISIBLE);
        /*if (TextUtils.isEmpty(statusTextFromNet)) {
            tv_status.setText("请选择");
        } else {
            tv_status.setText(statusTextFromNet);
        }*/
        tv_title_select.setText("请选择状态");
        line_status.setVisibility(View.VISIBLE);
        typeWheelView.setVisibility(View.GONE);
        line_type.setVisibility(View.GONE);
        StatusSelected = wheelStatusList.get(0);
        StatusIdSelected = wheelStatusIDList.get(0);

        statusWheelView.setOnWheelViewListener(new OnWheelViewListener() {
            @Override
            public void onSelected(int selectedIndex, String item) {
                StatusSelected = item;
                StatusIdSelected = wheelStatusIDList.get(selectedIndex - 1);
      /*  Logger.t("状态控件")
            .e("selectedIndex: " + wheelStatusIDList.get(selectedIndex) + ", item: " + item);
*/
            }
        });

        tvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                tv_status.setText(StatusSelected);
                tvStatus.setText(StatusSelected);
                UIUtils.postTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        //UpdateIssue(roleType, StatusIdSelected, null, null, null, null);
                        //更新问题状态
                        updateIssueStatus(StatusSelected, StatusIdSelected);
                        statusOrtypePop.dismiss();
                    }
                }, 200);
            }
        });
    }

    /**
     * 初始化状态轮滑
     */
    private void initRoleStatusWheelView() {
        statusWheelView.setVisibility(View.VISIBLE);
        tv_title_select.setText("请选择状态");
        /*if (TextUtils.isEmpty(statusTextFromNet)) {
            tv_status.setText("请选择");
        } else {
            tv_status.setText(statusTextFromNet);
        }*/
        line_status.setVisibility(View.VISIBLE);
        StatusSelected = wheelStatusList.get(0);
        StatusIdSelected = wheelStatusIDList.get(0);

        statusWheelView.setOnWheelViewListener(new OnWheelViewListener() {
            @Override
            public void onSelected(int selectedIndex, String item) {
                StatusSelected = item;
                StatusIdSelected = wheelStatusIDList.get(selectedIndex - 1);
      /*  Logger.t("状态控件")
            .e("selectedIndex: " + wheelStatusIDList.get(selectedIndex) + ", item: " + item);
*/
            }
        });

        tvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                isStatusSelected = true;
                tv_status.setText(StatusSelected);
                tvStatus.setText(StatusSelected);

                UIUtils.postTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        //UpdateIssue(roleType, StatusIdSelected, null, null, null, null);
                        updateIssueStatus(StatusSelected, StatusIdSelected);
                        statusOrtypePop.dismiss();
                    }
                }, 200);
            }
        });
    }

    /**
     * 初始化分类轮滑
     */
    private void initTypeWheelView() {
        Logger.e("走到-----------------------------初始化分类");
        typeWheelView.setVisibility(View.VISIBLE);
        statusWheelView.setVisibility(View.GONE);
        tv_title_select.setText("请选择分类");
        /*if (TextUtils.isEmpty(typeTextFromNet)) {
            tv_type.setText("请选择");
        } else {
            tv_type.setText(typeTextFromNet);
        }*/
        line_status.setVisibility(View.GONE);
        if (isTypePopCanClick) {
            line_type.setVisibility(View.VISIBLE);
        }
        TypeSelected = wheelTypeList.get(0);
        TypeIdSelected = wheelTypeIDList.get(0);

        typeWheelView.setOnWheelViewListener(new OnWheelViewListener() {
            @Override
            public void onSelected(int selectedIndex, String item) {
                TypeSelected = item;
                TypeIdSelected = wheelTypeIDList.get(selectedIndex - 1);
            }
        });

        tvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                tv_type.setText(TypeSelected);
                tvType.setText(TypeSelected);
                UIUtils.postTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        //UpdateIssue(roleType, null, TypeIdSelected, null, null, null);
                        updateIssueType(TypeSelected, TypeIdSelected);
                        statusOrtypePop.dismiss();
                    }
                }, 200);
            }
        });
    }

    /* *//**
     * 获取项目所有人员
     *//*
  private void GetProjectUser() {
    HashMap<String, String> params = new HashMap<>();
    params.put("BIMComposerID", organizeId);
    params.put("UserLikeName", "");
    IssueController controller = new IssueController();
    controller.GetProjectUser(params, new CallBack<List<ProjectUser>>() {
      @Override
      public void onSuccess(List<ProjectUser> projectUsers) {
        projectRoleList.clear();
        for (ProjectUser projectUser : projectUsers) {

          projectRoleList.add(projectUser);
        }

        problemRoleRvAdapter.notifyDataSetChanged();
      }

      @Override
      public void onFail(String erroMsg) {

      }
    });
  }*/

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getProjectID();
            bimProjectId = bean.getBimProjectId();
        }
    }

    /**
     * 去除重复数据
     */
    public List<String> removeStringListDupli(List<String> stringList) {
        Set<String> set = new LinkedHashSet<>();
        set.addAll(stringList);

        stringList.clear();

        stringList.addAll(set);
        return stringList;
    }

    private void updateIssuePhoto(List<File> updatePhotoFileList) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        issueController.uploadIssuePhoto(updatePhotoFileList, params,
                new CallBack<IssueUploadPhotoResult>() {

                    @Override
                    public void onSuccess(IssueUploadPhotoResult issueUploadPhotoResult) {

                        if (issueUploadPhotoResult.getRet() == 1) {
//                            photoFileList.clear();
//                            photoDtoList.clear();
                            for (IssueUploadPhotoResult.DataBean baseFilesBean : issueUploadPhotoResult.getData()) {

                                PhotoDto dto = new PhotoDto();
                                dto.setImg_url(Hawk.get(CustomParam.Base_URL) + "/" + baseFilesBean.getBf_path());
                                dto.setImgId(baseFilesBean.getBf_guid());
                                photoDtoList
                                        .add(dto);

                                tvTvPhotoSize.setText(photoDtoList.size() + "");
                                photoAdapter.notifyDataSetChanged();
                                showMsg("新增图片成功");
                            }
                        }
                    }

                    @Override
                    public void onFail(String erroMsg) {

                    }
                });
    }

    /**
     * 获取筛选条件
     */
    private void GetFilter() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", bimProjectId);
        params.put("token", Hawk.get(CustomParam.Token));
        //params.put()
        issueController.getProblemStatus(params, new CallBack<ProblemStatusModel>() {

            @Override
            public void onSuccess(ProblemStatusModel problemStatusModel) {
                //状态数据
                if (problemStatusModel.getRet() == 1) {
                    for (int i = 0; i < problemStatusModel.getData().size(); i++) {
                        wheelStatusList.add(problemStatusModel.getData().get(i).getItemName());
                        wheelStatusIDList.add(problemStatusModel.getData().get(i).getItemDetailId());
                    }
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

        HashMap<String, String> typeParams = new HashMap<>();
        typeParams.put("organizeId", bimProjectId);
        typeParams.put("token", Hawk.get(CustomParam.Token));
        issueController.getProblemType(params, new CallBack<IssueTypeDto>() {


            @Override
            public void onSuccess(IssueTypeDto issueTypeDto) {
                //分类数据
                if (issueTypeDto.getRet() == 1) {
                    for (int i = 0; i < issueTypeDto.getData().size(); i++) {
                        wheelTypeList.add(issueTypeDto.getData().get(i).getItemName());
                        wheelTypeIDList.add(issueTypeDto.getData().get(i).getItemDetailId());
                    }
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 填充数据
     */
    private void displayResult(IssueDetailResult.DataBean.IssueObjBean dataBean) {
        tvTitle.setText(dataBean.getTitle());
        tvProblemTitle.setText(dataBean.getTitle());
        titleResult = dataBean.getTitle();
        if (!TextUtils.isEmpty(dataBean.getContent())) {
            tvProblemIntro.setText(dataBean.getContent());
        }
        introResult = dataBean.getContent();
        tvStatus.setText(dataBean.getIssueStatusText());
        statusTextFromNet = dataBean.getIssueStatusText();
        typeTextFromNet = dataBean.getIssueTypeText();
        tvType.setText(dataBean.getIssueTypeText());
        if ("0001-01-01T00:00:00".equals(dataBean.getEndDate())) {
            tvDate.setText("请选择截止日期");
        } else {
            tvDate.setText((dataBean.getEndDate().split("T"))[0]);
        }
    }

    /**
     * 获取问题详情
     */
    private void GetIssueDetail(String issueId) {
        joinList.clear();
        joinIDList.clear();
        headList.clear();
        headIdList.clear();

        HashMap<String, String> params = new HashMap<>();
        params.put("IssueID", issueId);
        params.put("Token", Hawk.get(CustomParam.Token));
        issueController.getIssueDetail(params, new CallBack<IssueDetailResult>() {

            @Override
            public void onSuccess(IssueDetailResult result) {
                if (1 == result.getRet()) {

                    issueDetailResult = result.getData();
                    //优先判断角色
                    JudeRole(issueDetailResult.isIsIssueManager());

                    viewPointId = issueDetailResult.getIssueObj().getViewpointID();
                    modelId = issueDetailResult.getIssueObj().getModelId();
                    projectId = issueDetailResult.getIssueObj().getProjectID();

                    //显示UI数据
                    displayResult(issueDetailResult.getIssueObj());
                    //是否加载模型
                    if (!TextUtils.isEmpty(issueDetailResult.getIssueObj().getViewpointID())) {
                        isViewSelected = true;
                        linView.setVisibility(View.VISIBLE);
                        Glide.with(getApplicationContext())
                                .load(issueDetailResult.getIssueObj().getImageUrl())
                                .into(imgView);
                    }
                    //获取参与人数据
                    for (IssueDetailResult.DataBean.JoinersBean joiners2Bean : issueDetailResult.getJoiners()) {
                        joinList.add(joiners2Bean.getRealName());
                        joinIDList.add(joiners2Bean.getId());
                        removeStringListDupli(joinList);
                        removeStringListDupli(joinIDList);
                    }
                    problemJoinAdapter.notifyDataSetChanged();
                    //获取标签数据
                    for (IssueDetailResult.DataBean.TagListBean tagListBean : issueDetailResult.getTagList()) {

                        headList.add(tagListBean.getIt_name());
                        headIdList.add(tagListBean.getIt_guid());
                        removeStringListDupli(headList);
                        removeStringListDupli(headIdList);

                    }
                    judeTag();

                    problemHeadAdapter.notifyDataSetChanged();
                    //处理文档
                    if (!issueDetailResult.getRelationFiles().isEmpty()) {
                        //获取关联文件
                        tvDocAbout.setText(issueDetailResult.getRelationFiles().size() + "");
                        linDoc.setVisibility(View.VISIBLE);
                        docRelevanceList.addAll(issueDetailResult.getRelationFiles());
                        relevanceDocAdapter.notifyDataSetChanged();
                    }
                }

//                GetRelevance(StringUtils.dataToString(docTargetIdList));
                //加载照片数据
                if (!issueDetailResult.getBase_files().isEmpty()) {
                    loadPhotoData(issueDetailResult.getBase_files());
                }
                //评论数字
                if (!issueDetailResult.getComments().isEmpty()) {
                    setCommenteText(issueDetailResult.getComments().size());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 右上角评论个数
     */
    private void setCommenteText(int size) {
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("评论( " + size + " )");
    }

    /***
     * 加载照片数据
     */
    private void loadPhotoData(List<IssueDetailResult.DataBean.BaseFilesBean> photoFiles) {

        for (IssueDetailResult.DataBean.BaseFilesBean baseFilesBean : photoFiles) {

            PhotoDto dto = new PhotoDto();
            dto.setImg_url(Hawk.get(CustomParam.Base_URL) + "/" + baseFilesBean.getBf_path());
            dto.setImgId(baseFilesBean.getBf_guid());
            photoDtoList
                    .add(dto);

            tvTvPhotoSize.setText(photoDtoList.size() + "");
            photoAdapter.notifyDataSetChanged();
        }
    }

    //获取视点信息
    private void getViewPoint(String vpId) {

        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", organizeId);
        params.put("ViewpointID", vpId);
        ModelController controller = new ModelController();
        controller.GetViewPointDetails(params, new CallBack<ModelPointViewDetails>() {
            @Override
            public void onSuccess(ModelPointViewDetails modelPointViewDetails) {
                if (!TextUtils.isEmpty(modelPointViewDetails.getLinks().get(0).getContent())
                        && modelPointViewDetails.getLinks().get(0).getContent()
                        .contains("base64")) {

                    Glide.with(IssueDetailActivity.this)
                            .load(Base64Utils.decode(
                                    modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]))
                            .into(imgView);
                    viewPointOrNtationphotoList.add(new PhotoDto(
                            modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]));
                } else {

                    Glide.with(IssueDetailActivity.this)
                            .load(R.mipmap.img_bg_project_list)
                            .into(imgView);
                }

                tvView.setText(modelPointViewDetails.getName());
                if (TextUtils.isEmpty(modelPointViewDetails.getTag().getDescription())) {
                    tvViewIntro.setText("<无描述>");
                } else {
                    tvViewIntro.setText(modelPointViewDetails.getTag().getDescription());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //删除关联文档
    private void DeleteRelevance(String relevanceId, int type, int pos) {
        HashMap<String, String> params = new HashMap<>();
        params.put("RelationId", relevanceId);
        Logger.t("删除ID").e(relevanceId);
        issueController.DeleteRelevance(params, new CallBack<String>() {
            @Override
            public void onSuccess(String s) {
                if ("success".equals(s)) {
                    if (2 == type) {
                        //照片适配器
            /*if (isUploadPhoto) {
              photoFileList.remove(pos);
            }*/
                        photoFileList.clear();
                        photoRelevanceIdList.remove(pos);
                        Logger.t("删除后照片数组---------->").e(StringUtils.dataToString(photoRelevanceIdList));
                        photoDtoList.remove(pos);
                        tvTvPhotoSize.setText(photoDtoList.size() + "");
                        photoAdapter.notifyDataSetChanged();
                    } else if (1 == type) {
                        //文档适配器
                        docRelevanceList.remove(pos);
                        relevanceDocAdapter.notifyItemRemoved(pos);
                        tvDocAbout.setText(docRelevanceList.size() + "");
                    }
                    showMsg("删除成功");
                } else {
                    showMsg("删除失败");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //删除问题照片
    private void deleteIssuePhoto(String fileId, int pos) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        params.put("FileId", fileId);
        issueController.deleteIssuePhoto(params, new CallBack<DeleteIssuePhotoResult>() {

            @Override
            public void onSuccess(DeleteIssuePhotoResult deleteIssuePhotoResult) {
                if (deleteIssuePhotoResult.getRet() == 1) {

                    showMsg("删除成功");

                    photoDtoList.remove(pos);
                    tvTvPhotoSize.setText(photoDtoList.size() + "");
                    photoAdapter.notifyDataSetChanged();
                    if (photoDtoList.size() == 0) {
                        linePhoto.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    //角色判断
    private void JudeRole(boolean isCanUpdate) {
        //所有角色模型.问题标题,问题描述不可更改

        //创建人------------->可以修改所有内容
        if (isCanUpdate) {
            roleType = 1;
            AuthAdapter();
            //showMsg("有权限修改");
        } else {
            //showMsg("无权限你修改");
            roleType = 3;
            JoinRole();
        }
    }

    //负责人权限
    private void HeadRole() {
        NoAuthAdapter();
        tvType.setTextColor(getResources().getColor(R.color.text_problem_color));
        tvDate.setTextColor(getResources().getColor(R.color.text_problem_color));
        isTypePopCanClick = false;
        linSelectHead.setOnClickListener(null);
        linSelectJoin.setOnClickListener(null);
        linSelectTime.setOnClickListener(null);
        tvDrawingsMore.setOnClickListener(null);
        tvViewMore.setOnClickListener(null);
        tvPhotoMore.setOnClickListener(null);
        tvAddPhoto.setOnClickListener(null);
        gridHead.setOnItemClickListener(null);
        gridJoin.setOnItemClickListener(null);
        photoAdapter.addItemDeleteListener(null);
        tvAddPhoto.setVisibility(View.GONE);
    }

    //参与人
    private void JoinRole() {
        tvTag.setTextColor(getResources().getColor(R.color.text_problem_color));
        tvViewPoint.setTextColor(getResources().getColor(R.color.text_problem_color));
        tvProblemTitle.setTextColor(getResources().getColor(R.color.text_problem_color));
        tvPhotoMore.setTextColor(getResources().getColor(R.color.text_problem_color));
        tvPhotoTitle.setTextColor(getResources().getColor(R.color.text_problem_color));
        tvType.setTextColor(getResources().getColor(R.color.text_problem_color));
        tvDate.setTextColor(getResources().getColor(R.color.text_problem_color));
        isTypePopCanClick = false;
        tvDrawingsMore.setOnClickListener(null);
        tvViewMore.setOnClickListener(null);
        tvPhotoMore.setOnClickListener(null);
        linSelectHead.setOnClickListener(null);
        linSelectJoin.setOnClickListener(null);
        linSelectTime.setOnClickListener(null);
        tvAddPhoto.setOnClickListener(null);
        gridHead.setOnItemClickListener(null);
        gridJoin.setOnItemClickListener(null);
        photoAdapter.addItemDeleteListener(null);
        tvAddPhoto.setVisibility(View.GONE);
        NoAuthAdapter();
        isStatusPopCanClick = false;
        tvStatus.setTextColor(getResources().getColor(R.color.text_problem_color));
    }

    //无权限适配器
    private void NoAuthAdapter() {
        problemHeadAdapter = new ProblemHeadAdapter(headList, this, false);
        gridHead.setAdapter(problemHeadAdapter);
        problemJoinAdapter = new ProblemJoinAdapter(joinList, this, false);
        gridJoin.setAdapter(problemJoinAdapter);
    }

    //权限适配器
    private void AuthAdapter() {
        problemHeadAdapter = new ProblemHeadAdapter(headList, this, true);
        gridHead.setAdapter(problemHeadAdapter);
        problemJoinAdapter = new ProblemJoinAdapter(joinList, this, true);
        gridJoin.setAdapter(problemJoinAdapter);
    }

    /**
     * 修改问题详情
     */
    private void UpdateIssue(int role, String sID, String tID, String hList, String jList,
                             String date) {
        //    角色  role 1 =  创建人   2 = 负责人  3 = 参与人

        HashMap<String, String> params = new HashMap<>();
        params.put("UserId", Hawk.get(CustomParam.UserId));
        switch (role) {
            //创建人
            case 1:
                params.put("IssueID", issueId);
                if (!TextUtils.isEmpty(sID)) {
                    params.put("IssueStatusID", sID);
                }
                if (!TextUtils.isEmpty(tID)) {
                    params.put("IssueTypeID", tID);
                }
                if (!TextUtils.isEmpty(hList)) {
                    params.put("RUserIDStr", hList);
                }
                if (!TextUtils.isEmpty(jList)) {
                    params.put("LUserIDStr", jList);
                }
                if (!TextUtils.isEmpty(date)) {
                    params.put("EndDate", date);
                }
                params.put("Image2D_ModelID", imgModelId);
                params.put("Image2DID", imgId);
                params.put("Image2D_Position", imgPos);
                if (SelectModelViewActivity.IsCan || !isViewSelected) {
                    params.put("ViewPointID", SelectModelViewActivity.ViewId);
                    params.put("ModelID", modelId);
                }

                break;
            //负责人
            case 2:
                params.put("IssueID", issueId);
                params.put("IssueStatusID", sID);
                break;
            //参与人
            case 3:

                break;
        }

        issueController.UpdateIssue(params, new CallBack<String>() {
            @Override
            public void onSuccess(String s) {
                if ("success".equals(s)) {
                    GetIssueDetail(issueId);
                    showMsg("修改问题成功");
                    if (MarkActivity.isCan) {
                        MarkActivity.isCan = false;
                    }
                    if (SelectModelViewActivity.IsCan) {
                        SelectModelViewActivity.IsCan = false;
                    }
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 新增关联照片
     */
    private void NewRelevancPhoto(List<String> photoList) {

        List<RelationsBean> relevanceList = new ArrayList<>();
        //遍历photoList,去除url
        for (String photoUrl : photoList) {

            RelationsBean relationsBean = new RelationsBean();
            relationsBean.setRelationID(UUID.randomUUID().toString().replace("-", ""));
            relationsBean.setSourceID(issueId);
            relationsBean.setSourceType("问题");
            relationsBean.setTargetID(photoUrl);
            relationsBean.setTargetType("文档");
            relationsBean.setCreateUser(Hawk.get(CustomParam.RealName));
            relationsBean.setCreateDate(getSystemDate());
            relevanceList.add(relationsBean);
        }

        RelevanceDto dto = new RelevanceDto();
        dto.setRelations(relevanceList);
        dto.setUserId(Hawk.get(CustomParam.UserId));
        dto.setIssueId(issueId);

        Logger.t("参数------------->").e(JsonHelper.toJson(dto));
        issueController.NewRelevanceDoc(JsonHelper.toJson(dto), new CallBack<BaseDto>() {
            @Override
            public void onSuccess(BaseDto baseDto) {

                if (0 == baseDto.getErrorCode()) {

                    showMsg("关联照片成功");
                    GetIssueDetail(issueId);
                } else {

                    showMsg("关联照片失败");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    private void fff(String s) {

        try {

            //将得到json数据转换为一个json对象
            JSONObject jsonObject = new JSONObject(s);
            //获取"Links"的json对象,并将其转换为一个json数组
            JSONArray jsonArray = jsonObject.getJSONArray("Links");
            //通过循环获取数据,并放入list集合中
            String img_url = jsonArray.getJSONObject(0).getString("Content");

            if (!TextUtils.isEmpty(img_url)
                    && img_url
                    .contains("base64")) {

                Glide.with(getApplicationContext())
                        .load(Base64Utils.decode(
                                img_url.split("base64,")[1]))
                        .into(imgView);

                viewPointOrNtationphotoList.add(new PhotoDto(img_url.split("base64,")[1]));
                DataHolder.getInstance().setData(viewPointOrNtationphotoList);
            } else {

                Glide.with(getApplicationContext())
                        .load(R.mipmap.img_bg_project_list)
                        .into(imgView);
            }

            PointViewDescModel model = new Gson()
                    .fromJson(jsonObject.getString("Tag"), PointViewDescModel.class);
            if (TextUtils.isEmpty(model.getDescription())) {
                tvViewIntro.setText("<无描述>");
            } else {
                tvViewIntro.setText(model.getDescription());
            }

            tvView.setText(jsonObject.getString("Name"));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取二维图纸信息
     */
    private void getTwoDrawing(String twoDimensId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("ProjectID", organizeId);
        params.put("ModelID", imgModelId);
        params.put("VersionNO", "");
        params.put("FileType", "PlanView");
        params.put("FileName", twoDimensId);

        ModelController controller = new ModelController();

        controller.GetTwoDimensionalDetails(params, new CallBack<DrawingsModel>() {
            @Override
            public void onSuccess(DrawingsModel drawingsModel) {
                Glide.with(getApplicationContext())
                        .load(Base64Utils.decode(drawingsModel.getImagebase64()))
                        .into(imgDrawings);
        /*
        Glide.with(getApplicationContext())
            .load(Base64Utils.decode(drawingsModel.getImagebase64())).asBitmap().into(
            new SimpleTarget<Bitmap>() {
              @Override public void onResourceReady(Bitmap resource,
                  GlideAnimation<? super Bitmap> glideAnimation) {
                imageWidth = resource.getWidth();
                imageHeight = resource.getHeight();
                ivDrawings.setImageBitmap(resource);

                Logger.t("图纸大小------------>").e("x----->" + imageWidth + ",y----->" + imageHeight);

                //到指定位置
                //ivDrawings.zoomTo(1f, 4.125549f, 14.236945f);

                 *//* setMoveToTop(200, DrawingsBrowseActivity.this);
                  new Thread(new Runnable() {
                    @Override public void run() {
                      moni(100, 0);
                    }
                  }).start();*//*
              }
            });*/
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //退出 置空
        MarkActivity.imgName = "";
        MarkActivity.img_url = "";
        MarkActivity.img2d_pos = "";
        MarkActivity.img2dId = "";
        MarkActivity.img2d_ModelId = "";
        MarkActivity.isCan = false;

        SelectModelViewActivity.IsCan = false;
        SelectModelViewActivity.ViewId = "";
        SelectModelViewActivity.ViewType = "";
        SelectModelViewActivity.ModelId = "";

        isViewSelected = false;
        isDrawingSelected = false;
        NewIssueActivity.isFromWhere = false;
    }

    /**
     * 更新问题截止时间
     */
    private void updateIssueDate(String issueDate) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        params.put("EndDate", issueDate);
        issueController.updateIssueDate(params, new CallBack<UpdateIssueDateResult>() {

            @Override
            public void onSuccess(UpdateIssueDateResult updateIssueDateResult) {
                if (updateIssueDateResult.getRet() == 1) {
                    showMsg("修改截止日期成功");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 更新问题分类
     */
    private void updateIssueType(String typeName, String typeId) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        params.put("Name", typeName);
        params.put("Id", typeId);
        issueController.updateIssueType(params, new CallBack<UpdateIssueTypeResult>() {

            @Override
            public void onSuccess(UpdateIssueTypeResult updateIssueTypeResult) {
                if (updateIssueTypeResult.getRet() == 1) {
                    showMsg("修改问题分类成功");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 更新问题状态
     */
    private void updateIssueStatus(String statusName, String statusId) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        params.put("Name", statusName);
        params.put("Id", statusId);
        issueController.updateIssueStatus(params, new CallBack<UpdateIssueTypeResult>() {

            @Override
            public void onSuccess(UpdateIssueTypeResult updateIssueTypeResult) {
                if (updateIssueTypeResult.getRet() == 1) {
                    showMsg("修改问题状态成功");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取可添加的问题参与人
     */
    private void GetProjectUser() {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ProjectID", organizeId);
        params.put("RoleId", "-1");
        IssueController controller = new IssueController();
        controller.getIssueJoiner(params, new CallBack<IssueJoinerResult>() {

            @Override
            public void onSuccess(IssueJoinerResult issueJoinerResult) {
                projectRoleList.clear();
                for (IssueJoinerResult.DataBean dataBean : issueJoinerResult.getData()) {
                    projectRoleList.add(dataBean);
                }
                problemJoinAdapter.notifyDataSetChanged();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 增加问题参与人
     */
    private void addIssueJoiner(String joiner, String joinerId) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        params.put("Name", joiner);
        params.put("Id", joinerId);
        issueController.addIssueJoiner(params, new CallBack<UpdateIssuePeopleResult>() {

            @Override
            public void onSuccess(UpdateIssuePeopleResult updateIssuePeopleResult) {
                if (updateIssuePeopleResult.getRet() == 1) {
                    showMsg("增加问题参与人成功");
                    joinList.add(joiner);
                    joinIDList.add(joinerId);
                    removeStringListDupli(joinList);
                    removeStringListDupli(joinIDList);
                    problemPopWindowJoinAdapter.notifyDataSetChanged();
                    problemJoinAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 删减问题参与人
     */
    private void deleteIssueJoiner(int postion) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        params.put("Name", joinList.get(postion));
        params.put("Id", joinIDList.get(postion));
        issueController.deleteIssueJoiner(params, new CallBack<UpdateIssueTypeResult>() {

            @Override
            public void onSuccess(UpdateIssueTypeResult updateIssueTypeResult) {
                if (updateIssueTypeResult.getRet() == 1) {
                    showMsg("移除问题参与人成功");
                    joinList.remove(postion);
                    joinIDList.remove(postion);
                    problemPopWindowJoinAdapter.notifyDataSetChanged();
                    problemJoinAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 添加问题标签数据
     */
    private void addIssueTag(String tagsName, String issueTagId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);

        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < headIdList.size(); i++) {

            stringBuilder.append(headIdList.get(i));
            stringBuilder.append(",");
        }
        stringBuilder.append(issueTagId);
        params.put("rit_tagIds", stringBuilder.toString());
        issueController.setIssueTag(params, new CallBack<BaseResult>() {
            @Override
            public void onSuccess(BaseResult baseResult) {
                if (baseResult.getRet() == 1) {
                    showMsg("增加问题标签成功");
                    headIdList.add(issueTagId);
                    headList.add(tagsName);
                    removeStringListDupli(headIdList);
                    removeStringListDupli(headList);
                    problemHeadAdapter.notifyDataSetChanged();
                    problemPopWindowHeadAdapter.notifyDataSetChanged();
                    judeTag();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    private void judeTag() {
        if (headList.isEmpty()) {
            tvTag.setVisibility(View.VISIBLE);
            if (roleType == 3) {

                tvTag.setText("暂无标签");
            } else {

                tvTag.setText("请选择标签");
            }

        } else {
            tvTag.setVisibility(View.GONE);
        }
    }

    /**
     * 移除问题标签
     */

    private void removeIssueTag(int position) {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("IssueId", issueId);
        params.put("it_guid", headIdList.get(position));
        issueController.removeIssueTag(params, new CallBack<BaseResultDto>() {
            @Override
            public void onSuccess(BaseResultDto baseResultDto) {
                if (baseResultDto.getRet() == 1) {
                    showMsg("移除标签成功");
                    headIdList.remove(position);
                    headList.remove(position);
                    problemHeadAdapter.notifyDataSetChanged();
                    problemPopWindowHeadAdapter.notifyDataSetChanged();
                    judeTag();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }


    /***
     * TBS内核 打开文件
     * @param file
     * @param fileType
     */
    private void displayFile(File file, String fileType) {

        TbsReaderView mTbsReaderView = new TbsReaderView(this, this);
        LinearLayout rootRl = (LinearLayout) findViewById(R.id.lin_all);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                rootRl.addView(mTbsReaderView, new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.MATCH_PARENT));
                Bundle bundle = new Bundle();
                bundle.putString("filePath", file.getPath());
                bundle.putString("tempPath", Environment.getExternalStorageDirectory().getPath());
                boolean result = mTbsReaderView.preOpen(fileType, false);
                if (result) {
                    mTbsReaderView.openFile(bundle);
                }

            }
        });


    }

    @Override
    public void onCallBackAction(Integer integer, Object o, Object o1) {

    }

    /**
     * 拼接在线url地址
     */
    private void spliceDocUrlAndOpen(String downloadUrl, String fileName) {

        mLoading.dismiss();
        try {
            String firstUrl = BaseApp.getDocUrl() + "/api/DocViewer/Viewer/Show?downloadUrl=" + URLEncoder.encode(downloadUrl, "UTF-8") + "&tempNameAfterDownload=" + fileName;
            String docUrl = BaseApp.getDocUrl() + "/Content/PDFJS/web/viewer.html?file=" + URLEncoder.encode(firstUrl, "UTF-8");
            Intent intent = new Intent(IssueDetailActivity.this, DocViewActivity.class);
            intent.putExtra(CustomParam.DocUrl, docUrl);
            intent.putExtra(CustomParam.DocName, fileName);
            startActivity(intent);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

    }

    /**
     * dwg 文件打开
     *
     * @param bean
     */
    private void openDwg(String downloadUrl, DocRelevance bean) {


        if (!TextUtils.isEmpty(FileUtils.isExistFile(bean.getFileName()))) {

            mLoading.dismiss();
            //dwg预览
            Intent intent1 = new Intent(this, DwgPreviewActivity.class);
            intent1.putExtra("file", FileUtils.isExistFile(bean.getFileName()));
            startActivity(intent1);
        } else {

            DownloadUtil.get().download(downloadUrl, "BIMeFile", bean.getFileName(), new DownloadUtil.OnDownloadListener() {
                @Override
                public void onDownloadSuccess(File docFile) {


                    ThreadUtils.getInstance().execute(new Runnable() {
                        @Override
                        public void run() {
                            //dwg预览
                            Intent intent1 = new Intent(IssueDetailActivity.this, DwgPreviewActivity.class);
                            intent1.putExtra("file", docFile.getAbsolutePath());
                            startActivity(intent1);
                            mLoading.dismiss();
                        }
                    });
                }

                @Override
                public void onDownloading(int progress) {

                }

                @Override
                public void onDownloadFailed() {

                }
            });

        }
    }
}
