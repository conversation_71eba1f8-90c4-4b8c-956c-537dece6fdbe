package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.ProjectStructureAdapter;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.ProjectStrcutreResult;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :工程结构
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class ProjectStructureActivity extends BaseActivity implements View.OnClickListener {

  private List<List<ProjectStrcutreResult.DataBean>> allList = new ArrayList<>();
  private String organizeId;
  private ProjectStructureAdapter adapter;
  private boolean isFirstFloor = true;
  private String code = "";
  private ProjectStrcutreResult.DataBean dataBean;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_project_structure);
    initView();
    loadData();
  }

  @Override protected void loadData() {
    GetProjectDao();
    getProjectStrcutre();
  }

  @Override protected void initView() {
    TextView tvTittle = findViewById(R.id.tv_title);
    tvTittle.setText("工程结构");
    TextView tvRight = findView(R.id.tv_right, this);
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("完成");
    LinearLayout liniBack = findView(R.id.lin_back, this);
    RecyclerView rvProjectStructure = findViewById(R.id.rv_project_structure);
    rvProjectStructure.setLayoutManager(new LinearLayoutManager(this));
    DividerItemDecoration itemDecoration =
        new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
    rvProjectStructure.addItemDecoration(itemDecoration);
    rvProjectStructure.setItemAnimator(new DefaultItemAnimator());
    adapter = new ProjectStructureAdapter(this);
    adapter.addRecycleItemListener(new ProjectStructureAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        isFirstFloor = false;
        dataBean = (ProjectStrcutreResult.DataBean) o;
        getProjectStrcutre();
      }
    });
    rvProjectStructure.setAdapter(adapter);
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

  }

  @Override protected void initRefresh() {

  }

  /**
   * 获取工程结构
   */
  private void getProjectStrcutre() {
    HashMap<String, String> params = new HashMap<>();
    params.put("organizeId", organizeId);
    //传递 quality 或 security
    params.put("type", "quality");
    params.put("likepara", (isFirstFloor) ? "" : dataBean.getEc_code());
    CheckController checkController = new CheckController();
    checkController.getProjectStrcutre(params, new CallBack<ProjectStrcutreResult>() {

      @Override public void onSuccess(ProjectStrcutreResult projectStrcutreResult) {
        if (projectStrcutreResult.getData().isEmpty()) {

          // 空值处理

        } else {

          //
          allList.add(projectStrcutreResult.getData());

          adapter.setData(projectStrcutreResult.getData());
        }
      }

      @Override public void onFail(String erroMsg) {

      }
    });
  }

  /**
   * 从数据库获取数据
   */
  private void GetProjectDao() {
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      organizeId = bean.getBimProjectId();
    }
  }

  @Override public void onClick(View v) {
    switch (v.getId()) {

      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        Intent intent = new Intent(this, NewCheckActivity.class);
        intent.putExtra("psData", dataBean);
        setResult(RESULT_OK, intent);
        finish();
        break;
    }
  }
}
