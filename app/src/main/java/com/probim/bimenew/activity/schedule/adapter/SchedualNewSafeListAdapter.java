package com.probim.bimenew.activity.schedule.adapter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualNewSafeDTO;
import com.probim.bimenew.interfaces.IOnItemSchedualSafeClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualNewSafeListAdapter extends RecyclerView.Adapter<SchedualNewSafeListAdapter.ViewHolder> {

    private List<SchedualNewSafeDTO> dtoList = new ArrayList<>();
    private IOnItemSchedualSafeClickListener onItemClickListener;

    public SchedualNewSafeListAdapter(List<SchedualNewSafeDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_safe, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewSafeDTO dto = dtoList.get(position);
        int xx = position + 1;
        holder.tvTitle.setText("风险 " + xx);
        holder.edtName.setText(dto.getMobileSafe_Name());
        holder.edtMeasure.setText(dto.getMobileSafe_Measure());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });

        holder.edtName.clearFocus();
        holder.edtName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        onItemClickListener.showNameChange(position, editable.toString());
                    }

                }
            }
        });

        holder.edtMeasure.clearFocus();
        holder.edtMeasure.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        onItemClickListener.showMeasureChange(position, editable.toString());
                    }

                }

            }
        });

        holder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onDelete(position, dto);
            }
        });
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualSafeClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_safe_title)
        TextView tvTitle;
        @BindView(R.id.edt_name)
        EditText edtName;
        @BindView(R.id.edt_measure)
        EditText edtMeasure;
        @BindView(R.id.iv_delete_item)
        ImageView ivDelete;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
