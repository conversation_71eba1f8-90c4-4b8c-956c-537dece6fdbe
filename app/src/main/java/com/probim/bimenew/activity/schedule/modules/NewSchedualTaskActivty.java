package com.probim.bimenew.activity.schedule.modules;

import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.HorizontalRecyclerView;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.schedule.SoftKeyBoardListener;
import com.probim.bimenew.activity.schedule.adapter.SchedualNewTaskNoPlanListAdapter;
import com.probim.bimenew.activity.schedule.adapter.SchedualNewTaskPlanListAdapter;
import com.probim.bimenew.activity.schedule.adapter.SchedualPhotoAdapter;
import com.probim.bimenew.activity.schedule.adapter.SpinnerAdapter;
import com.probim.bimenew.activity.schedule.dto.MobileJson;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualNewPhotoDto;
import com.probim.bimenew.activity.schedule.dto.SchedualNewTaskNoPlanDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualNewTaskPlanDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualTaskAddDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualTaskDetailsDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualUploadResult;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.SchedualController;
import com.probim.bimenew.interfaces.IOnItemSchedualTaskNoPlanClickListener;
import com.probim.bimenew.interfaces.IOnItemSchedualTaskPlanClickListener;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.CalculateDaysInterval;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.JsonHelper;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import java.io.File;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class NewSchedualTaskActivty extends BaseActivity implements View.OnClickListener {
    private final int code_photo = 3000;
    private final int code_select_task = 1000;
    private TimePickerView pvTime;
    private TextView tvAddDate;
    private List<SchedualNewPhotoDto> photoDtoList = new ArrayList<>();
    private List<File> photoFileList = new ArrayList<>();
    private List<SchedualNewTaskPlanDTO> planList = new ArrayList<>();
    private List<SchedualNewTaskNoPlanDTO> noPlanList = new ArrayList<>();
    private SchedualPhotoAdapter photoAdapter;
    private TextView tvTaskName;
    private TextView tvTaskDate;
    private RecyclerView rvTaskPhoto;
    private SchedualListDto.DataDTO planDto;
    private String creatDate;
    private int taskType = 0;
    private int clickListPosition;
    private List<String> photoNameList = new ArrayList<>();
    private RecyclerView rvPlan;
    private RecyclerView rvNoPlan;
    private SchedualNewTaskPlanListAdapter planAdapter;
    private SchedualNewTaskNoPlanListAdapter noPlanAdapter;
    private RelativeLayout rvSelectDate;
    private EditText edtConstruction;
    private boolean isClickActualratio;
    private int temp;
    private String itemTotalClick;

    private List<SchedualListDto.DataDTO> dtoList = new ArrayList<>();
    private TextView tvPlanName;

    private SpinnerAdapter spinnerAdapter;
    private Boolean isEditTask;
    private Spinner spinner;
    private String uuid;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_schedual_add_task);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
//        initKey();
        loadData();
        initRecycleview();
    }

    private void initKey() {
        SoftKeyBoardListener.setListener(this, new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
            @Override
            public void keyBoardShow(int height) {
                Logger.t("今日填报").e("输入法打开");
            }

            @Override
            public void keyBoardHide(int height) {
                if (isClickActualratio) {
                    int total;
                    if (TextUtils.isEmpty(planList.get(clickListPosition).getProgress_MobileAddratio())) {
                        total = 0;
                    } else {
                        total = Integer.parseInt(planList.get(clickListPosition).getProgress_MobileAddratio());
                    }
                    planList.get(clickListPosition).setProgress_MobileAddratio(String.valueOf(total + temp));
                    planAdapter.notifyDataSetChanged();
                    Logger.t("今日填报").e("输入法关闭");
                }

            }
        });
    }

    @Override
    protected void loadData() {
        getAllPlan();
        if (Hawk.contains(CustomParam.SCHEDUAL_TIME_SELECTED)) {
            creatDate = Hawk.get(CustomParam.SCHEDUAL_TIME_SELECTED);
            tvAddDate.setText(creatDate);
            rvSelectDate.setOnClickListener(null);
        } else {
            creatDate = getDay(new Date(System.currentTimeMillis()));
            tvAddDate.setText(creatDate);
        }
    }

    private void getAllPlan() {
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("organizeId", organizeId);
        schedualController.getSchedualList(parmas, new CallBack<SchedualListDto>() {
            @Override
            public void onSuccess(SchedualListDto schedualListDto) {
                if (schedualListDto.getData() != null) {
                    if (schedualListDto.getData().size() > 0) {
                        planDto = schedualListDto.getData().get(0);
                        dtoList.clear();
                        dtoList.addAll(schedualListDto.getData());
                        spinnerAdapter.notifyDataSetChanged();
                        // 获取全部计划数据
                        if (isEditTask) {
                            uuid = getIntent().getStringExtra(CustomParam.SCHEDUAL_ID_SELECTED);
                            getTaskDetials(uuid);
                        }
                    }

                }

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

    }

    @Override
    protected void initView() {
        ImageView ivUpdateState = findView(R.id.iv_up_state, this);
        isEditTask = getIntent().getBooleanExtra(CustomParam.isEditTask, false);
        TextView tvTittle = findView(R.id.tv_black_title);
        tvTittle.setText("今日填报-任务进度");
        LinearLayout linBack = findView(R.id.lin_back, this);

        tvPlanName = findView(R.id.tv_plan_name);

        creatDate = getDay(new Date(System.currentTimeMillis()));
        tvAddDate = findView(R.id.tv_add_date);

        rvSelectDate = findView(R.id.rl_select_date, this);

        rvPlan = findView(R.id.rv_new_task_plan);
        rvNoPlan = findView(R.id.rv_new_task_non_plan);
        RelativeLayout rlAdd = findView(R.id.rl_add_schedual, this);
        tvTaskName = findView(R.id.tv_task_name);
        tvTaskDate = findView(R.id.tv_task_date);
        rvTaskPhoto = findView(R.id.rv_task_photo);
        RelativeLayout rlSelectPhoto = findView(R.id.rl_add_photo, this);
        TextView ivSave = findView(R.id.tv_right, this);
        ivSave.setVisibility(View.VISIBLE);
        ivSave.setText("保存");
        edtConstruction = findView(R.id.edt_construction);
        spinner = (Spinner) findViewById(R.id.spinner_select_plan);
        // 创建适配器对象并设置数据源
        spinnerAdapter = new SpinnerAdapter(dtoList);
        // 将适配器设置到 Spinner
        spinner.setAdapter(spinnerAdapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                planDto = dtoList.get(i);
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
        if (isEditTask) {
            tvPlanName.setVisibility(View.VISIBLE);
            ivUpdateState.setVisibility(View.VISIBLE);
        } else {
            spinner.setVisibility(View.VISIBLE);
        }

    }

    /**
     * 获取系统时间
     *
     * @return
     */
    private String getDay(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    /**
     * 时间选择器
     */
    private void openTimePicker() {
        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);
        //确定按钮文字颜色
        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                creatDate = getDay(date);
                tvAddDate.setText(creatDate);
            }
        }).setCancelText("清空")
                .setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false})
                .setContentTextSize(16)
                .setLabel("", "", "", "", "", "")
                .isCenterLabel(true)
                .setLineSpacingMultiplier(3.0f)
                .setDividerColor(R.color.divider_color)
                .setDecorView(null)
//                .setRangDate(startDate, endDate)
                .setTextColorCenter(Color.parseColor("#283A4F"))
                .setTextColorOut(Color.parseColor("#A6AEB6"))
                //容器
                //.setDecorView(linContainer)
                .setLayoutRes(R.layout.pickerview_newcheck,
                        new CustomListener() {
                            @Override
                            public void customLayout(View v) {

                                TextView tvDateTittle = v.findViewById(R.id.tv_date_title);
                                tvDateTittle.setText("请选择填报日期");
                                TextView btnConfirm = v.findViewById(R.id.btn_confirm);
                                btnConfirm.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        pvTime.returnData();
                                        pvTime.dismiss();
                                    }
                                });

                                TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                                tv_clear.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        pvTime.dismiss();
                                    }
                                });

                                ImageView ivClose = (ImageView) v.findViewById(R.id.iv_close);
                                ivClose.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View view) {
                                        //pvTime.returnData();
                                        pvTime.dismiss();
                                    }
                                });
                            }
                        })
                .build();
        pvTime.show();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        planAdapter = new SchedualNewTaskPlanListAdapter(planList);
        noPlanAdapter = new SchedualNewTaskNoPlanListAdapter(noPlanList);
        noPlanAdapter.setOnItemClickListener(new IOnItemSchedualTaskNoPlanClickListener() {
            @Override
            public void onClick(int pos, Object o) {

            }

            @Override
            public void onDelete(int pos, Object o) {
                noPlanList.remove(pos);
                noPlanAdapter.notifyDataSetChanged();
            }

            @Override
            public void onClose(int pos, Object o) {


            }

            @Override
            public void showEdtNoPlanChange(int pos, String s) {
                noPlanList.get(pos).setNoProgress_Name(s);
            }
        });
        VerticalRecyclerView.initialize(rvNoPlan).setAdapter(noPlanAdapter);
        planAdapter.setOnItemClickListener(new IOnItemSchedualTaskPlanClickListener() {
            @Override
            public void onClick(int pos, Object o) {

            }

            @Override
            public void onDelete(int pos, Object o) {
                planList.remove(pos);
                planAdapter.notifyDataSetChanged();

            }

            @Override
            public void onClose(int pos, Object o) {

            }

            @Override
            public void onTaskClick(int pos, Object o) {
                clickListPosition = pos;
                Intent intent = new Intent(NewSchedualTaskActivty.this, AllSchedualTaskListActivity.class);
                intent.putExtra("plan-children", planDto);
                startActivityForResult(intent, code_select_task);
            }

            @Override
            public void showEdtWhyChange(int pos, String s) {
                planList.get(pos).setProgress_MobileReason(s);
            }

            @Override
            public void showEdtPlantratioChange(int pos, String s) {
                planList.get(pos).setProgress_planratio(s);
            }

            @Override
            public void showEdtActualratioChange(int pos, String s) {
                /*if (!TextUtils.isEmpty(s)) {
                    isClickActualratio = true;
                    temp = Integer.parseInt(s);
                    clickListPosition = pos;
                }*/
                planList.get(pos).setProgress_actualratio(s);
                Logger.t("今日填报").e(s);


            }

            @Override
            public void showEdtAddratioChange(int pos, String s) {
                // planList.get(pos).setProgress_MobileAddratio(s);
            }

            @Override
            public void showEdtTomorrowratioChange(int pos, String s) {
                planList.get(pos).setProgress_MobileTomorrowratio(s);
            }

            @Override
            public void showStateChange(int pos, String s) {
                planList.get(pos).setProgress_state(s);
            }

        });
        VerticalRecyclerView.initialize(rvPlan).setAdapter(planAdapter);

        //初始化照片适配器
        photoAdapter = new SchedualPhotoAdapter(photoDtoList, this);
        photoAdapter.addItemListener(new SchedualPhotoAdapter.OnItemListener() {
            @Override
            public void OnItemDelete(int pos, Object o) {
                if (isEditTask) {
                    SchedualNewPhotoDto dto = (SchedualNewPhotoDto) o;
                    deletePhoto(dto.getBf_guid(), pos);
                } else {
                    photoDtoList.remove(pos);
                    photoFileList.remove(pos);
//                    photoNameList.remove(pos);
                    photoAdapter.notifyDataSetChanged();
                    if (photoDtoList.size() == 0) {
//                    linePhoto.setVisibility(View.GONE);
                    }
                }

            }

            @Override
            public void OnItemClick(int pos, Object o) {
                if (photoDtoList.size() != 0) {
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
                    bundle.putInt(CustomParam.PhotoPosition, pos);
                    Intent intent = new Intent(NewSchedualTaskActivty.this, SchedualPhotoViewActivity.class);
                    intent.putExtras(bundle);
                    startActivity(intent);
                }
            }

            @Override
            public void onEdtNameChange(int pos, String s) {
                // photoNameList.set(pos, s);
                photoDtoList.get(pos).setBf_filename(s);
//                File newFile = renameFile(photoFileList.get(pos), s);
//                photoFileList.set(pos, newFile);

            }
        });
        HorizontalRecyclerView.initialize(rvTaskPhoto).setAdapter(photoAdapter);
    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        Intent intent = null;
        switch (v.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.rl_select_date:
                openTimePicker();
                break;
            case R.id.rl_add_schedual:
                selectTypePop();
                break;
            case R.id.rl_add_photo:
                getPhoto();
                break;
            case R.id.tv_right:

                if (photoFileList.isEmpty()) {
                    addTask();
                } else {
                    uploadPhoto();
                }

                break;
            case R.id.iv_up_state:
                updateState("待审核");
                break;
            default:
                break;
        }
    }

    /**
     * 手动添加一条空白数据
     */

    private void handleItemData(boolean isPlan) {
        if (isPlan) {

            // 数据整合 addPeopleList
            SchedualNewTaskPlanDTO dto = new SchedualNewTaskPlanDTO();
            // 任务项
            dto.setProgress_Name("请选择");
            // 任务项数据总集  clickTotalStr
            dto.setProgress_MobilePlan("");
            // 计划开始时间
            dto.setProgress_planstarttime("");
            //计划结束时间
            dto.setProgress_plannendtime("");
            //任务状态
            dto.setProgress_state("");
            // 计划完成比例
            dto.setProgress_planratio("");
            // 今日完成比例
            dto.setProgress_actualratio("");
            // 明日完成比例
            dto.setProgress_MobileTomorrowratio("");
            // 累计完成比例
            dto.setProgress_MobileAddratio("");
            // 图片id
            dto.setProgress_MobileImageID("");
            // // 进度计划id
            if (isEditTask) {
                dto.setProgress_ProjectID(uuid);
            } else {
                dto.setProgress_ProjectID(planDto.getUID());
            }

            //当前任务id
            dto.setProgress_treeID("");
            // 填报人
            dto.setProgress_createuser(getLoginAccount());
            // 填报时间
            dto.setProgress_unittime(creatDate);
            dto.setOrganizeId(schedualOrganizeId);
            dto.setProgress_MobileCompany("");
            // 任务分类
//            dto.setTaskType(taskType);
            planList.add(dto);
            planAdapter.notifyDataSetChanged();
            rvPlan.scrollToPosition(planList.size() - 1);


        } else {
            // 数据整合 addPeopleList
            SchedualNewTaskNoPlanDTO dto = new SchedualNewTaskNoPlanDTO();
            // 任务项
            dto.setNoProgress_Name("");
            // 填报人
            dto.setNoProgress_Username(getLoginAccount());
            // 填报时间
            dto.setNoProgress_unittime(creatDate);
            //
            if (isEditTask) {
                dto.setNoProgress_ProjectID(uuid);

            } else {
                dto.setNoProgress_ProjectID(planDto.getUID());

            }
            dto.setOrganizeId(schedualOrganizeId);
            dto.setNoProgress_MobileCompany("");
            noPlanList.add(dto);
            noPlanAdapter.notifyDataSetChanged();
            rvNoPlan.scrollToPosition(noPlanList.size() - 1);

        }
    }

    /**
     * 选择任务计划 或 非任务计划
     */
    private void selectTypePop() {
        View view = LayoutInflater.from(NewSchedualTaskActivty.this)
                .inflate(R.layout.popwindow_schedual, null);
        setBackgroundAlpha(0.5f);
        PopupWindow mPop = new PopupWindow(NewSchedualTaskActivty.this);
        mPop.setContentView(view);
        mPop.setFocusable(true);
        mPop.setOutsideTouchable(true);
        mPop.setBackgroundDrawable(new ColorDrawable());
        mPop.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        mPop.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        view.findViewById(R.id.rl_plan).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                taskType = 0;
                handleItemData(true);
//                includePlan.setVisibility(View.VISIBLE);
                mPop.dismiss();
            }
        });
        view.findViewById(R.id.rl_no_plan).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                taskType = 1;
                handleItemData(false);

//                includeNoPlan.setVisibility(View.VISIBLE);
                mPop.dismiss();
            }
        });
        view.findViewById(R.id.rl_cancle).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPop.dismiss();
            }
        });
        mPop.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                setBackgroundAlpha(1.0f);
            }
        });
        mPop.setAnimationStyle(R.style.picker_view_slide_anim);
        mPop.showAtLocation((ViewGroup) findViewById(android.R.id.content), Gravity.BOTTOM, 0, 0);
    }

    /**
     * 设置popwindow默认背景变灰
     */
    public void setBackgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha;
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        getWindow().setAttributes(lp);
    }

    /**
     * 选取照片
     */
    private void getPhoto() {
        Set<MimeType> mimeTypeSet = new HashSet<>();
        mimeTypeSet.add(MimeType.JPEG);
        mimeTypeSet.add(MimeType.PNG);
        Matisse.from(this)
                .choose(mimeTypeSet)
                .countable(true)
                .capture(true)
                .captureStrategy(
                        new CaptureStrategy(true, getPackageName()))
                .maxSelectable(9)
                .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
                .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
                .thumbnailScale(0.85f)
                .imageEngine(new GlideEngine())
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                .forResult(code_photo);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode != RESULT_OK) {
            return;
        }
        switch (requestCode) {

            case code_photo:

                for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {
                    File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
                    File fileCompress = CompressHelper.getDefault(getApplicationContext())
                            .compressToFile(fileOrigin);
                    SchedualNewPhotoDto dto = new SchedualNewPhotoDto();
                    dto.setBf_path(fileCompress.getAbsolutePath());
                    dto.setBf_filename(fileCompress.getName());
                    photoDtoList.add(dto);
                    photoFileList.add(fileCompress);
//                    photoNameList.add(fileCompress.getName());
                    /*if (isSchedualDetials) {
                        photoDetailsDtoList.add(dto);
                        photoDetailsFileList.add(fileCompress);
                        photoDetailsNameList.add(fileCompress.getName());
                    }*/
                }
                /*linNoSelectPhoto.setVisibility(View.GONE);
                linSelectPhotoNewQuestion.setVisibility(View.VISIBLE);
                tvTvPhotoSize.setText(photoDtoList.size() + "");*/
                photoAdapter.notifyDataSetChanged();

                break;
            case code_select_task:
                SchedualListDto.DataDTO.ChildrenDTO taskDto = (SchedualListDto.DataDTO.ChildrenDTO) data.getExtras().getSerializable(CustomParam.StartForResult);
                itemTotalClick = data.getExtras().getString(CustomParam.PLAN_ITEM_TOTAL);
                Logger.t("层级数据").e(itemTotalClick);
                /**
                 *
                 int planTime = CalculateDaysInterval.calcu(dto.getProgress_planstarttime(),dto.getProgress_plannendtime());
                 int time = CalculateDaysInterval.calcu(dto.getProgress_planstarttime(),dto.getProgress_unittime());
                 float planp = time / planTime;

                 if (planp>=1){
                 holder.edtPlantratio.setText("100");
                 }else {
                 holder.edtPlantratio.setText(Float.toString(planp * 100));
                 }
                 *
                 */
                planList.get(clickListPosition).setProgress_MobilePlan(itemTotalClick);
                planList.get(clickListPosition).setProgress_Name(taskDto.getName());
                planList.get(clickListPosition).setProgress_planstarttime(taskDto.getStart().split("T")[0]);
                planList.get(clickListPosition).setProgress_plannendtime(taskDto.getFinish().split("T")[0]);
                float planTime = CalculateDaysInterval.calcu(planList.get(clickListPosition).getProgress_planstarttime(), planList.get(clickListPosition).getProgress_plannendtime());
                float time = CalculateDaysInterval.calcu(planList.get(clickListPosition).getProgress_planstarttime(), planList.get(clickListPosition).getProgress_unittime());
                if (planTime == 0) {
                    planTime = planTime + 1;
                }
                float planp = time / planTime;
                String planpStr;
                if (planp >= 1) {
                    planpStr = "100";
                    planList.get(clickListPosition).setProgress_planratio(planpStr);
                } else if (planp >= 0) {
                    planpStr = Math.floor(planp * 100) + "";
                    planList.get(clickListPosition).setProgress_planratio(planpStr);
                } else {
                    planpStr = "0";
                    planList.get(clickListPosition).setProgress_planratio(planpStr);
                }
                getTaskAdd(taskDto.getUID(), planpStr);
                planList.get(clickListPosition).setProgress_treeID(taskDto.getUID());
                planAdapter.notifyDataSetChanged();
                break;
            default:

                break;
        }
    }

    /**
     * 任务新增接口
     */
    private void addTask() {
        mLoading.show();
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", getToken());
        parmas.put("organizeId", schedualOrganizeId);
//        parmas.put("", edtConstruction.getText().toString());
        if (isEditTask) {
            parmas.put("Progress_ProjectID", uuid);
        } else {
            parmas.put("Progress_ProjectID", planDto.getUID());
        }

        parmas.put("Progress_unittime", creatDate);
        // 数据组装
        String constructionUnit = edtConstruction.getText().toString();
        if (!TextUtils.isEmpty(constructionUnit)) {

            for (SchedualNewTaskPlanDTO dto : planList) {
                dto.setProgress_MobileCompany(constructionUnit);

            }
            for (SchedualNewTaskNoPlanDTO dto : noPlanList) {
                dto.setNoProgress_MobileCompany(constructionUnit);
            }
        }
        MobileJson json = new MobileJson();
        json.setPlan_json(planList);
        json.setNoPlan_json(noPlanList);
        parmas.put("MobileJSON", JsonHelper.toJson(json));
        schedualController.addSchedualTask(parmas, new CallBack<SchedualBaseResult>() {

            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                if (schedualBaseResult.getRet() == 1) {
                    if (!isSchedualDetials) {
                        Hawk.put(CustomParam.SCHEDUAL_TIME_SELECTED, creatDate);
                    }
                    showMsg(schedualBaseResult.getData());
                    finish();
                } else {
                    showMsg(schedualBaseResult.getMsg());
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });


    }

    /**
     * 处理数组json
     */
    private void handleListJson() {
        /**
         *{
         * "Plan_json": [{
         * "Progress_Name": "基础开挖",  // 任务项
         * "Progress_planstarttime": "2014-12-1", // 计划开始时间
         * "Progress_plannendtime": "2014-12-8", //计划结束时间
         * "Progress_state": "滞后", //任务状态
         * "Progress_planratio": "20", 计划完成比例
         * "Progress_actualratio": "20",今日完成比例
         * "Progress_MobileTomorrowratio": "12",明日完成比例
         * "Progress_MobileAddratio": "12", 累计完成比例
         * "Progress_MobileImageID": "0667d1d9-7283-4076-a065-195a5266cf67,067a9afe-9ee5-447e-b38e-2b253df99d56", // 图片id
         * "Progress_ProjectID": "3695d371-6d74-42cb-bdd0-fb193591f051", // 进度计划id
         * "Progress_treeID": "72381fa7-446e-4923-9f24-6a20ce395fd6", //当前任务id
         * "Progress_createuser": "薛友松", //填报人 登录人
         * "Progress_unittime": "2022-8-26" //填报时间
         * "Progress_MobileReason": "无" //滞后原因
         *
         * }],
         * "NoPlan_json": [{
         * "NoProgress_unittime": "2022-8-26", //填报时间
         * "NoProgress_Username": "薛友松", //填报人
         * "NoProgress_Name": "测试" // 备注
         * }]
         * }
         *
         *
         */


    }

    /**
     * 图片上传接口
     */
    private void uploadPhoto() {
        mLoading.show();
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Token", getToken());
        if (isEditTask) {
            parmas.put("Progress_ProjectID", uuid);
        } else {
            parmas.put("Progress_ProjectID", planDto.getUID());
        }

        parmas.put("Progress_unittime", creatDate);
        parmas.put("organizeId", schedualOrganizeId);
        // 组装数据
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < photoDtoList.size(); i++) {
            if (photoDtoList.get(i).getBf_guid() == null) {
                stringBuilder.append(photoDtoList.get(i).getBf_filename());
                stringBuilder.append(",");
            }

        }
        String imgIdsStr = stringBuilder.toString();
        parmas.put("Progress_imageName", imgIdsStr);
        // 数据组装
        schedualController.uploadSchedualPhoto(photoFileList, parmas, new CallBack<SchedualUploadResult>() {


            @Override
            public void onSuccess(SchedualUploadResult schedualUploadResult) {

                /*// 组装数据
                StringBuilder stringBuilder = new StringBuilder();
                for (SchedualUploadResult.DataDTO dataDTO : schedualUploadResult.getData()) {
                    stringBuilder.append(dataDTO.getBf_guid());
                    stringBuilder.append(",");

                }
                imgIdsStr = stringBuilder.toString();*/
                //新建任务
                if (schedualUploadResult.getRet() == 1) {
                    addTask();
                } else {
                    showMsg(schedualUploadResult.getMsg());
                }

                mLoading.dismiss();

            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });


    }


    /**
     * 重命名文件
     *
     * @param file    文件
     * @param newName 新名称
     * @return {@code true}: 重命名成功<br>{@code false}: 重命名失败
     */
    private File renameFile(File file, String newName) {

        File newFile = new File(file.getParent() + File.separator + newName + getFileExtension(file.getName()));
        // 如果重命名的文件已存在返回false
        file.renameTo(newFile);
        return newFile;
    }

    /**
     * 获取文件的拓展名
     *
     * @param fileName
     * @return
     */
    private String getFileExtension(String fileName) {

        String fe = "";
        int i = fileName.lastIndexOf('.');
        if (i > 0) {
            fe = fileName.substring(i + 1);
        }

        return "." + fe;
    }

    /**
     * 判断值在数组的索引
     *
     * @param target
     * @return
     */
    public int findIndex(String target) {
        for (int i = 0; i < dtoList.size(); i++) {
            if (dtoList.get(i).getName().equals(target)) {
                return i;
            }
        }
        return -1; // 如果未找到目标值，则返回-1
    }

    /**
     * 现场督导会详情
     */
    private void getTaskDetials(String UID) {
        mLoading.show();
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Progress_ProjectID", UID);
        parmas.put("Progress_createuserid", Hawk.get(CustomParam.Alpha));
        parmas.put("Progress_unittime", Hawk.get(CustomParam.SCHEDUAL_TIME_SELECTED));
        schedualController.getTaskDetials(parmas, new CallBack<SchedualTaskDetailsDTO>() {
            @Override
            public void onSuccess(SchedualTaskDetailsDTO schedualTaskDetailsDTO) {
                if (schedualTaskDetailsDTO.getRet() == 1) {
                    photoDtoList.addAll(schedualTaskDetailsDTO.getData().getImage_Json());
                    photoAdapter.notifyDataSetChanged();
                    planList.addAll(schedualTaskDetailsDTO.getData().getPlan_json());
                    planAdapter.notifyDataSetChanged();
                    noPlanList.addAll(schedualTaskDetailsDTO.getData().getNoPlan_json());
                    noPlanAdapter.notifyDataSetChanged();
                    if (planList.size() > 0) {
                        tvPlanName.setText(planList.get(0).getProgress_Name());
                        spinner.setSelection(findIndex(planList.get(0).getProgress_Name()), true);
                    }
                    if (noPlanList.size() > 0) {
                        tvPlanName.setText(noPlanList.get(0).getProgress_Name());
                        spinner.setSelection(findIndex(noPlanList.get(0).getProgress_Name()), true);
                    }
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }

    /**
     * 现场督导会详情
     */
    private void getTaskAdd(String id, String plan) {
        mLoading.show();
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Progress_ProjectID", planDto.getUID());
        parmas.put("Progress_treeID", id);
        schedualController.getTaskAdd(parmas, new CallBack<SchedualTaskAddDTO>() {
            @Override
            public void onSuccess(SchedualTaskAddDTO schedualTaskAddDTO) {
                if (schedualTaskAddDTO.getRet() == 1) {
                    String temp;
                    if (TextUtils.isEmpty(schedualTaskAddDTO.getData())) {
                        temp = "0";
                    } else {
                        temp = schedualTaskAddDTO.getData();
                    }
                    planList.get(clickListPosition).setProgress_MobileAddratio(schedualTaskAddDTO.getData());


                    if (Float.parseFloat(plan) > Float.parseFloat(temp)) {
                        planList.get(clickListPosition).setProgress_state("滞后");
                    } else if (Float.parseFloat(plan) < Float.parseFloat(temp)) {
                        planList.get(clickListPosition).setProgress_state("超前");
                    } else {
                        planList.get(clickListPosition).setProgress_state("正常");
                    }


                    planAdapter.notifyDataSetChanged();
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }

    /**
     * 现场督导会详情
     */
    private void deletePhoto(String guid, int pos) {
        mLoading.show();
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("bf_Guid", guid);
        schedualController.deletePhoto(parmas, new CallBack<SchedualBaseResult>() {
            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                if (schedualBaseResult.getRet() == 1) {
                    photoDtoList.remove(pos);
                    photoAdapter.notifyDataSetChanged();
                }

                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }

    /**
     * 预览发布接口
     */
    private void updateState(String state) {
        SchedualPreviewListDTO.DataDTO dto = Hawk.get(CustomParam.SCHEDUAL_ITEM_SELECTED);
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
//        parmas.put("Unittime", dto.getMobilePA_Unittime());
//        parmas.put("CreateuserId", dto.getMobilePA_Createuserid());
//        parmas.put("ProjectID", dto.getMobilePA_ProjectID());
        parmas.put("organizeId", schedualOrganizeId);
        parmas.put("State", state);
        parmas.put("Token", getToken());
        schedualController.updateState(parmas, new CallBack<SchedualBaseResult>() {
            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                if (schedualBaseResult.getRet() == 1) {
                    showMsg(schedualBaseResult.getData());
                    finish();
                } else {
                    showMsg(schedualBaseResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
}
