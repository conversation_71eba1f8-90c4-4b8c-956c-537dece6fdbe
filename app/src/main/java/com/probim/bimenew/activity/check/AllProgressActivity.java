package com.probim.bimenew.activity.check;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.NewCheckActivity;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.adapter.AllProgressAdapter;
import com.probim.bimenew.adapter.AllProgtessItemAdapter;
import com.probim.bimenew.adapter.AllTaskAdapter;
import com.probim.bimenew.adapter.TaskItemAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.controller.SchedualController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllOriginTaskItemDto;
import com.probim.bimenew.result.AllTaskItemDto;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.result.AllTaskResult;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.dropdownmenu.bean.DropdownItemObject;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :任务列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class AllProgressActivity extends BaseActivity implements View.OnClickListener {

    List<SchedualListDto.DataDTO.ChildrenDTO> tasks = new ArrayList<>();
    List<AllTaskItemResult.DataBean.TasksBean> itemTasks = new ArrayList<>();
    private List<SchedualListDto.DataDTO> allList = new ArrayList<>();
    private List<SchedualListDto.DataDTO.ChildrenDTO> allItemList = new ArrayList<>();
    private List<AllOriginTaskItemDto> originList = new ArrayList<>();
    private String organizeId;

    private AllProgressAdapter allAdapter;
    private int index = -1;
    private RecyclerView rvProjectStructure;
    private boolean isFirst = true;
    private boolean isOnItem = false;
    private CheckController checkController;
    private String projectID;
    private boolean isTaskFrom;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_project_structure);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
        GetProjectDao();
        getPlan();
    }

    /**
     * 获取计划列表
     */
    private void getPlan() {
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("organizeId", projectID);
        schedualController.getSchedualList(parmas, new CallBack<SchedualListDto>() {
            @Override
            public void onSuccess(SchedualListDto schedualListDto) {
                if (schedualListDto.getData() != null) {
                    allList.addAll(schedualListDto.getData());
                    allAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void initView() {
        if (getIntent() != null) {
            isTaskFrom = getIntent().getBooleanExtra("isTask", false);
        }
        TextView tvTittle = findViewById(R.id.tv_black_title);
        tvTittle.setText("任务列表");
        TextView tvRight = findView(R.id.tv_right, this);
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("完成");
        LinearLayout liniBack = findView(R.id.lin_back, this);

        //初始化全部文件夹适配器
        setAllAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();
            projectID = bean.getProjectID();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.lin_back:
                finish();
                //backStepToList();

                break;
            case R.id.tv_right:

                Intent intent = new Intent(this, NewCheckActivity.class);
                Bundle bundle = new Bundle();
                bundle.putSerializable(CustomParam.StartForResult, (Serializable) getItemSelectedList());
                //intent.putExtra(CustomParam.StartForResult, getSelectedList());
                intent.putExtras(bundle);
                setResult(RESULT_OK, intent);
                finish();
                break;
        }
    }
    /**
     * 获取选中数据 传值回新建任务界面
     */
    private List<SchedualListDto.DataDTO.ChildrenDTO> getItemSelectedList() {

        List<SchedualListDto.DataDTO.ChildrenDTO> temp = new ArrayList<>();
        for (int i = 0; i < tasks.size(); i++) {
            if (tasks.get(i).isSelected()) {
                temp.add(tasks.get(i));
            }
        }
        return temp;

    }

    /**
     * 设置新的适配器
     */
    private void setItemAdapter() {

        AllProgtessItemAdapter itemAdapter = new AllProgtessItemAdapter(this, isTaskFrom, tasks);
        VerticalRecyclerView.initialize(rvProjectStructure).setAdapter(itemAdapter);
        itemAdapter.addRecycleItemListener(new AllProgtessItemAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                SchedualListDto.DataDTO.ChildrenDTO dto = (SchedualListDto.DataDTO.ChildrenDTO) o;
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(JsonHelper.toJson(dto));
                    // 检查属性是否存在
                    if (jsonObject.has("children")) {
                        // 文件夹
                        List<SchedualListDto.DataDTO.ChildrenDTO> tempTasks = new ArrayList<>();
                        tempTasks.addAll(dto.getChildren());
                        tasks.clear();
                        tasks.addAll(tempTasks);
                        itemAdapter.notifyDataSetChanged();
                    } else {
                        // 单选
                    }
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }


            }
        });
        rvProjectStructure.setAdapter(itemAdapter);
    }

    /**
     * 设置文件夹适配器
     */
    private void setAllAdapter() {

        rvProjectStructure = findViewById(R.id.rv_project_structure);
        allAdapter = new AllProgressAdapter(this, allList);
        allAdapter.addRecycleItemListener(new AllProgressAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                List<SchedualListDto.DataDTO.ChildrenDTO> dto = (List<SchedualListDto.DataDTO.ChildrenDTO>) o;
                if (!dto.isEmpty()) {
                    tasks.addAll(dto);
                    setItemAdapter();
                }


            }
        });
        VerticalRecyclerView.initialize(rvProjectStructure).setAdapter(allAdapter);
        rvProjectStructure.setAdapter(allAdapter);
    }


}
