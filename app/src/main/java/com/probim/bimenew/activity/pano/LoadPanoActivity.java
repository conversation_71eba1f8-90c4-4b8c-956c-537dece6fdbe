package com.probim.bimenew.activity.pano;

import android.annotation.SuppressLint;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.JavascriptInterface;
import android.webkit.JsPromptResult;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.content.FileProvider;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.BuildConfig;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.DwgPreviewActivity;
import com.probim.bimenew.activity.NewIssueActivity;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.result.BIMFileResult;
import com.probim.bimenew.utils.FileUtils;
import com.probim.bimenew.utils.GsonSingleton;
import com.probim.bimenew.utils.base64file.File64;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.Set;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Description :视图加载或者视点加载
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/30/17:24.
 */
public class LoadPanoActivity extends BaseActivity {

    private final String webUrl = Hawk.get(CustomParam.Panorama_URL);
    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.lin_back)
    LinearLayout linBack;
    @BindView(R.id.tv_black_title)
    TextView tvTitle;
    @BindView(R.id.web_x)
    WebView webX;
    @BindView(R.id.tv_right)
    TextView tvRight;
    @BindView(R.id.rl_load_view)
    RelativeLayout rlContaner;
    private String projectId;
    private String bim365Id;
    private String projectType;
    private String projectName;
    private String modelId;
    private String imgBase64;
    private WebView newWebView;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_web);
        ButterKnife.bind(this);
        StatusBarUtils.transparencyBar(this);
        initWebView();
        loadData();
    }

    /**
     * 初始化webview
     */
    private void initWebView() {
//        tvRight.setVisibility(View.VISIBLE);
//        tvRight.setText(R.string.loadview_righttext);
        WebSettings webSetting = webX.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setJavaScriptCanOpenWindowsAutomatically(true);
        webSetting.setAllowFileAccess(true);
        webSetting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        webSetting.setSupportZoom(true);
        webSetting.setBuiltInZoomControls(true);
        webSetting.setUseWideViewPort(true);
        webSetting.setSupportMultipleWindows(true);
        webSetting.setSupportMultipleWindows(true);
        initJs();
    }

    private void initJs() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }

        webX.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                // 一般根据scheme（协议格式） & authority（协议名）判断（前两个参数）
                // 例如：url = "js://webview?arg1=111&arg2=222"
                Uri uri = Uri.parse(url);
                // 如果url的协议 = 预先约定的 js 协议
                Logger.t("url").e(url);
                if (uri.getScheme().equals("xxx")) {
                    // 拦截url,下面JS开始调用Android需要的方法
                    // 执行JS所需要调用的逻辑
                    Logger.e(uri.getAuthority());
                    imgBase64 = uri.toString();
                    Logger.t("base64").e(imgBase64);
                    Set<String> collection = uri.getQueryParameterNames();
                    Iterator<String> it = collection.iterator();
                    String result = "";
                    while (it.hasNext()) {
                        result += uri.getQueryParameter(it.next()) + ",";
                        Logger.e(result);
                        // tv_result.setText(result);
                    }
                    return true;
                }
                return super.shouldOverrideUrlLoading(view, url);
            }
        });
        webX.setWebChromeClient(new WebChromeClient() {
                                    @Override
                                    public void onProgressChanged(WebView view, int newProgress) {
                                        mLoading.show();
                                        if (newProgress == 100) {

                                            mLoading.dismiss();
                                        }
                                        super.onProgressChanged(view, newProgress);
                                    }

                                    @Override
                                    public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
                                        // 根据协议的参数，判断是否是所需要的url(原理同方式2)
                                        // 一般根据scheme（协议格式） & authority（协议名）判断（前两个参数）
                                        //假定传入进来的 url = "js://webview?arg1=111&arg2=222"（同时也是约定好的需要拦截的）

                                       /* Logger.t("模型截图").e(message);
                                        Uri uri = Uri.parse(message);
                                        // 如果url的协议 = 预先约定的 js 协议
                                        // 就解析往下解析参数
                                        if (uri.getScheme().equals("data")) {
                                            imgBase64 = message;

                                            // 如果 authority  = 预先约定协议里的 webview，即代表都符合约定的协议
                                            // 所以拦截url,下面JS开始调用Android需要的方法
                                            if (uri.getAuthority().equals("webview")) {

                                                //
                                                // 执行JS所需要调用的逻辑
                                                System.out.println("js调用了Android的方法");
                                                // 可以在协议上带有参数并传递到Android上
                                                HashMap<String, String> params = new HashMap<>();
                                                Set<String> collection = uri.getQueryParameterNames();

                                                //参数result:代表消息框的返回值(输入值)
                                                result.confirm("js调用了Android的方法成功啦");
                                            }
                                            return true;
                                        }*/
                                        return true;
                                    }

                                    @Override
                                    public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                                        Logger.t("consoleMessage").e(consoleMessage.message());

                                        if (!TextUtils.isEmpty(consoleMessage.message()) && consoleMessage.message().startsWith("data:image")) {
                                            imgBase64 = consoleMessage.message();

                                        }
                                        return super.onConsoleMessage(consoleMessage);
                                    }

                                    @Override
                                    public boolean onCreateWindow(WebView view, boolean isDialog, boolean is7UserGesture, Message resultMsg) {

                                        newWebView = new WebView(LoadPanoActivity.this);
                                        WebSettings settings = newWebView.getSettings();
                                        settings.setJavaScriptEnabled(true);//支持js
                                        rlContaner.addView(newWebView);
                                        newWebView.setWebViewClient(new WebViewClient());
                                        newWebView.setWebChromeClient(this);
                                        WebView.WebViewTransport transport = (WebView.WebViewTransport) resultMsg.obj;
                                        transport.setWebView(newWebView);
                                        resultMsg.sendToTarget();
                                        return true;


                                    }


                                }


        );

        webX.addJavascriptInterface(new getOnPageFinished(), "finishRenderToAndroid");

    }

    @Override
    protected void loadData() {
        if (getIntent() != null) {
            Intent intent = getIntent();
            //读取数据
            Bundle bundle = intent.getExtras();
            String url = bundle.getString(CustomParam.LoadPano);
            tvTitle.setText(bundle.getString(CustomParam.TvLeft));
            projectId = bundle.getString(CustomParam.ProjectId);
            boolean isAll = bundle.getBoolean("isAll");
            if (isAll) {
                url = webUrl + "/Panorama/" + url + "/vtour/tour2.html?organizeId=" + projectId + "&isDis=";
            } else {
                String sceneId = bundle.getString(CustomParam.SceneId);
                url = webUrl + "/Panorama/" + url + "/vtour/tour2.html?organizeId=" + projectId + "&isDis=&startscene=scene_" + sceneId;
            }

            webX.loadUrl(url);
            Logger.t("全景图地址").e(url);
        }
    }

    @Override
    protected void initView() {

    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @SuppressLint("NewApi")
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @OnClick({R.id.iv_back, R.id.tv_left, R.id.lin_back, R.id.tv_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_left:
                finish();
                break;
            case R.id.lin_back:
                finish();
                break;
            case R.id.tv_right:
                mLoading.show();

        }
    }

    /**
     * web加载js
     */
    private void loadViewJs(JsViewPoistionCallBack jsViewPoistionCallBack) {
        /**
         * 获取js相机位置
         */

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            webX.evaluateJavascript("window.model.BIM365API.Context.getViewPointBasicInfo(true)",
                    new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String s) {
                            Logger.t("相机位置").e(s);
                            if (!TextUtils.isEmpty(s)) {
                                jsViewPoistionCallBack.selectedViewPoistion(s);
                            }

                        }
                    });
        }

        /**
         * 获取js相机快照
         */
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            webX.evaluateJavascript("window.model.BIM365API.Context.getPictureByNav()",
                    new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String s) {
                            //Logger.t("模型截图").e(s);


                        }
                    });
        }

    }


    /**
     * 打开文件
     */
    private void openFile(File modelFile) {

        Intent intent = null;
        String fileType = FileUtils.getFileType(modelFile.getName().toLowerCase());

        if ("dwg".equalsIgnoreCase(fileType)) {
            //dwg预览
            intent = new Intent(this, DwgPreviewActivity.class);
            intent.putExtra("file", modelFile.getAbsolutePath());
            startActivity(intent);
        } else {

            try {
                //其他格式打开
                intent = new Intent(Intent.ACTION_VIEW);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    Uri uri = FileProvider.getUriForFile(this, getPackageName(), modelFile);
                    grantUriPermission(BuildConfig.APPLICATION_ID, uri,
                            Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    intent.addFlags(
                            Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    intent.setFlags(
                            Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    intent.setData(uri);
                } else {
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setDataAndType(Uri.fromFile(modelFile), fileType);
                }

                startActivity(intent);
            } catch (ActivityNotFoundException e) {

                showMsg("文件不能打开,请下载相关软件");
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {

            if (newWebView != null) {
                rlContaner.removeView(newWebView);
                newWebView.destroy();
                newWebView = null;
                return true;


            } else if (webX != null) {
                webX.destroy();
                webX = null;
                finish();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    /***
     * jsViewPoistion CallBack
     */
    interface JsViewPoistionCallBack {

        void selectedViewPoistion(String viewPoistion);
    }

    private interface JsGetImage64 {

        void getImage64(String image64);
    }

    public class AndroidtoJs extends Object {
        // 定义JS需要调用的方法
        // 被JS调用的方法必须加入@JavascriptInterface注解
        @JavascriptInterface
        public void PreView(String msg) throws IOException {
            if (!TextUtils.isEmpty(msg)) {
                Logger.t("jsssss").e(msg);

                BIMFileResult bimFileResult = GsonSingleton.INSTANCE.jsonTobean(msg, BIMFileResult.class);
                //  文件格式
                if (bimFileResult.getContent().startsWith("data")) {
                    String fileName = bimFileResult.getName().split("\\|")[1];
                    String fileContent = bimFileResult.getContent().split("\\,", 2)[1];
                    Logger.t("jsssss").e(fileName);
                    Logger.t("jsssss").e(fileContent);
                    File64.decryptFileByBase64(fileContent, fileName, new File64.setOnFileDownloadListener() {


                        @Override
                        public void onDownloadSuccess(File modelFile) {
                            mLoading.dismiss();
                            //打开文件
                            openFile(modelFile);
                        }

                        @Override
                        public void onDownloading() {
                            mLoading.show();
                        }
                    });
                }

            }

        }


    }


    /**
     * 模型加载完成
     */
    public class getOnPageFinished extends Object {
        // 定义JS需要调用的方法
        // 被JS调用的方法必须加入@JavascriptInterface注解
        @JavascriptInterface
        public void getOnPageFinished(String msg) throws IOException {
            if (!TextUtils.isEmpty(msg)) {


                Logger.t("getOnPageFinished").e(msg);


            }

        }


    }

}
