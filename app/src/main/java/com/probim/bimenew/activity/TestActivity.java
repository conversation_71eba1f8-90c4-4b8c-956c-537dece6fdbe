package com.probim.bimenew.activity;

import android.app.DownloadManager;
import android.app.DownloadManager.Query;
import android.app.DownloadManager.Request;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.Button;
import android.widget.RelativeLayout;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.R;
import com.tencent.smtt.sdk.TbsReaderView;
import com.tencent.smtt.sdk.TbsReaderView.ReaderCallback;
import java.io.File;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/27/16:18.
 */
public class TestActivity extends BaseActivity implements ReaderCallback {

  private TbsReaderView mTbsReaderView;
  private Button mDownloadBtn;

  private DownloadManager mDownloadManager;
  private long mRequestId;
  private DownloadObserver mDownloadObserver;
  private String mFileUrl = "http://ow365.cn/?i=34&n=1&furl=http%3A%2F%2Fofficeweb365.com%2Fviewfile%2F%E5%85%B3%E4%BA%8E%E5%8A%A0%E5%BF%AB%E4%B8%B4%E6%97%B6%E8%AE%BE%E6%96%BD%E5%BB%BA%E8%AE%BE%E9%80%9F%E5%BA%A6%E5%92%8C%E4%BF%9D%E8%AF%81%E5%BB%BA%E8%AE%BE%E6%A0%87%E5%87%86%E7%9A%84%E9%80%9A%E7%9F%A5.docx";
  private String mFileName;

  @Override
  protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_test);
    ButterKnife.bind(this);

    mTbsReaderView = new TbsReaderView(this, this);
    mDownloadBtn = (Button) findViewById(R.id.btn_download);
    RelativeLayout rootRl = (RelativeLayout) findViewById(R.id.rl_root);
    rootRl.addView(mTbsReaderView,
        new RelativeLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));

    mFileName = parseName(mFileUrl);
    if (isLocalExist()) {
      mDownloadBtn.setText("打开文件");
    }
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }


  private void displayFile() {
    Bundle bundle = new Bundle();
    bundle.putString("filePath", getLocalFile().getPath());
    bundle.putString("tempPath", Environment.getExternalStorageDirectory().getPath());
    boolean result = mTbsReaderView.preOpen(parseFormat(mFileName), false);
    if (result) {
      mTbsReaderView.openFile(bundle);
    }
  }

  private String parseFormat(String fileName) {
    return fileName.substring(fileName.lastIndexOf(".") + 1);
  }

  private String parseName(String url) {
    String fileName = null;
    try {
      fileName = url.substring(url.lastIndexOf("/") + 1);
    } finally {
      if (TextUtils.isEmpty(fileName)) {
        fileName = String.valueOf(System.currentTimeMillis());
      }
    }
    return fileName;
  }

  private boolean isLocalExist() {
    return getLocalFile().exists();
  }

  private File getLocalFile() {
    return new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
        mFileName);
  }

  private void startDownload() {
    mDownloadObserver = new DownloadObserver(new Handler());
    getContentResolver()
        .registerContentObserver(Uri.parse("content://downloads/my_downloads"), true,
            mDownloadObserver);

    mDownloadManager = (DownloadManager) getSystemService(DOWNLOAD_SERVICE);
    Request request = new Request(Uri.parse(mFileUrl));
    request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, mFileName);
    request.allowScanningByMediaScanner();
    request.setNotificationVisibility(Request.VISIBILITY_HIDDEN);
    mRequestId = mDownloadManager.enqueue(request);
  }

  private void queryDownloadStatus() {
    Query query = new Query().setFilterById(mRequestId);
    Cursor cursor = null;
    try {
      cursor = mDownloadManager.query(query);
      if (cursor != null && cursor.moveToFirst()) {
        //已经下载的字节数
        int currentBytes = cursor
            .getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR));
        //总需下载的字节数
        int totalBytes = cursor
            .getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_TOTAL_SIZE_BYTES));
        //状态所在的列索引
        int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));
        Log.i("downloadUpdate: ", currentBytes + " " + totalBytes + " " + status);
        mDownloadBtn.setText("正在下载：" + currentBytes + "/" + totalBytes);
        if (DownloadManager.STATUS_SUCCESSFUL == status
            && mDownloadBtn.getVisibility() == View.VISIBLE) {
          mDownloadBtn.setVisibility(View.GONE);
          mDownloadBtn.performClick();
        }
      }
    } finally {
      if (cursor != null) {
        cursor.close();
      }
    }
  }

  @Override
  public void onCallBackAction(Integer integer, Object o, Object o1) {

  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    mTbsReaderView.onStop();
    if (mDownloadObserver != null) {
      getContentResolver().unregisterContentObserver(mDownloadObserver);
    }
  }

  @OnClick(R.id.btn_download)
  public void onViewClicked() {
    if (isLocalExist()) {
      mDownloadBtn.setVisibility(View.GONE);
      displayFile();
    } else {
      startDownload();
    }
  }

  private class DownloadObserver extends ContentObserver {

    private DownloadObserver(Handler handler) {
      super(handler);
    }

    @Override
    public void onChange(boolean selfChange, Uri uri) {
      Log.i("downloadUpdate: ", "onChange(boolean selfChange, Uri uri)");
      queryDownloadStatus();
    }
  }


}
