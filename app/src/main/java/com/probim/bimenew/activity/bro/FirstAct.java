package com.probim.bimenew.activity.bro;

import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.probim.bimenew.R;

public class FirstAct extends AppCompatActivity implements View.OnClickListener, CC.onRe {

    private CC cc;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_first);
        Button btn1 = findViewById(R.id.btn_1);
        btn1.setOnClickListener(this);
       // initBro();



    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_1:
                Intent intent = new Intent(this, MyService.class);
                // 适配Android 8.0
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    startForegroundService(intent);
                } else {
                    startService(intent);
                }
                //startActivity(new Intent(this, SecondAct.class));
                break;
            default:
                break;
        }
    }

    /**
     * 动态注册
     */
    private void initBro() {
        //包装Receiver
        IntentFilter intentFilter = new IntentFilter();
        //添加标志
        intentFilter.addAction("com.broadcast.user");
        cc = new CC();
        cc.setInter(this);
        //采用本地广播 安全
        LocalBroadcastManager mLocalBroadcastManager = LocalBroadcastManager.getInstance(this);
        //动态注册广播
        mLocalBroadcastManager.registerReceiver(cc, intentFilter);
    }

    @Override
    public void onCC(String s) {
        Toast.makeText(this, s, Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterReceiver(cc);
    }
}