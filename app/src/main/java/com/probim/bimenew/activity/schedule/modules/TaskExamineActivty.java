package com.probim.bimenew.activity.schedule.modules;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bigkoo.pickerview.view.TimePickerView;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.HorizontalRecyclerView;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.schedule.adapter.ExamSchedualNewTaskNoPlanListAdapter;
import com.probim.bimenew.activity.schedule.adapter.ExamSchedualNewTaskPlanListAdapter;
import com.probim.bimenew.activity.schedule.adapter.ExamSchedualPhotoAdapter;
import com.probim.bimenew.activity.schedule.adapter.SpinnerAdapter;
import com.probim.bimenew.activity.schedule.dto.SchedualBaseResult;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualNewPhotoDto;
import com.probim.bimenew.activity.schedule.dto.SchedualNewTaskNoPlanDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualNewTaskPlanDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualTaskAddDTO;
import com.probim.bimenew.activity.schedule.dto.SchedualTaskDetailsDTO;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.SchedualController;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.CalculateDaysInterval;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class TaskExamineActivty extends BaseActivity implements View.OnClickListener {
    private final int code_photo = 3000;
    private final int code_select_task = 1000;
    private TimePickerView pvTime;
    private TextView tvAddDate;
    private List<SchedualNewPhotoDto> photoDtoList = new ArrayList<>();
    private List<File> photoFileList = new ArrayList<>();
    private List<SchedualNewTaskPlanDTO> planList = new ArrayList<>();
    private List<SchedualNewTaskNoPlanDTO> noPlanList = new ArrayList<>();
    private ExamSchedualPhotoAdapter photoAdapter;
    private TextView tvTaskName;
    private TextView tvTaskDate;
    private RecyclerView rvTaskPhoto;
    private SchedualListDto.DataDTO planDto;
    private String creatDate;
    private int taskType = 0;
    private int clickListPosition;
    private List<String> photoNameList = new ArrayList<>();
    private RecyclerView rvPlan;
    private RecyclerView rvNoPlan;
    private ExamSchedualNewTaskPlanListAdapter planAdapter;
    private ExamSchedualNewTaskNoPlanListAdapter noPlanAdapter;
    private RelativeLayout rvSelectDate;
    private EditText edtConstruction;
    private boolean isClickActualratio;
    private int temp;
    private String itemTotalClick;

    private List<SchedualListDto.DataDTO> dtoList = new ArrayList<>();
    private TextView tvPlanName;

    private SpinnerAdapter spinnerAdapter;
    private Boolean isEditTask;
    private Spinner spinner;
    private LinearLayout linState;
    private SchedualPreviewListDTO.DataDTO schedualDto;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_schedual_examine);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
//        initKey();
        loadData();
        initRecycleview();
    }


    @Override
    protected void loadData() {
        if (Hawk.contains(CustomParam.SCHEDUAL_TIME_SELECTED)) {
            creatDate = Hawk.get(CustomParam.SCHEDUAL_TIME_SELECTED);
            tvAddDate.setText(creatDate);
        } else {
            tvAddDate.setText(creatDate);
        }
    }


    @Override
    protected void initView() {
        schedualDto = (SchedualPreviewListDTO.DataDTO) getIntent().getSerializableExtra("schedule-dto");
        getTaskDetials();
        ImageView ivUpdateState = findView(R.id.iv_up_state, this);
        isEditTask = getIntent().getBooleanExtra(CustomParam.isEditTask, false);
        TextView tvTittle = findView(R.id.tv_black_title);
        tvTittle.setText("今日填报-任务进度");
        LinearLayout linBack = findView(R.id.lin_back, this);

        tvPlanName = findView(R.id.tv_plan_name);

        tvAddDate = findView(R.id.tv_add_date);


        rvPlan = findView(R.id.rv_new_task_plan);
        rvNoPlan = findView(R.id.rv_new_task_non_plan);
        tvTaskName = findView(R.id.tv_task_name);
        tvTaskDate = findView(R.id.tv_task_date);
        rvTaskPhoto = findView(R.id.rv_task_photo);
        ImageView ivSave = findView(R.id.iv_save, this);
        ivSave.setVisibility(View.GONE);
        edtConstruction = findView(R.id.edt_construction);
        RelativeLayout rlBohui = findView(R.id.rl_bohui, this);
        RelativeLayout rlTongguo = findView(R.id.rl_tongguo, this);
        linState = findView(R.id.lin_state);
        if (schedualDto.getAuditStatus() == 1){
            linState.setVisibility(View.VISIBLE);
        }


    }


    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        planAdapter = new ExamSchedualNewTaskPlanListAdapter(planList);
        noPlanAdapter = new ExamSchedualNewTaskNoPlanListAdapter(noPlanList);
        VerticalRecyclerView.initialize(rvNoPlan).setAdapter(noPlanAdapter);
        VerticalRecyclerView.initialize(rvPlan).setAdapter(planAdapter);

        //初始化照片适配器
        photoAdapter = new ExamSchedualPhotoAdapter(photoDtoList, this);
        HorizontalRecyclerView.initialize(rvTaskPhoto).setAdapter(photoAdapter);
    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        Intent intent = null;
        switch (v.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.rl_select_date:
                break;
            case R.id.rl_add_schedual:
                break;
            case R.id.rl_add_photo:
                break;
            case R.id.iv_save:
                break;
            case R.id.rl_bohui:
                updateState("驳回待提交");
                break;
            case R.id.rl_tongguo:
                updateState("已审核");
                break;
            default:
                break;
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode != RESULT_OK) {
            return;
        }
        switch (requestCode) {

            case code_photo:

                for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {
                    File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));
                    File fileCompress = CompressHelper.getDefault(getApplicationContext())
                            .compressToFile(fileOrigin);
                    SchedualNewPhotoDto dto = new SchedualNewPhotoDto();
                    dto.setBf_path(fileCompress.getAbsolutePath());
                    dto.setBf_filename(fileCompress.getName());
                    photoDtoList.add(dto);
                    photoFileList.add(fileCompress);
//                    photoNameList.add(fileCompress.getName());
                    /*if (isSchedualDetials) {
                        photoDetailsDtoList.add(dto);
                        photoDetailsFileList.add(fileCompress);
                        photoDetailsNameList.add(fileCompress.getName());
                    }*/
                }
                /*linNoSelectPhoto.setVisibility(View.GONE);
                linSelectPhotoNewQuestion.setVisibility(View.VISIBLE);
                tvTvPhotoSize.setText(photoDtoList.size() + "");*/
                photoAdapter.notifyDataSetChanged();

                break;
            case code_select_task:
                SchedualListDto.DataDTO.ChildrenDTO taskDto = (SchedualListDto.DataDTO.ChildrenDTO) data.getExtras().getSerializable(CustomParam.StartForResult);
                itemTotalClick = data.getExtras().getString(CustomParam.PLAN_ITEM_TOTAL);
                Logger.t("层级数据").e(itemTotalClick);
                /**
                 *
                 int planTime = CalculateDaysInterval.calcu(dto.getProgress_planstarttime(),dto.getProgress_plannendtime());
                 int time = CalculateDaysInterval.calcu(dto.getProgress_planstarttime(),dto.getProgress_unittime());
                 float planp = time / planTime;

                 if (planp>=1){
                 holder.edtPlantratio.setText("100");
                 }else {
                 holder.edtPlantratio.setText(Float.toString(planp * 100));
                 }
                 *
                 */
                planList.get(clickListPosition).setProgress_MobilePlan(itemTotalClick);
                planList.get(clickListPosition).setProgress_Name(taskDto.getName());
                planList.get(clickListPosition).setProgress_planstarttime(taskDto.getStart().split("T")[0]);
                planList.get(clickListPosition).setProgress_plannendtime(taskDto.getFinish().split("T")[0]);
                float planTime = CalculateDaysInterval.calcu(planList.get(clickListPosition).getProgress_planstarttime(), planList.get(clickListPosition).getProgress_plannendtime());
                float time = CalculateDaysInterval.calcu(planList.get(clickListPosition).getProgress_planstarttime(), planList.get(clickListPosition).getProgress_unittime());
                if (planTime == 0) {
                    planTime = planTime + 1;
                }
                float planp = time / planTime;
                String planpStr;
                if (planp >= 1) {
                    planpStr = "100";
                    planList.get(clickListPosition).setProgress_planratio(planpStr);
                } else if (planp >= 0) {
                    planpStr = Math.floor(planp * 100) + "";
                    planList.get(clickListPosition).setProgress_planratio(planpStr);
                } else {
                    planpStr = "0";
                    planList.get(clickListPosition).setProgress_planratio(planpStr);
                }
                getTaskAdd(taskDto.getUID(), planpStr);
                planList.get(clickListPosition).setProgress_treeID(taskDto.getUID());
                planAdapter.notifyDataSetChanged();
                break;
            default:

                break;
        }
    }


    /**
     * 现场督导会详情
     */
    private void getTaskDetials() {
        mLoading.show();
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
//        parmas.put("Progress_ProjectID", schedualDto.getMobilePA_ProjectID());
//        parmas.put("Progress_createuserid", schedualDto.getMobilePA_Createuserid());
        parmas.put("Progress_unittime", Hawk.get(CustomParam.SCHEDUAL_TIME_SELECTED));
        schedualController.getTaskDetials(parmas, new CallBack<SchedualTaskDetailsDTO>() {
            @Override
            public void onSuccess(SchedualTaskDetailsDTO schedualTaskDetailsDTO) {
                if (schedualTaskDetailsDTO.getRet() == 1) {
                    photoDtoList.addAll(schedualTaskDetailsDTO.getData().getImage_Json());
                    photoAdapter.notifyDataSetChanged();
                    planList.addAll(schedualTaskDetailsDTO.getData().getPlan_json());
                    planAdapter.notifyDataSetChanged();
                    noPlanList.addAll(schedualTaskDetailsDTO.getData().getNoPlan_json());
                    noPlanAdapter.notifyDataSetChanged();
                    if (planList.size() > 0) {
                        tvPlanName.setText(planList.get(0).getProgress_Name());
                    }
                    if (noPlanList.size() > 0) {
                        tvPlanName.setText(noPlanList.get(0).getProgress_Name());
                    }
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }

    /**
     * 现场督导会详情
     */
    private void getTaskAdd(String id, String plan) {
        mLoading.show();
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("Progress_ProjectID", planDto.getUID());
        parmas.put("Progress_treeID", id);
        schedualController.getTaskAdd(parmas, new CallBack<SchedualTaskAddDTO>() {
            @Override
            public void onSuccess(SchedualTaskAddDTO schedualTaskAddDTO) {
                if (schedualTaskAddDTO.getRet() == 1) {
                    String temp;
                    if (TextUtils.isEmpty(schedualTaskAddDTO.getData())) {
                        temp = "0";
                    } else {
                        temp = schedualTaskAddDTO.getData();
                    }
                    planList.get(clickListPosition).setProgress_MobileAddratio(schedualTaskAddDTO.getData());


                    if (Float.parseFloat(plan) > Float.parseFloat(temp)) {
                        planList.get(clickListPosition).setProgress_state("滞后");
                    } else if (Float.parseFloat(plan) < Float.parseFloat(temp)) {
                        planList.get(clickListPosition).setProgress_state("超前");
                    } else {
                        planList.get(clickListPosition).setProgress_state("正常");
                    }


                    planAdapter.notifyDataSetChanged();
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }


    /**
     * 预览发布接口
     */
    private void updateState(String state) {
        SchedualPreviewListDTO.DataDTO dto = Hawk.get(CustomParam.SCHEDUAL_ITEM_SELECTED);
        SchedualController schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
//        parmas.put("Unittime", dto.getMobilePA_Unittime());
//        parmas.put("CreateuserId", dto.getMobilePA_Createuserid());
//        parmas.put("ProjectID", dto.getMobilePA_ProjectID());
        parmas.put("organizeId", schedualOrganizeId);
        parmas.put("State", state);
        parmas.put("Token", getToken());
        schedualController.updateState(parmas, new CallBack<SchedualBaseResult>() {
            @Override
            public void onSuccess(SchedualBaseResult schedualBaseResult) {
                if (schedualBaseResult.getRet() == 1) {
                    showMsg(schedualBaseResult.getData());
                    finish();
                } else {
                    showMsg(schedualBaseResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
}
