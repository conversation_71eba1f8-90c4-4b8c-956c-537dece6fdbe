package com.probim.bimenew.activity;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import android.view.View;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;

import java.util.List;

/**
 * Description :新建检查 定位界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/17/16:41.
 */

public class SelectModelForNewQuestionActivity extends BaseActivity {

  @BindView(R.id.tv_title) TextView tvTitle;
  private String projectID;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_select_model);
    ButterKnife.bind(this);
    loadData();
  }

  @Override protected void loadData() {
    /**
     * 从数据库获取数据
     */
    ProjectBeanDao dao = BaseApp.getInstance().getDao();
    List<ProjectBean> beanList = dao.loadAll();
    for (ProjectBean bean : beanList) {
      projectID = bean.getProjectID();
    }

//    replaceFragmnet(ModelSelectForNewQuestionFragment.newInstance(projectID));
    tvTitle.setText("定位");
  }

  @Override protected void initView() {

  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

  }

  @Override protected void initRefresh() {

  }

  /**
   * 切换 Fragment
   */

  private void replaceFragmnet(Fragment mFragment) {

    FragmentManager fragmentManager = getSupportFragmentManager();

    FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();

    fragmentTransaction.add(R.id.fragment_container, mFragment);

    fragmentTransaction.commit();
  }

  @OnClick({ R.id.lin_back, R.id.img_right }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.img_right:
        break;
    }
  }
}
