package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.ModelPointViewDetails;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.Base64Utils;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :模型--->视点详情
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/16/15:11.
 */
public class PointViewActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.tv_view_name)
  TextView tvViewName;
  @BindView(R.id.tv_view_intro)
  TextView tvViewIntro;
  @BindView(R.id.tv_view_create)
  TextView tvViewCreate;
  @BindView(R.id.tv_view_update)
  TextView tvViewUpdate;
  @BindView(R.id.btn_loading)
  Button btnLoading;
  @BindView(R.id.img)
  ImageView img;
  @BindView(R.id.img_right)
  ImageView imgRight;
  @BindView(R.id.tv_default)
  TextView tvDefault;
  private String projectId;
  private String viewId;
  private String modelId;
  private String moldeViewId;
  List<PhotoDto> photoDtoList = new ArrayList<>();

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_model_point_view);
    ButterKnife.bind(this);
    imgRight.setVisibility(View.VISIBLE);
    loadData();
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      projectId = bundle.getString(CustomParam.ProjectId);
      viewId = bundle.getString(CustomParam.ViewId);
      modelId = bundle.getString(CustomParam.ModelId);
      String title = bundle.getString(CustomParam.ViewPointName);
      tvTitle.setText(title);
      tvLeft.setText("视点");
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ViewpointID", viewId);
      ModelController controller = new ModelController();
      controller.GetViewPointDetails(params, new CallBack<ModelPointViewDetails>() {
        @Override
        public void onSuccess(ModelPointViewDetails modelPointViewDetails) {
          if (!TextUtils.isEmpty(modelPointViewDetails.getLinks().get(0).getContent())
              && modelPointViewDetails.getLinks().get(0).getContent()
              .contains("base64")) {

            Glide.with(PointViewActivity.this)
                .load(Base64Utils.decode(
                    modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]))
                .into(img);

            photoDtoList
                .add(new PhotoDto(
                    modelPointViewDetails.getLinks().get(0).getContent().split("base64,")[1]));
          } else {

            Glide.with(PointViewActivity.this)
                .load(R.mipmap.img_bg_project_list)
                .into(img);
          }

          tvViewName.setText(modelPointViewDetails.getName());
          if (TextUtils.isEmpty(modelPointViewDetails.getTag().getDescription())) {
            tvViewIntro.setText("<无描述>");
          } else {
            tvViewIntro.setText(modelPointViewDetails.getTag().getDescription());
          }
          tvViewCreate.setText(modelPointViewDetails.getEditor());
          tvViewUpdate.setText(modelPointViewDetails.getEditTime().replace("T", "  "));

          if (modelPointViewDetails.isIsDefault()) {

            tvDefault.setVisibility(View.VISIBLE);
          } else {

            tvDefault.setVisibility(View.INVISIBLE);
          }

          moldeViewId = modelPointViewDetails.getViewID();
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }
  }

  /**
   * 设置默认视点
   */
  private void SetDefault(String pId, String mId, String vId) {
    ModelController controller = new ModelController();
    HashMap<String, String> params = new HashMap<>();
    params.put("ProjectID", pId);
    params.put("ModelID", mId);
    params.put("ViewpointID", vId);
    controller.SetDefaultPointView(params, new CallBack<String>() {
      @Override
      public void onSuccess(String s) {

        if ("\"\"".equals(s)) {
          loadData();

        } else {
          showMsg(s);
        }

      }

      @Override
      public void onFail(String erroMsg) {

      }
    });

  }


  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({R.id.iv_back, R.id.lin_back, R.id.btn_loading, R.id.img_right, R.id.img})
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.btn_loading:
        loadView();
        break;
      case R.id.img_right:
        SetDefault(projectId, modelId, viewId);
        break;
      case R.id.img:
        Bundle bundle = new Bundle();
        bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
        Intent intent = new Intent(PointViewActivity.this, PhotoViewForPointViewActivity.class);
        intent.putExtras(bundle);
        startActivity(intent);
        break;
    }
  }

  private void loadView() {
    Bundle bundle = new Bundle();
    bundle.putString(CustomParam.ViewId, moldeViewId);
    bundle.putString(CustomParam.ProjectId, projectId);
    bundle.putString(CustomParam.ModelId, modelId);
    bundle.putString(CustomParam.ViewPointId, viewId);
    bundle.putString(CustomParam.TvLeft, "视点");
    Intent intent = new Intent(this, LoadModelViewActivity.class);
    intent.putExtras(bundle);
    startActivity(intent);

  }
}
