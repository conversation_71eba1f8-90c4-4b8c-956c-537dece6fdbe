package com.probim.bimenew.activity.check;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;

import java.util.List;

public class NewCheckPhotoAdapter extends RecyclerView.Adapter<NewCheckPhotoAdapter.VHolder> {
    private final Context mContext;
    private final List<String> photoDtoList;
    private final LayoutInflater layoutInflater;
    private onItemDeleteListener onItemDeleteListener;

    public NewCheckPhotoAdapter(Context mContext, List<String> list) {
        this.mContext = mContext;
        this.photoDtoList = list;
        layoutInflater = LayoutInflater.from(mContext);

    }


    @NonNull
    @Override
    public VHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new VHolder(layoutInflater.inflate(R.layout.item_new_check_photo, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VHolder vHolder, int i) {
        String url = photoDtoList.get(i);
        Glide.with(mContext).load(url).into(vHolder.igv);
        vHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        vHolder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemDeleteListener != null) {
                    onItemDeleteListener.onItemDeleteClick(i, url);
                }
            }
        });

    }


    @Override
    public int getItemCount() {
        return photoDtoList.size();
    }

    public void setOnItemDeteleListener(onItemDeleteListener listener) {

        this.onItemDeleteListener = listener;


    }

    public interface onItemDeleteListener {
        void onItemDeleteClick(int position, String url);

    }

    public class VHolder extends RecyclerView.ViewHolder {

        private final ImageView igv;
        private final ImageView ivDelete;

        public VHolder(@NonNull View itemView) {
            super(itemView);
            igv = itemView.findViewById(R.id.igv_check_photo);
            ivDelete = itemView.findViewById(R.id.iv_delete_photo);
        }
    }
}
