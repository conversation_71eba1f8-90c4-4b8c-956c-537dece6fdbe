package com.probim.bimenew.activity.bro;

import java.util.concurrent.atomic.AtomicInteger;


/**
 * int i = 3;
 * int a = i++; // a = 3, i = 4
 * int b = ++a; // b = 4, a = 4
 */
public class TestCAS {

    int x;

    private final AtomicInteger atomicCount = new AtomicInteger(0);
    private final int i = 0;

    public static void main(String[] args) {

      /*  Counter cas = new Counter();
        List<Thread> ts = new ArrayList<>(500);
        long start = System.currentTimeMillis();
        for (int j = 0; j < 100; j++) {
            Thread t = new Thread(() -> {
                for (int i = 0; i < 10000; i++) {
                    cas.safeCount();
                }
            });
            ts.add(t);
        }
        for (Thread t : ts) {
            t.start();
        }
        for (Thread t : ts) {
            try {
                t.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        System.out.println(cas.i);
        System.out.println(cas.atomicCount.get());
        System.out.println(System.currentTimeMillis() - start);
    }*/
    }

    /**
     * cas cafecount
     **/
    private void safeCount() {
        for (; ; ) {
            int i = atomicCount.get();
            boolean suc = atomicCount.compareAndSet(i, ++i);
            if (suc) {
                break;
            }
        }
    }
}

