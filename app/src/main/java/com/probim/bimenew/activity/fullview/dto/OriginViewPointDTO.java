package com.probim.bimenew.activity.fullview.dto;

import java.util.List;

public class OriginViewPointDTO {

    private CameraPositionDTO cameraPosition;
    private Object sectionBox;
    private CameraRotationDTO cameraRotation;
    private ControlPostionDTO controlPostion;
    private List<?> selectedElements;
    private List<?> hiddenElements;
    private List<?> isolatedElements;
    private String markupmsg;
    private List<?> coloredElements;
    private FocalOffsetDTO focalOffset;

    public CameraPositionDTO getCameraPosition() {
        return cameraPosition;
    }

    public void setCameraPosition(CameraPositionDTO cameraPosition) {
        this.cameraPosition = cameraPosition;
    }

    public Object getSectionBox() {
        return sectionBox;
    }

    public void setSectionBox(Object sectionBox) {
        this.sectionBox = sectionBox;
    }

    public CameraRotationDTO getCameraRotation() {
        return cameraRotation;
    }

    public void setCameraRotation(CameraRotationDTO cameraRotation) {
        this.cameraRotation = cameraRotation;
    }

    public ControlPostionDTO getControlPostion() {
        return controlPostion;
    }

    public void setControlPostion(ControlPostionDTO controlPostion) {
        this.controlPostion = controlPostion;
    }

    public List<?> getSelectedElements() {
        return selectedElements;
    }

    public void setSelectedElements(List<?> selectedElements) {
        this.selectedElements = selectedElements;
    }

    public List<?> getHiddenElements() {
        return hiddenElements;
    }

    public void setHiddenElements(List<?> hiddenElements) {
        this.hiddenElements = hiddenElements;
    }

    public List<?> getIsolatedElements() {
        return isolatedElements;
    }

    public void setIsolatedElements(List<?> isolatedElements) {
        this.isolatedElements = isolatedElements;
    }

    public String getMarkupmsg() {
        return markupmsg;
    }

    public void setMarkupmsg(String markupmsg) {
        this.markupmsg = markupmsg;
    }

    public List<?> getColoredElements() {
        return coloredElements;
    }

    public void setColoredElements(List<?> coloredElements) {
        this.coloredElements = coloredElements;
    }

    public FocalOffsetDTO getFocalOffset() {
        return focalOffset;
    }

    public void setFocalOffset(FocalOffsetDTO focalOffset) {
        this.focalOffset = focalOffset;
    }

    public static class CameraPositionDTO {
        private Double x;
        private Double y;
        private Double z;

        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getZ() {
            return z;
        }

        public void setZ(Double z) {
            this.z = z;
        }
    }

    public static class CameraRotationDTO {
        private Double _x;
        private Double _y;
        private Double _z;
        private String _order;

        public Double get_x() {
            return _x;
        }

        public void set_x(Double _x) {
            this._x = _x;
        }

        public Double get_y() {
            return _y;
        }

        public void set_y(Double _y) {
            this._y = _y;
        }

        public Double get_z() {
            return _z;
        }

        public void set_z(Double _z) {
            this._z = _z;
        }

        public String get_order() {
            return _order;
        }

        public void set_order(String _order) {
            this._order = _order;
        }
    }

    public static class ControlPostionDTO {
        private Double x;
        private Double y;
        private Double z;

        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getZ() {
            return z;
        }

        public void setZ(Double z) {
            this.z = z;
        }
    }

    public static class FocalOffsetDTO {
        private Double x;
        private Double y;
        private Double z;

        public Double getX() {
            return x;
        }

        public void setX(Double x) {
            this.x = x;
        }

        public Double getY() {
            return y;
        }

        public void setY(Double y) {
            this.y = y;
        }

        public Double getZ() {
            return z;
        }

        public void setZ(Double z) {
            this.z = z;
        }
    }
}
