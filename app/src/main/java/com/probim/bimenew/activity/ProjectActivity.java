package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;

import com.chaychan.library.BottomBarLayout;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.MainTabAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ProjectController;
import com.probim.bimenew.dto.AuthorityDto;
import com.probim.bimenew.dto.ModelStageDto;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.fragment.project.AllFullviewFragment;
import com.probim.bimenew.fragment.project.BusinessFragment;
import com.probim.bimenew.fragment.project.DocumentFragment;
import com.probim.bimenew.fragment.project.IssueFragment;
import com.probim.bimenew.fragment.project.newModel.NewModelFragment;
import com.probim.bimenew.model.Authority;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.ProjectIDResult;
import com.probim.bimenew.result.ProjectListResult;
import com.probim.bimenew.utils.view.NoScrollViewPager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description : Author : Gary Email : <EMAIL> Date : 2018/7/9/14:11.
 */
public class ProjectActivity extends BaseActivity {

    @BindView(R.id.vp_content)
    NoScrollViewPager vpContent;
    @BindView(R.id.bottom_bar)
    BottomBarLayout bottomBar;

    private List<BaseFragment> mFragments;
    private MainTabAdapter mTabAdapter;
    private String organizeId;
    private boolean project_public;
    private String projectId;
    private String projectName;
    private boolean DocAuthority = false;
    private boolean ProblemAuthority = false;
    private boolean ModelAuthority = false;
    private boolean AddProblemAuthority = false;
    private boolean CheckAuthority = true;
    private ProjectController controller;
    private boolean isCanSubmit;
    private boolean isCanExamine;

    private boolean isCanAdd;

    private List<ModelStageDto> allStages = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_project);
        ButterKnife.bind(this);
        Hawk.put("isCanSubmit", false);
        Hawk.put("isCanExamine", false);
        Hawk.put("isCanAdd", false);
        initData();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {

    }

    @Override
    protected void initData() {
        if (getIntent() != null) {
            Intent intent = getIntent();
            // 读取数据
            Bundle bundle = intent.getExtras();
            ProjectListResult.DataBean.RowsBean bean = (ProjectListResult.DataBean.RowsBean) bundle.getSerializable(CustomParam.PROJECT);
            projectName = bean.getProjectName();
            projectId = bean.getProjectId();
            organizeId = bean.getOrganizeId();
            project_public = bean.getIsPublic();
//            project_public = 0 != isPublic;
            /*String projectName = dataBean.getFullname();
            String projectId = dataBean.getBimcomposerid();
            String organizeId = dataBean.getOrganizeid();*/

            //getAu();
            controller = new ProjectController();
            getAuthority();
            getProjectID();
        }
    }

    /**
     * 通过项目ID获取机构ID
     */
    private void getProjectID() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", organizeId);
        controller.getProject(params, new CallBack<ProjectIDResult>() {
            @Override
            public void onSuccess(ProjectIDResult projectIDResult) {
                Hawk.put(CustomParam.PROJECTIDSSS, projectIDResult.getData());
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    private void initLisenter(String projectID, String projectName, String organizeId) {
        mFragments = new ArrayList<>();
        mFragments.add(NewModelFragment.newInstance(projectID, projectName, true, allStages));
        mFragments.add(IssueFragment.newInstance(projectID, projectName, ProblemAuthority, AddProblemAuthority));
        mFragments.add(AllFullviewFragment.newInstance(projectID, projectName, true, true));
        mFragments.add(BusinessFragment.newInstance(projectID, projectName, CheckAuthority));
        mFragments.add(DocumentFragment.newInstance(projectID, projectName, organizeId, DocAuthority));
//        mFragments.add(QualityFragment.newInstance(projectID, projectName, CheckAuthority));
//        mFragments.add(SafeFragment.newInstance(projectID, projectName, CheckAuthority));
//        mFragments.add(AllSchedualListFragment.newInstance(projectID, projectName, true));
        mTabAdapter = new MainTabAdapter(mFragments, getSupportFragmentManager());
        vpContent.setAdapter(mTabAdapter);
        vpContent.setOffscreenPageLimit(mFragments.size());
        bottomBar.setViewPager(vpContent);
    }

    private void getAu() {
        ProjectController controller = new ProjectController();
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", Hawk.get(CustomParam.UserId));
        params.put("projectId", organizeId);
        params.put("isPublic", project_public + "");

        controller.getAuthorize(params, new CallBack<List<Authority>>() {
            @Override
            public void onSuccess(List<Authority> authorities) {

                if (authorities.isEmpty()) {
                    DocAuthority = false;
                    ProblemAuthority = false;

                    // CustomParam.Authority =false;
                    initLisenter(projectId, projectName, organizeId);
                    return;
                }
                for (Authority authority : authorities) {

                    if ("IssueTracking".equals(authority.getModuleCode())) {

                        // 判断问题是否可见
                        if (authority.getAccesses().isEmpty()) {
                            // 问题不可见
                            ProblemAuthority = false;
                            AddProblemAuthority = false;
                        } else {
                            // 问题可见
                            ProblemAuthority = true;

                            for (Authority.AccessesBean accessesBean : authority.getAccesses()) {

                                if ("lr-add".equals(accessesBean.getModuleButtonCode())) {
                                    // 新建问题
                                    AddProblemAuthority = true;
                                }
                            }
                        }
                    } else if ("Document".equals(authority.getModuleCode())) {

                        // 判断文档是否可见

                        //不可见
                        //可见
                        DocAuthority = !authority.getAccesses().isEmpty();
                    } else if ("BIMModel".equals(authority.getModuleCode())) {

                        //不可见
                        //可见
                        ModelAuthority = !authority.getAccesses().isEmpty();
                    }
                }
                initLisenter(projectId, projectName, organizeId);
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取项目权限
     */
    private void getAuthority() {
        HashMap<String, String> params = new HashMap<>();
        params.put("token", Hawk.get(CustomParam.Token));
        params.put("organizeId", projectId);
        params.put("parentId", "0");
        controller.getAuthority(params, new CallBack<AuthorityDto>() {
            @Override
            public void onSuccess(AuthorityDto authorityDto) {
                if (authorityDto.getRet() == 1) {
                    for (AuthorityDto.DataDTO dataDto : authorityDto.getData()) {
                        if (dataDto.getMenuCode().equals("JDGL")) {
                            for (int i = 0; i < dataDto.getChildren().size(); i++) {
                                if (dataDto.getChildren().get(i).getMenuCode().equals("JDTB")) {
                                    for (int j = 0; j < dataDto.getChildren().get(i).getButtons().size(); j++) {
                                        // 新增/发布
                                        if (dataDto.getChildren().get(i).getButtons().get(j).getButtonCode().equals("JDTB_SubmitAudit")) {
                                            isCanAdd = true;
                                            Hawk.put("isCanAdd", true);
                                        }
                                        // 审核
                                        if (dataDto.getChildren().get(i).getButtons().get(j).getButtonCode().equals("JDTB_Audit")) {
                                            isCanExamine = true;
                                            Hawk.put("isCanExamine", true);
                                        }
                                    }
                                }
                            }
                        }
                        if (dataDto.getMenuCode().equals("MODEL")) {
                            // 模型数据
                            if (!dataDto.getChildren().isEmpty()) {
                                handleData(dataDto.getChildren(), new ModelStageDto());
                            } else {
                                initLisenter(projectId, projectName, organizeId);
                            }
                        }
                    }
                }
                /*if (isCanUpload) {
                    // 审核
                    if (isCanSubmit) {
                        // 提交
                        Hawk.put(CustomParam.isCanUploadTask, false);
                        Logger.t("权限").e("无审核权限");
                    } else {
                        Hawk.put(CustomParam.isCanUploadTask, true);
                        Logger.t("权限").e("有审核权限");
                    }
                } else {
                    Hawk.put(CustomParam.isCanUploadTask, false);
                }*/

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 递归
     *
     * @param dto
     * @param modelStageDto
     */
    private void handleData(List<AuthorityDto.DataDTO.ChildrenDTO> dto, ModelStageDto modelStageDto) {

        for (AuthorityDto.DataDTO.ChildrenDTO childrenDTO : dto) {
            ModelStageDto stageDto = new ModelStageDto();
            List<String> stringList = new ArrayList<>();
            stringList.add(childrenDTO.getBusinessCode());
            stageDto.setCode(childrenDTO.getBusinessCode());
            stageDto.setData(stringList);
            stageDto.setName(childrenDTO.getMenuName());

            if (modelStageDto.getCode() != null) {
                modelStageDto.getData().add(stageDto.getCode());
            } else {
                allStages.add(stageDto);
            }

            if (!childrenDTO.getChildren().isEmpty()) {
                if (modelStageDto.getCode() == null) {
                    handleData(childrenDTO.getChildren(), stageDto);
                } else {
                    handleData(childrenDTO.getChildren(), modelStageDto);
                }

            }
        }
        initLisenter(projectId, projectName, organizeId);
    }
}
