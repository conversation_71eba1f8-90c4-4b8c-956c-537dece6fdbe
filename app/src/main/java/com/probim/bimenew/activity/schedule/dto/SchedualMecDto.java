package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SchedualMecDto {

    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("Materials_ID")
        private int materials_ID;
        @SerializedName("Token")
        private Object token;
        @SerializedName("Materials_Name")
        private String materials_Name;
        @SerializedName("Materials_PlanNum")
        private int materials_PlanNum;
        @SerializedName("Materials_Unit")
        private String materials_Unit;
        @SerializedName("Materials_Size")
        private String materials_Size;
        @SerializedName("Materials_Method")
        private String materials_Method;
        private String organizeId;

        public int getMaterials_ID() {
            return materials_ID;
        }

        public void setMaterials_ID(int materials_ID) {
            this.materials_ID = materials_ID;
        }

        public Object getToken() {
            return token;
        }

        public void setToken(Object token) {
            this.token = token;
        }

        public String getMaterials_Name() {
            return materials_Name;
        }

        public void setMaterials_Name(String materials_Name) {
            this.materials_Name = materials_Name;
        }

        public int getMaterials_PlanNum() {
            return materials_PlanNum;
        }

        public void setMaterials_PlanNum(int materials_PlanNum) {
            this.materials_PlanNum = materials_PlanNum;
        }

        public String getMaterials_Unit() {
            return materials_Unit;
        }

        public void setMaterials_Unit(String materials_Unit) {
            this.materials_Unit = materials_Unit;
        }

        public String getMaterials_Size() {
            return materials_Size;
        }

        public void setMaterials_Size(String materials_Size) {
            this.materials_Size = materials_Size;
        }

        public String getMaterials_Method() {
            return materials_Method;
        }

        public void setMaterials_Method(String materials_Method) {
            this.materials_Method = materials_Method;
        }

        public String getOrganizeId() {
            return organizeId;
        }

        public void setOrganizeId(String organizeId) {
            this.organizeId = organizeId;
        }
    }
}
