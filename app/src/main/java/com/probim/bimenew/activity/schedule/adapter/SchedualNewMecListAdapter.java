package com.probim.bimenew.activity.schedule.adapter;

import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.interfaces.IOnItemSchedualMecClickListener;
import com.probim.bimenew.activity.schedule.dto.SchedualNewMecDTO;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualNewMecListAdapter extends RecyclerView.Adapter<SchedualNewMecListAdapter.ViewHolder> {

    private List<SchedualNewMecDTO> dtoList = new ArrayList<>();
    private IOnItemSchedualMecClickListener onItemClickListener;
    private Handler handler = new Handler();

    public SchedualNewMecListAdapter(List<SchedualNewMecDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_mechanical, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewMecDTO dto = dtoList.get(position);
        int xx = position + 1;
        holder.tvTitle.setText(" 机械" + xx);
        holder.tvName.setText(dto.getMaterialsDetial_Name());
        if (holder.tvName.getText().equals("其他")) {
            holder.edtJx.setVisibility(View.VISIBLE);
        } else {
            holder.edtJx.setVisibility(View.GONE);
        }
        holder.edtPlan.setText(dto.getMaterialsDetial_PlanNum() + "");
        holder.edtChange.setText(dto.getMaterialsDetial_AdddelNum() + "");
        holder.edtLive.setText(dto.getMaterialsDetial_NewNum() + "");
        holder.edtLeiji.setText(dto.getMaterialsDetial_AddNum() + "");
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
        holder.rlSelectMec.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onMecClick(position, dto);
                }
            }
        });
        holder.edtChange.clearFocus();
        holder.edtChange.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        Runnable runnable = new Runnable() {
                            @Override
                            public void run() {
                                //结束后进行操作
                                onItemClickListener.showAddChange(position, editable.toString());
                            }
                        };
                        handler.postDelayed(runnable, 1500);

                    }

                }

            }
        });

        holder.edtLive.clearFocus();
        holder.edtLive.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        Runnable runnable = new Runnable() {
                            @Override
                            public void run() {
                                //结束后进行操作
                                onItemClickListener.showLiveChange(position, editable.toString());
                            }
                        };
                        handler.postDelayed(runnable, 1500);

                    }

                }


            }
        });
        holder.edtLeiji.clearFocus();
        holder.edtLeiji.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showLeijiChange(position, editable.toString());
                }

            }
        });
        holder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onDelete(position, dto);
            }
        });
        holder.edtJx.clearFocus();
        holder.edtJx.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showEdt(position, editable.toString());
                }


            }
        });

    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualMecClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_mec_name)
        TextView tvName;
        @BindView(R.id.edt_plan)
        TextView edtPlan;
        @BindView(R.id.edt_change)
        TextView edtChange;
        @BindView(R.id.edt_live)
        TextView edtLive;
        @BindView(R.id.edt_leiji)
        TextView edtLeiji;
        @BindView(R.id.tv_mec_title)
        TextView tvTitle;
        @BindView(R.id.rl_select_mec)
        RelativeLayout rlSelectMec;
        @BindView(R.id.iv_delete_item)
        ImageView ivDelete;
        @BindView(R.id.edt_jixie)
        EditText edtJx;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
