package com.probim.bimenew.activity.check;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.NewCheckActivity;
import com.probim.bimenew.adapter.AllMaterialAdapter;
import com.probim.bimenew.adapter.MaterialItemAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllMaterialListBean;
import com.probim.bimenew.result.MateriaListBean;
import com.probim.bimenew.result.MateriaOriginListBean;
import com.probim.bimenew.utils.GsonSingleton;
import com.probim.bimenew.utils.JsonHelper;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :构件列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class AllMaterialListActivity extends BaseActivity implements View.OnClickListener {

    private final List<List<AllMaterialListBean.DataBean.ListBean>> allList = new ArrayList<>();
    private final List<AllMaterialListBean.DataBean.ListBean> currentList = new ArrayList<>();
    private final List<MateriaListBean.DataBean.ListBean> allItemList = new ArrayList<>();
    private final List<MateriaOriginListBean.DataBean.ListBean> originList = new ArrayList<>();
    private String organizeId;
    private AllMaterialAdapter allAdapter;
    private int index = -1;
    private RecyclerView rvProjectStructure;
    private boolean isFirst = true;
    private boolean isOnItem = false;
    private String projectId;
    private boolean isDetails;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_project_structure);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
        GetProjectDao();
        getMaterialList("");
    }

    @Override
    protected void initView() {
        if (getIntent() != null) {
            isDetails = getIntent().getBooleanExtra("isCheckDetails", false);
        }
        TextView tvTittle = findViewById(R.id.tv_black_title);
        tvTittle.setText("构件列表");
        TextView tvRight = findView(R.id.tv_right, this);
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("完成");
        LinearLayout liniBack = findView(R.id.lin_back, this);

        //初始化全部文件夹适配器
        setAllAdapter();
    }

    /**
     * 获取文件夹下数据
     */
    private void getMaterialItemList(String guid) {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        List<String> stringList = new ArrayList<>();
        params.put("token", Hawk.get(CustomParam.Token));
        params.put("organizeId", projectId);
        params.put("PageNum", "1");
        params.put("PageSize", "999");
        params.put("bc_guid_materialtype", guid);
        params.put("bm_materialcode", "");
        params.put("bm_materialname", "");
        params.put("statusIdlistjson", "");
        params.put("ifhasrelation", "");
        params.put("updatetimestart", "");
        params.put("updatetimeend", "");
        params.put("SortField", "bm_materialcode");
//        params.put("SortType", "desc");
        // params.put("likepara", (isFirstFloor) ? "" : dataBean.getEc_code());
        CheckController checkController = new CheckController();
        checkController.getMateriaList2(params, new CallBack<MateriaListBean>() {

            @Override
            public void onSuccess(MateriaListBean materiaListBean) {
                if (materiaListBean.getData().getList().isEmpty()) {

                    // 空值处理
                    showMsg("暂无数据");

                } else {
                    isOnItem = true;
                    index++;
                    originList.addAll(GsonSingleton.INSTANCE.jsonTobean(JsonHelper.toJson(materiaListBean),
                            MateriaOriginListBean.class).getData().getList());

                    // 循环遍历数据 设置isSelected 值为false
                    for (MateriaListBean.DataBean.ListBean bean : materiaListBean.getData().getList()) {
                        bean.setSelected(false);
                    }
                    allItemList.addAll(materiaListBean.getData().getList());
                    // 替换新的适配器  解决数据不统一问题
                    setItemNewAdapter();
                    //allList.addAll(materiaListBean.getData().getList());
                    //adapter.notifyDataSetChanged();
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 获取构件列表
     */
    private void getMaterialList(String code) {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token",Hawk.get(CustomParam.Token));
        params.put("organizeId", projectId);
        params.put("basecode", code);
        CheckController checkController = new CheckController();
        checkController.getMateriaList(params, new CallBack<AllMaterialListBean>() {

            @Override
            public void onSuccess(AllMaterialListBean materiaListBean) {
                if (materiaListBean.getData().getList().isEmpty()) {

                    // 空值处理

                } else {

                    //角标
                    index++;

                    // 循环遍历数据 设置isSelected 值为false
                    for (AllMaterialListBean.DataBean.ListBean bean : materiaListBean.getData().getList()) {
                        bean.setSelected(false);
                    }

                    //储存所有数据
                    allList.add(materiaListBean.getData().getList());

                    //清除当前数据

                    currentList.clear();
                    currentList.addAll(materiaListBean.getData().getList());
                    allAdapter.notifyDataSetChanged();
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();
            projectId = bean.getProjectID();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.lin_back:

                backStepToList();

                break;
            case R.id.tv_right:
                Intent intent = null;
                if (isDetails) {
                    intent = new Intent(this, CheckDetailsActivity.class);
                } else {
                    intent = new Intent(this, NewCheckActivity.class);
                }
                Bundle bundle = new Bundle();
                bundle.putSerializable(CustomParam.StartForResult, (Serializable) getSelectedList());
                //intent.putExtra(CustomParam.StartForResult, getSelectedList());
                intent.putExtras(bundle);
                setResult(RESULT_OK, intent);
                finish();
                break;
        }
    }

    /**
     * 获取选中数据
     */
    private List<MateriaOriginListBean.DataBean.ListBean> getSelectedList() {

        List<MateriaOriginListBean.DataBean.ListBean> isSelectedAllList = new ArrayList<>();

        for (int i = 0; i < allItemList.size(); i++) {

            if (allItemList.get(i).getSelected()) {

                isSelectedAllList.add(originList.get(i));
            }
        }
        return isSelectedAllList;
    }

    /**
     * 设置新的适配器
     */
    private void setItemNewAdapter() {

        MaterialItemAdapter itemAdapter = new MaterialItemAdapter(this, allItemList);
        VerticalRecyclerView.initialize(rvProjectStructure).setAdapter(itemAdapter);
        itemAdapter.addRecycleItemListener(new MaterialItemAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {

            }
        });
        rvProjectStructure.setAdapter(itemAdapter);
    }

    /**
     * 设置文件夹适配器
     */
    private void setAllAdapter() {

        rvProjectStructure = findViewById(R.id.rv_project_structure);
        allAdapter = new AllMaterialAdapter(this, currentList);
        VerticalRecyclerView.initialize(rvProjectStructure).setAdapter(allAdapter);
        allAdapter.addRecycleItemListener(new AllMaterialAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                AllMaterialListBean.DataBean.ListBean bean = (AllMaterialListBean.DataBean.ListBean) o;
                String code = bean.getBmc_code();
                int isHasFolder = bean.getDirectChildrenCount();
                int itemCount = bean.getChildrenItemCount();
                String guid = bean.getBmc_guid();
                //
                if (isHasFolder != 0 && itemCount != 0) {
                    //获取文件夹数据
                    getMaterialList(code);
                } else {
                    //获取文件夹下数据
                    getMaterialItemList(guid);
                }
            }
        });
        rvProjectStructure.setAdapter(allAdapter);
    }

    /**
     * 处理后退数据展示
     */
    private void backStepToList() {

        if (isOnItem) {
            index--;
            setAllAdapter();
            currentList.clear();
            currentList.addAll(allList.get(index));
            allAdapter.notifyDataSetChanged();
            isOnItem = false;

        } else {

            if (index == 0 || index == -1) {
                finish();
                return;

            }
            allList.remove(index);
            index--;
            currentList.clear();
            currentList.addAll(allList.get(index));
            allAdapter.notifyDataSetChanged();


        }


       /*
        if (isOnItem) {
            setAllAdapter();
            currentList.clear();
            currentList.addAll(allList.get(index));
            allAdapter.notifyDataSetChanged();
            allList.remove(index);
            isOnItem = false;

        } else {
            index--;
            if (index == -1) {
                finish();
                return;
            }
            currentList.clear();
            currentList.addAll(allList.get(index));
            allAdapter.notifyDataSetChanged();
            if (index!=0) {
                allList.remove(index);
            }
        }*/
       /* if (isFirst) {
            setAllAdapter();
        }

        if (!isOnItem) {
            if (index == 0) {
                finish();
                return;
            }
            index--;
            //showMsg(index + "");
            currentList.clear();
            currentList.addAll(allList.get(index));
            allAdapter.notifyDataSetChanged();
            allList.remove(index);
            isFirst = false;
        } else {
            if (index < 0) {
                finish();
                return;
            }
            //showMsg(index + "");
            currentList.clear();
            currentList.addAll(allList.get(index));
            allAdapter.notifyDataSetChanged();
            allList.remove(index);
            isFirst = false;
            index--;
        }*/

   /* if (isOnItem) {

      if (index < 0) {
        finish();
      } else {
        isOnItem = false;
        currentList.clear();
        currentList.addAll(allList.get(index));
        allAdapter.notifyDataSetChanged();
        allList.remove(index);
        isFirst = false;
        index--;
      }
    } else {

      if (index <= 0) {
        finish();
      } else {
        index--;
        currentList.clear();
        currentList.addAll(allList.get(index));
        allAdapter.notifyDataSetChanged();
        allList.remove(index);
        isFirst = false;

      }
    }*/
    }
}
