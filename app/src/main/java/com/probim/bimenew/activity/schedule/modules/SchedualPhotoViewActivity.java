package com.probim.bimenew.activity.schedule.modules;

import android.os.Bundle;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.viewpager.widget.ViewPager;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.schedule.adapter.SchedualPhotoPagerAdapter;
import com.probim.bimenew.activity.schedule.dto.SchedualNewPhotoDto;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.utils.DimenTool;
import com.probim.bimenew.utils.view.ViewPagerFixed;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description : 照片浏览界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/23/15:16.
 */
public class SchedualPhotoViewActivity extends BaseActivity {

  @BindView(R.id.photoview_pager)
  ViewPagerFixed mPager;
  @BindView(R.id.viewtab_layout)
  LinearLayout mViewTabLayout;

  private List<SchedualNewPhotoDto> list = new ArrayList<>();
  private ArrayList<ImageView> pointPics = new ArrayList<>();
  private int current;

  @Override
  protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_photo_view);
    ButterKnife.bind(this);

    if (getIntent() == null) {
      return;
    }
    //图片数据
    list = (List<SchedualNewPhotoDto>) getIntent().getSerializableExtra(CustomParam.StartPhoto);
    current = getIntent().getIntExtra(CustomParam.PhotoPosition, -1);
    mPager.setPageMargin((int) (getResources().getDisplayMetrics().density * 15));
    SchedualPhotoPagerAdapter adapter = new SchedualPhotoPagerAdapter(list, this, mPager);
    mPager.setAdapter(adapter);
    adapter.setListener(new SchedualPhotoPagerAdapter.IonItemclick() {
      @Override
      public void ImyClick() {
        finish();
      }
    });
    addPoints();
    mPager.setCurrentItem(current);

    setIndicator(current);

    mPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
      @Override
      public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

      }

      @Override
      public void onPageSelected(int position) {
        setIndicator(position);
      }

      @Override
      public void onPageScrollStateChanged(int state) {

      }
    });
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }


  private void addPoints() {
    LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
        LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
    for (int i = 0; i < list.size(); i++) {
      // 导航点
      ImageView iv_point = new ImageView(this);
      iv_point.setMinimumHeight(DimenTool.dip2px(this, 10));
      iv_point.setMinimumWidth(DimenTool.dip2px(this, 10));
      // 设置margin属性
      lp.setMargins(DimenTool.dip2px(this, 5), 0, 0, 0);
      iv_point.setLayoutParams(lp);
      // 将导航点加入到LinearLayout里面
      mViewTabLayout.addView(iv_point);
      pointPics.add(iv_point);
    }
  }

  private void setIndicator(int position) {
    int index = position % list.size();
    for (int i = 0; i < list.size(); i++) {
      pointPics.get(i).setImageResource(R.drawable.turck_dot_normal);
      if (index == i) {
        pointPics.get(i).setImageResource(R.drawable.turck_dot_focused);
      }
    }
  }
}
