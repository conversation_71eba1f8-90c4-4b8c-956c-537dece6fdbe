package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualStateListDTO {


    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private DataDTO data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataDTO getData() {
        return data;
    }

    public void setData(DataDTO data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("Progress_MobileState")
        private String progress_MobileState;
        @SerializedName("MobileUserDetial_state")
        private String mobileUserDetial_state;
        @SerializedName("MaterialsDetial_state")
        private String materialsDetial_state;
        @SerializedName("MachineDetial_State")
        private String machineDetial_State;
        @SerializedName("MobileSafe_state")
        private String mobileSafe_state;
        @SerializedName("MobileSupervise_state")
        private String mobileSupervise_state;

        public String getProgress_MobileState() {
            return progress_MobileState;
        }

        public void setProgress_MobileState(String progress_MobileState) {
            this.progress_MobileState = progress_MobileState;
        }

        public String getMobileUserDetial_state() {
            return mobileUserDetial_state;
        }

        public void setMobileUserDetial_state(String mobileUserDetial_state) {
            this.mobileUserDetial_state = mobileUserDetial_state;
        }

        public String getMaterialsDetial_state() {
            return materialsDetial_state;
        }

        public void setMaterialsDetial_state(String materialsDetial_state) {
            this.materialsDetial_state = materialsDetial_state;
        }

        public String getMachineDetial_State() {
            return machineDetial_State;
        }

        public void setMachineDetial_State(String machineDetial_State) {
            this.machineDetial_State = machineDetial_State;
        }

        public String getMobileSafe_state() {
            return mobileSafe_state;
        }

        public void setMobileSafe_state(String mobileSafe_state) {
            this.mobileSafe_state = mobileSafe_state;
        }

        public String getMobileSupervise_state() {
            return mobileSupervise_state;
        }

        public void setMobileSupervise_state(String mobileSupervise_state) {
            this.mobileSupervise_state = mobileSupervise_state;
        }
    }
}
