package com.probim.bimenew.activity;

import android.os.Bundle;
import androidx.viewpager.widget.ViewPager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.PhotoPager2Adapter;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.utils.DataHolder;
import com.probim.bimenew.utils.view.ViewPagerFixed;
import java.util.ArrayList;
import java.util.List;

/**
 * Description : 照片浏览界面 Author : Gary <PERSON>  : <EMAIL> Date   :
 * 2018/8/23/15:16.
 */
public class NewQuestionPhotoActivity extends BaseActivity {

  @BindView(R.id.photoview_pager)
  ViewPagerFixed mPager;
  @BindView(R.id.viewtab_layout)
  LinearLayout mViewTabLayout;

  private List<PhotoDto> list = new ArrayList<>();
  private ArrayList<ImageView> pointPics = new ArrayList<>();
  private int current;

  @Override
  protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_photo_view);
    ButterKnife.bind(this);
    list = DataHolder.getInstance().getData();
    mPager.setPageMargin((int) (getResources().getDisplayMetrics().density * 15));
    PhotoPager2Adapter adapter = new PhotoPager2Adapter(list, this, mPager);
    mPager.setAdapter(adapter);
    adapter.setListener(new PhotoPager2Adapter.IonItemclick() {
      @Override
      public void ImyClick() {
        finish();
      }
    });
    mPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
      @Override
      public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

      }

      @Override
      public void onPageSelected(int position) {

      }

      @Override
      public void onPageScrollStateChanged(int state) {

      }
    });
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }


}
