package com.probim.bimenew.activity;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.LoginController;
import com.probim.bimenew.model.Company;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.ISwipe;
import com.probim.bimenew.utils.view.EasySwipeMenuLayout;
import com.probim.bimenew.utils.view.fontview.FontEdtTextView;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

/**
 * Description :输入企业编码
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/6/11/15:12
 */
public class EdtCompanyCodeActivity extends BaseActivity implements View.OnClickListener {

    private ImageView ivCompany;
    private FontEdtTextView edtCompanyCode;
    private TextView tvCompany;
    private LinearLayout linEdtCode;
    private EasySwipeMenuLayout easySwipeMenuLayout;
    private boolean isEdtCode;
    private ImageView ivSelfSelected;
    private TextView tvSelfSelected;
    private LinearLayout linSelf;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edt_company_code2);
        initView();
        initEdt();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        ivSelfSelected = findView(R.id.iv_self_selected);
        tvSelfSelected = findView(R.id.tv_self_selected);
        easySwipeMenuLayout = findView(R.id.easySwipeMenuLayout, this);
        easySwipeMenuLayout.addSwipeTouchListener(new ISwipe() {
            @Override
            public void OnLeftSwipe() {
            }

            @Override
            public void OnRightSwipe() {
            }
        });
        ivCompany = findView(R.id.iv_company);
        edtCompanyCode = findView(R.id.edt_company_code);
        TextView tvTittle = findView(R.id.tv_title);
        LinearLayout linBack = findView(R.id.lin_back, this);
        linEdtCode = findView(R.id.lin_edt_code, this);
        FrameLayout fraCommit = findView(R.id.fra_commit, this);
        tvCompany = findView(R.id.tv_company);
        ImageView ivDeleteCode = findView(R.id.iv_delete_code, this);
        linSelf = findView(R.id.lin_self, this);
        tvTittle.setText("企业服务");
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.lin_back:
            case R.id.fra_commit:
                finish();
                break;
            case R.id.iv_delete_code:
                setSelfSelectedCompany();
                break;

            case R.id.lin_edt_code:

                break;

            case R.id.lin_self:
                //设置BIMe 服务器000002
                setSelfSelectedCompany();
                break;
            case R.id.easySwipeMenuLayout:
                setSelfUnSelectedCompany();
                break;
            default:
                break;
        }
    }

    /**
     * 获取企业信息
     */

    private void getCompany(String code) {

        HashMap<String, String> params = new HashMap<>();
        params.put("Code", code);
        LoginController controller = new LoginController();
        controller.GetCompany(params, new CallBack<Company>() {
            @Override
            public void onSuccess(Company company) {
                if (company.getRet() == 1) {

                    setSelfUnSelectedCompany();
                    for (Company.DataDTO data : company.getData()) {
                        switch (data.getCu_urltype().toLowerCase()) {
                            case "logurl":
                                Glide.with(EdtCompanyCodeActivity.this).load(data.getCu_url()).into(ivCompany);
                                break;
                            case "docapi":
                                Hawk.put(CustomParam.Doc_URL, data.getCu_url());
                                break;
                            case "companyname":
                                tvCompany.setText(data.getCu_url());
                                Hawk.put(CustomParam.CompanyStr, data.getCu_url());
                                break;
                            case "modelapi":
                                Hawk.put(CustomParam.Bim_URL, data.getCu_url());
                                break;
                            case "newapi":
                                Hawk.put(CustomParam.Base_URL, data.getCu_url());
                                break;
                            case "newweb":
                                Hawk.put(CustomParam.Web_URL, data.getCu_url());
                                break;
                            case "taskapi":
                                Hawk.put(CustomParam.Schedual_URL, data.getCu_url());
                                break;
                            case "web":
                                handleJson(data);
                                break;
                            case "panorama":
                                Hawk.put(CustomParam.Panorama_URL, data.getCu_url());
                                break;
                            case "mobiletaskapi":
                                Hawk.put(CustomParam.Flow_URL, data.getCu_url());
                                break;

                        }

                    }
                    Hawk.put(CustomParam.CompanyCode, code);
                    isEdtCode = true;
                } else {
                    showMsg("输入企业编码有误");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 处理数据
     *
     * @param dataDTO
     */
    private void handleJson(Company.DataDTO dataDTO) {
        try {
            if (dataDTO.getCu_desc().startsWith("{") && dataDTO.getCu_desc().endsWith("}")) {
                JSONObject jsonObject = new JSONObject(dataDTO.getCu_desc().replace("\\", ""));
                if (jsonObject.has("CompanyName")) {
                    tvCompany.setText(jsonObject.getString("CompanyName"));
                    Hawk.put(CustomParam.CompanyStr, jsonObject.getString("CompanyName"));
                }
            }

        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
//        CompanyName
    }

    /***
     * 初始化slef未选中状态
     */
    private void setSelfUnSelectedCompany() {
        linEdtCode.setVisibility(View.GONE);
        easySwipeMenuLayout.setVisibility(View.VISIBLE);
        easySwipeMenuLayout.resetStatus();
        tvSelfSelected.setTextColor(R.color.BIMe_Black);
        ivSelfSelected.setVisibility(View.VISIBLE);
        linSelf.setBackground(getDrawable(R.drawable.shape_white_6));
    }

    /***
     * 初始化self选中状态
     */
    private void setSelfSelectedCompany() {
        easySwipeMenuLayout.resetStatus();
        linEdtCode.setVisibility(View.VISIBLE);
        easySwipeMenuLayout.setVisibility(View.GONE);
        tvSelfSelected.setTextColor(R.color.white);
        ivSelfSelected.setVisibility(View.VISIBLE);
        edtCompanyCode.setText(null);
        getCompany("000000");
    }

    /**
     * 键盘确认键
     */
    private void initEdt() {

        edtCompanyCode.setFocusable(true);
        edtCompanyCode.setFocusableInTouchMode(true);
        edtCompanyCode.requestFocus();
        //显示软键盘
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        //用户输入完毕进行搜索
        edtCompanyCode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void afterTextChanged(Editable editable) {

                if (editable.length() == 6) {
                    //
                    String edtCode = editable.toString().trim();
                    if (!TextUtils.isEmpty(edtCode)) {

                        getCompany(edtCode);
                    }
                }

            }
        });
        // 键盘搜索键
        edtCompanyCode.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
       /* if (i == EditorInfo.IME_ACTION_GO) {
          //
          String edtCode = edtCompanyCode.getText().toString().trim();
          if (!TextUtils.isEmpty(edtCode)) {

            getCompany(edtCode);
          }

          return true;
        }*/
                return false;
            }
        });
    }
}
