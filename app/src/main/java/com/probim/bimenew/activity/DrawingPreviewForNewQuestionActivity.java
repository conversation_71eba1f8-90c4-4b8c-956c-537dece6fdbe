package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.model.DrawingsModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;
import com.probim.bimenew.utils.zoomPhoto.PhotoViewZoom;
import java.util.HashMap;

/**
 * Description :图纸选点功能
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/17:32.
 */
public class DrawingPreviewForNewQuestionActivity extends BaseActivity {

  @BindView(R.id.iv_back)
  ImageView ivBack;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.iv_drawings)
  PhotoViewZoom ivDrawings;
  @BindView(R.id.tv_right) TextView tvRight;
  private int imageWidth;
  private int imageHeight;
  public static String img2d_ModelId = "";
  public static String img2dId = "";
  public static String img2d_pos = "";
  public static String imgName = "";
  public static String img_url = "";

  private String modelId;
  private String twoDimensId;
  private float pX;
  private float pY;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_drawings_question);
    ButterKnife.bind(this);
    StatusBarUtils.transparencyBar(this);
    initView();
    loadData();
  }

  @Override
  protected void loadData() {
    if (getIntent() != null) {
      Intent intent = getIntent();
      //读取数据
      Bundle bundle = intent.getExtras();
      //if ()
      twoDimensId = bundle.getString(CustomParam.TwoDimensId);
      String name = bundle.getString(CustomParam.TwoDimensName);
      imgName = name;
      String projectId = bundle.getString(CustomParam.ProjectId);
      modelId = bundle.getString(CustomParam.ModelId);
      String position = bundle.getString(CustomParam.DrawingPosition);
      String[] strPosition = position.split("_");
      pX = Float.valueOf(strPosition[0]);
      pY = Float.valueOf(strPosition[1]);
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", projectId);
      params.put("ModelID", modelId);
      params.put("VersionNO", "");
      params.put("FileType", "PlanView");
      params.put("FileName", twoDimensId);

      ModelController controller = new ModelController();

      controller.GetTwoDimensionalDetails(params, new CallBack<DrawingsModel>() {
        @Override
        public void onSuccess(DrawingsModel drawingsModel) {

          img_url = drawingsModel.getImagebase64();
        /*  Glide.with(getApplicationContext())
              .load(Base64Utils.decode(drawingsModel.getImagebase64())).asBitmap().into(
              new SimpleTarget<Bitmap>() {
                @Override public void onResourceReady(Bitmap resource,
                    GlideAnimation<? super Bitmap> glideAnimation) {
                  imageWidth = resource.getWidth();
                  imageHeight = resource.getHeight();
                  ivDrawings.mOriginalWidth = imageWidth;
                  ivDrawings.mOriginalHeight = imageHeight;
                  ivDrawings.setImageBitmap(resource);

                  //到指定位置
                  ivDrawings.zoomTo(1f, pX, pY);
                }
              });*/
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });
    }
  }

  @Override
  protected void initView() {
    tvTitle.setText("图纸预览");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("完成");
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({ R.id.iv_back, R.id.lin_back, R.id.tv_right })
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.iv_back:
        finish();
        break;
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        finish();
        break;
    }
  }
}
