package com.probim.bimenew.activity.schedule.modules;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.AllProgressActivity;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.schedule.adapter.SpinnerStringAdapter;
import com.probim.bimenew.activity.schedule.dto.AddTaskDto;
import com.probim.bimenew.activity.schedule.dto.AddTaskResultDto;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.activity.schedule.dto.ShowTypeDto;
import com.probim.bimenew.activity.schedule.dto.UnitDto;
import com.probim.bimenew.activity.schedule.dto.UploadTaskDto;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.SchedualController;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.JsonHelper;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class TaskDetailsActivity extends BaseActivity implements View.OnClickListener {
    private SpinnerStringAdapter spinnerAdapter;
    private List<ShowTypeDto> spinnerList = new ArrayList<>();
    private final int code_select_task = 1000;
    private ShowTypeDto showTypeDto;
    private SchedualController schedualController;
    private TextView tvTask;
    private TextView tvPlanUnit;
    private TextView tvActualUnit;
    private EditText edtAll;
    private Spinner spinner;

    private TimePickerView pvTime;
    private TextView tvPlanStartTime;
    private TextView tvPlanEndTime;
    private TextView tvAddTime;
    private TextView tvActualStartTime;
    private TextView tvActualEndTime;
    private TextView tvFinishPlan;
    private EditText edtFinishActual;
    // 初次填报
    private boolean isFirstAdd;
    // 已经填报过
    private boolean isAdded;

    private String planStartDate;
    private String planEndDate;

    private String addDate;
    private SchedualPreviewListDTO.DataDTO detailsDto;
    private LinearLayout linState;

    private TextView tvState;

    private UnitDto unitDtoX;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_task_details);
        TranslucentUtils.setTRANSLUCENT(this);
        loadData();
        initView();
    }

    @Override
    protected void loadData() {
        if (getIntent() != null) {
            detailsDto = (SchedualPreviewListDTO.DataDTO) getIntent().getSerializableExtra("schedule-dto");
            getUint();
        }

    }

    @Override
    protected void initView() {
        View view = findView(R.id.view_back, this);
        linState = findView(R.id.lin_state);
        tvState = findView(R.id.tv_state);
        RelativeLayout rlBohui = findView(R.id.rl_bohui, this);
        RelativeLayout rlTongguo = findView(R.id.rl_tongguo, this);
        ImageView ivUpload = findView(R.id.iv_up_state, this);
        TextView tvTitle = findView(R.id.tv_check_type);
        tvTitle.setText("填报详情");
        TextView tvRight = findView(R.id.tv_check_staus, this);
        tvRight.setVisibility(View.GONE);
        tvRight.setText("保存");
        tvPlanStartTime = findView(R.id.tv_plan_start_time, this);
        tvPlanStartTime.setText(detailsDto.getProgress_planstarttime().split(" ")[0]);
        tvPlanEndTime = findView(R.id.tv_plan_end_time, this);
        tvPlanEndTime.setText(detailsDto.getProgress_plannendtime().split(" ")[0]);
        tvAddTime = findView(R.id.tv_add_time, this);
        tvAddTime.setText(detailsDto.getProgress_unittime().split(" ")[0]);
        tvActualStartTime = findView(R.id.tv_actual_start_time, this);
        tvActualStartTime.setText(TextUtils.isEmpty(detailsDto.getProgress_actualstarttime()) ? "实际开始时间未选择" : detailsDto.getProgress_actualstarttime().split(" ")[0]);
        tvActualEndTime = findView(R.id.tv_actual_end_time, this);
        tvActualEndTime.setText(TextUtils.isEmpty(detailsDto.getProgress_actualendtime()) ? "实际结束时间未选择" : detailsDto.getProgress_actualendtime().split(" ")[0]);
        tvFinishPlan = findView(R.id.tv_finish_plan);
        tvFinishPlan.setText(detailsDto.getProgress_planvalue() + "");
        edtFinishActual = findView(R.id.edt_finish_actual);
        edtFinishActual.setText(detailsDto.getProgress_actualvalue() + "");
        tvTask = findView(R.id.tv_task);
        tvTask.setText(detailsDto.getProgress_Name());
        tvPlanUnit = findView(R.id.tv_plan_unit);
        tvActualUnit = findView(R.id.tv_actual_unit);
        boolean isCanExamine = Hawk.get("isCanExamine");
        boolean isCanAdd = Hawk.get("isCanAdd");
        if (isCanExamine){
            // 审核权限
            if (detailsDto.getAuditStatus() == 1) {
                // 判断登录人是监理单位
                // 待审核状态
                linState.setVisibility(View.VISIBLE);
                edtFinishActual.setEnabled(false);
                tvAddTime.setOnClickListener(null);
                ivUpload.setVisibility(View.GONE);
                tvRight.setVisibility(View.GONE);
            } else {
                // 已经审核状态
                edtFinishActual.setEnabled(false);
                ivUpload.setVisibility(View.GONE);
                tvRight.setVisibility(View.GONE);
                tvAddTime.setOnClickListener(null);
            }
        }
        if (isCanAdd){
            if (detailsDto.getAuditStatus() == 0 || detailsDto.getAuditStatus() == 3) {
                // 待提交状态 可编辑
                tvRight.setVisibility(View.VISIBLE);
                ivUpload.setVisibility(View.VISIBLE);
            } else {
                edtFinishActual.setEnabled(false);
                ivUpload.setVisibility(View.GONE);
                tvRight.setVisibility(View.GONE);
                tvAddTime.setOnClickListener(null);
            }
        }

        edtFinishActual.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    handleState(editable.toString());
                }
            }
        });

        handleState(detailsDto.getProgress_actualvalue() + "");
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.view_back:
                finish();
                break;
            case R.id.tv_add_time:
                openTimePicker(tvAddTime, true);
                break;
            case R.id.tv_actual_start_time:
//                openTimePicker(tvActualStartTime, false);
                break;
            case R.id.tv_actual_end_time:
//                openTimePicker(tvActualEndTime, false);
                break;
            case R.id.tv_check_staus:
                // 提交
                editTask();
                break;
            case R.id.iv_up_state:
                // 发布
                uploadTask("发布成功");
                break;
            case R.id.rl_bohui:
                uploadTask("驳回成功");
                break;
            case R.id.rl_tongguo:
                uploadTask("审批成功");
                break;
        }
    }

    /**
     * 获取单位
     */
    private void getUint() {
        schedualController = new SchedualController();
        HashMap<String, String> params = new HashMap<>();
        params.put("uid", detailsDto.getProgress_treeID());
        params.put("token", Hawk.get(CustomParam.Token));
        schedualController.getUnitText(params, new CallBack<UnitDto>() {

            @Override
            public void onSuccess(UnitDto unitDto) {
                if (unitDto.getRet() == 1) {
                    if (unitDto.getData().getUnitType() != null) {
                        // 任务显示方式
                        // 1 为 百分比
                        // 2 为 里程 m
                        // 3 为 里程 km
                        // 4 为 高程 m
                        switch (unitDto.getData().getUnitType()) {
                            case 1:
                                tvPlanUnit.setText("%");
                                tvActualUnit.setText("%");
                                break;
                            case 2:
                                tvPlanUnit.setText("m");
                                tvActualUnit.setText("m");
                                break;
                            case 3:
                                tvPlanUnit.setText("km");
                                tvActualUnit.setText("km");
                                break;
                            case 4:
                                tvPlanUnit.setText("m");
                                tvActualUnit.setText("m");
                                break;
                        }

                    }
                    unitDtoX = unitDto;

                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取当前日期
     *
     * @return
     */
    private String getCurrentDay() {
        // 获取当前时间
        Date currentDate = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        addDate = simpleDateFormat.format(currentDate);

        // 创建一个SimpleDateFormat对象，用于指定日期时间格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 使用SimpleDateFormat格式化当前时间为字符串
        String formattedDate = dateFormat.format(currentDate);

        return formattedDate;
    }

    /**
     * 获取系统时间
     *
     * @return
     */
    private String getDay(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    /*
     * 时间选择器
     */
    private void openTimePicker(TextView tv, boolean isAdd) {
        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);
        //确定按钮文字颜色
        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                String formatDate = getDay(date);
                tv.setText(formatDate);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                addDate = simpleDateFormat.format(date);
                judeInDate(addDate, detailsDto.getProgress_planstarttime(), detailsDto.getProgress_plannendtime());
            }
        }).setCancelText("清空").setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false}).setContentTextSize(16).setLabel("", "", "", "", "", "").isCenterLabel(true).setLineSpacingMultiplier(3.0f).setDividerColor(R.color.divider_color).setDecorView(null)
//                .setRangDate(startDate, endDate)
                .setTextColorCenter(Color.parseColor("#283A4F")).setTextColorOut(Color.parseColor("#A6AEB6"))
                //容器
                //.setDecorView(linContainer)
                .setLayoutRes(R.layout.pickerview_newcheck, new CustomListener() {
                    @Override
                    public void customLayout(View v) {

                        TextView tvDateTittle = v.findViewById(R.id.tv_date_title);
                        tvDateTittle.setText("请选择日期");
                        TextView btnConfirm = v.findViewById(R.id.btn_confirm);
                        btnConfirm.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });

                        TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                        tv_clear.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                pvTime.dismiss();
                            }
                        });

                        ImageView ivClose = (ImageView) v.findViewById(R.id.iv_close);
                        ivClose.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                //pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });
                    }
                }).build();
        pvTime.show();
    }

    /**
     * 计算时间差值
     *
     * @param startDate
     * @param endDate
     */
    private long handleDateDuration(String startDate, String endDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(startDate, formatter);
        LocalDateTime endTime = LocalDateTime.parse(endDate, formatter);
        // 计算时间差值
        Duration duration = Duration.between(startTime, endTime);

        // 获取时间差值的各个部分
        long seconds = duration.getSeconds();
        long minutes = duration.toMinutes();
        long hours = duration.toHours();
        long days = duration.toDays();
        // 打印时间差值
        Logger.t("时间差值（秒）: ").e(seconds + "");
        Logger.t("时间差值（分钟）: ").e(minutes + "");
        Logger.t("时间差值（小时）").e(hours + "");
        Logger.t("时间差值（天）: ").e(days + "");
        return days;

    }

    /**
     * 判断填报时间是否在计划之内
     *
     * @param addDate
     * @param startDate
     * @param endDate
     */
    private void judeInDate(String addDate, String startDate, String endDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(startDate, formatter);
        LocalDateTime endTime = LocalDateTime.parse(endDate, formatter);
        LocalDateTime addTime = LocalDateTime.parse(addDate, formatter);
        // 计算时间差值
        // 首先判断填报时间 是否小于计划开始时间
        Duration durationLeft = Duration.between(startTime, addTime);
        Logger.t("durationLeft ").e(durationLeft.toDays() + "");
        // 其次判断填报时间 是否大雨计划结束时间
        Duration durationRight = Duration.between(endTime, addTime);
        Logger.t("durationRight ").e(durationRight.toDays() + "");
        int unitType = unitDtoX.getData().getUnitType();
        if (unitType == 1) {
            // 1 百分比计算方式
            if (durationRight.toDays() >= 0) {
                tvFinishPlan.setText("100");
                return;
            }
            if (durationLeft.toDays() >= 0 && durationRight.toDays() <= 0) {
                long sum = handleDateDuration(startDate, endDate) + 1;
                long average = 100 / sum;
                long plan = average * (durationLeft.toDays() + 1);
                tvFinishPlan.setText(plan + "");
                // 当前填报时间在计划时间之内
                Logger.t("打印当前时间 1").e(addDate);
            } else {
                tvFinishPlan.setText("0");
            }
        } else if (unitType == 4) {
            // 为差值计算方式
            if (durationRight.toDays() >= 0) {
                tvFinishPlan.setText(unitDtoX.getData().getUnitValue().split(",")[1]);
                return;
            }
            if (durationLeft.toDays() >= 0 && durationRight.toDays() <= 0) {
                long sum = handleDateDuration(startDate, endDate) + 1;
                float average = (Float.parseFloat(unitDtoX.getData().getUnitValue().split(",")[1]) - Float.parseFloat(unitDtoX.getData().getUnitValue().split(",")[0])) / sum;
                float plan = average * (durationLeft.toDays() + 1);
                DecimalFormat decimalFormat = new DecimalFormat(".00");
                tvFinishPlan.setText(decimalFormat.format(plan));
                // 当前填报时间在计划时间之内
                Logger.t("打印当前时间 4").e(addDate);
            } else {
                tvFinishPlan.setText("0");
            }

        } else {
            if (durationRight.toDays() >= 0) {
                tvFinishPlan.setText(unitDtoX.getData().getUnitValue().split(",")[1]);
                return;
            }
            if (durationLeft.toDays() >= 0 && durationRight.toDays() <= 0) {
                long sum = handleDateDuration(startDate, endDate) + 1;
                long average = Long.parseLong(unitDtoX.getData().getUnitValue()) / sum;
                long plan = average * (durationLeft.toDays() + 1);
                tvFinishPlan.setText(plan + "");
                // 当前填报时间在计划时间之内
                Logger.t("打印当前时间 2,3 ").e(addDate);
            } else {
                tvFinishPlan.setText("0");
            }

        }
    }

    /**
     * 编辑填报
     */
    private void editTask() {
        AddTaskDto addTaskDto = new AddTaskDto();
        addTaskDto.setProgress_ID(detailsDto.getProgress_ID());
        addTaskDto.setProgress_Name(detailsDto.getProgress_Name());
        addTaskDto.setProgress_ProjectID(detailsDto.getProgress_ProjectID());
        addTaskDto.setIsSubmitAudit(false);
        addTaskDto.setProgress_actualstarttime(detailsDto.getProgress_actualstarttime());
        addTaskDto.setProgress_actualendtime(detailsDto.getProgress_actualendtime());
        addTaskDto.setProgress_planvalue(Float.parseFloat(tvFinishPlan.getText().toString()));
        addTaskDto.setProgress_actualvalue(Double.parseDouble(edtFinishActual.getText().toString()));
        addTaskDto.setProgress_createuser(detailsDto.getProgress_createuser());
        addTaskDto.setProgress_createuserid(detailsDto.getProgress_createuserid());
        addTaskDto.setProgress_parentid(detailsDto.getProgress_parentid());
        addTaskDto.setProgress_planfate(detailsDto.getProgress_planfate());
        addTaskDto.setProgress_plannendtime(detailsDto.getProgress_plannendtime());
        addTaskDto.setProgress_planratio(detailsDto.getProgress_planratio());
        addTaskDto.setProgress_planstarttime(detailsDto.getProgress_planstarttime());
        addTaskDto.setProgress_state(tvState.getText().toString());
        addTaskDto.setProgress_treeID(detailsDto.getProgress_treeID());
        addTaskDto.setProgress_unittime(tvAddTime.getText().toString());
        addTaskDto.setToken(Hawk.get(CustomParam.Token));
        float y = Float.parseFloat(edtFinishActual.getText().toString());
        float x = Float.parseFloat(tvFinishPlan.getText().toString());
        float ss = y / x * 100;
        addTaskDto.setProgress_actualratio(ss);
        schedualController.editTask(JsonHelper.toJson(addTaskDto), new CallBack<AddTaskResultDto>() {
            @Override
            public void onSuccess(AddTaskResultDto addTaskResultDto) {
                if (addTaskResultDto.getRet() == 1) {
                    showMsg("编辑成功");
                    finish();
                }

            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 发布提交
     */
    private void uploadTask(String msg) {
        schedualController = new SchedualController();
        UploadTaskDto dto = new UploadTaskDto();
        if (msg.equals("驳回成功")){
            // 2
            dto.setAuditDescription("请校核后再提交");
            dto.setAuditType(2);

        }else if (msg.equals("审批成功")){
            // 1
            dto.setAuditDescription("同意");
            dto.setAuditType(1);
            // 发布是0
        }else if (msg.equals("发布成功")){
            dto.setAuditType(0);
        }
        dto.setToken(Hawk.get(CustomParam.Token));
        List<String> ids = new ArrayList<>();
        ids.add(detailsDto.getProgress_ID());
        dto.setProgressIds(ids);
        schedualController.uploadTask(JsonHelper.toJson(dto), new CallBack<AddTaskResultDto>() {


            @Override
            public void onSuccess(AddTaskResultDto addTaskResultDto) {

                if (addTaskResultDto.getRet() == 1) {
                    if (addTaskResultDto.getData()) {
                        showMsg(msg);
                        finish();
                    }
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

    }

    /**
     * 处理进度计划状态
     */
    private void handleState(String actualStr) {
        // 实际完成进度
        float actualFinish = Float.parseFloat(actualStr);
        float planFinish = Float.parseFloat(tvFinishPlan.getText().toString());
        if (actualFinish > planFinish) {
            tvState.setText("超前");
        } else if (actualFinish == planFinish) {
            tvState.setText("正常");
        } else {
            tvState.setText("滞后");
        }

    }

}
