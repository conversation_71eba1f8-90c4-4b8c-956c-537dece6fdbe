package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualNewSafeDTO {

    @SerializedName("MobileSafe_Name")
    private String mobileSafe_Name;
    @SerializedName("MobileSafe_Measure")
    private String mobileSafe_Measure;
    @SerializedName("MobileSafe_ProjectID")
    private String mobileSafe_ProjectID;
    @SerializedName("MobileSafe_Unittime")
    private String mobileSafe_Unittime;
    @SerializedName("MobileSafe_Createusername")
    private String mobileSafe_Createusername;
    @SerializedName("MobileSafe_state")
    private String mobileSafe_state;
    private String organizeId;

    public String getMobileSafe_Name() {
        return mobileSafe_Name;
    }

    public void setMobileSafe_Name(String mobileSafe_Name) {
        this.mobileSafe_Name = mobileSafe_Name;
    }

    public String getMobileSafe_Measure() {
        return mobileSafe_Measure;
    }

    public void setMobileSafe_Measure(String mobileSafe_Measure) {
        this.mobileSafe_Measure = mobileSafe_Measure;
    }

    public String getMobileSafe_ProjectID() {
        return mobileSafe_ProjectID;
    }

    public void setMobileSafe_ProjectID(String mobileSafe_ProjectID) {
        this.mobileSafe_ProjectID = mobileSafe_ProjectID;
    }

    public String getMobileSafe_Unittime() {
        return mobileSafe_Unittime;
    }

    public void setMobileSafe_Unittime(String mobileSafe_Unittime) {
        this.mobileSafe_Unittime = mobileSafe_Unittime;
    }

    public String getMobileSafe_Createusername() {
        return mobileSafe_Createusername;
    }

    public void setMobileSafe_Createusername(String mobileSafe_Createusername) {
        this.mobileSafe_Createusername = mobileSafe_Createusername;
    }

    public String getMobileSafe_state() {
        return mobileSafe_state;
    }

    public void setMobileSafe_state(String mobileSafe_state) {
        this.mobileSafe_state = mobileSafe_state;
    }

    public String getOrganizeId() {
        return organizeId;
    }

    public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
    }
}
