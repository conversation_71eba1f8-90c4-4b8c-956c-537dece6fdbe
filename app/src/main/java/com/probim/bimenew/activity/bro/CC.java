package com.probim.bimenew.activity.bro;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

public class CC extends BroadcastReceiver {
    private onRe onReeee;

    @Override
    public void onReceive(Context context, Intent intent) {
        String string = intent.getAction();

        if (!TextUtils.isEmpty(string)) {
//            Toast.makeText(context, string, Toast.LENGTH_SHORT).show();
            if (onReeee != null) {
                onReeee.onCC(string);
            }
        }
    }

    public void setInter(onRe DDD) {
        this.onReeee = DDD;


    }

    public interface onRe {
        void onCC(String s);

    }
}
