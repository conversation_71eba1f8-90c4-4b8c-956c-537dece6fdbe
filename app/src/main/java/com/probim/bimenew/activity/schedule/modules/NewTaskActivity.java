package com.probim.bimenew.activity.schedule.modules;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bigkoo.pickerview.builder.TimePickerBuilder;
import com.bigkoo.pickerview.listener.CustomListener;
import com.bigkoo.pickerview.listener.OnTimeSelectListener;
import com.bigkoo.pickerview.view.TimePickerView;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.check.AllProgressActivity;
import com.probim.bimenew.activity.check.AllTaskListActivity;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.activity.schedule.adapter.SpinnerStringAdapter;
import com.probim.bimenew.activity.schedule.dto.AddTaskDto;
import com.probim.bimenew.activity.schedule.dto.AddTaskResultDto;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.activity.schedule.dto.SchedualPreviewListDTO;
import com.probim.bimenew.activity.schedule.dto.SetUnitDto;
import com.probim.bimenew.activity.schedule.dto.ShowTypeDto;
import com.probim.bimenew.activity.schedule.dto.UnitDto;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.SchedualController;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.utils.JsonHelper;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class NewTaskActivity extends BaseActivity implements View.OnClickListener {
    private SpinnerStringAdapter spinnerAdapter;
    private List<ShowTypeDto> spinnerList = new ArrayList<>();
    private final int code_select_task = 1000;
    private ShowTypeDto showTypeDto;
    private SchedualController schedualController;
    private TextView tvTask;
    private SchedualListDto.DataDTO.ChildrenDTO tasksBean;
    private TextView tvAllUnit;
    private TextView tvPlanUnit;
    private TextView tvActualUnit;
    private EditText edtAll;
    private Spinner spinner;

    private TimePickerView pvTime;
    private TextView tvPlanStartTime;
    private TextView tvPlanEndTime;
    private TextView tvAddTime;
    private TextView tvActualStartTime;
    private TextView tvActualEndTime;
    private TextView tvFinishPlan;
    private EditText edtFinishActual;
    // 初次填报
    private boolean isFirstAdd;
    // 已经填报过
    private boolean isAdded;

    private String planStartDate;
    private String planEndDate;

    private SchedualPreviewListDTO.DataDTO infoDto;
    private String addDate;

    private TextView tvState;
    private TextView tvTaskType;
    private EditText edtAllMin;
    private LinearLayout linEdtALlMin;

    private UnitDto unitDtoX;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_new_task);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
    }

    @Override
    protected void initView() {
        View view = findView(R.id.view_back, this);
        tvState = findView(R.id.tv_state);
        tvTaskType = findView(R.id.tv_task_type);
        linEdtALlMin = findView(R.id.lin_edt_all_min);
        edtAllMin = findView(R.id.edt_all_min);
        TextView tvRight = findView(R.id.tv_check_staus, this);
        tvPlanStartTime = findView(R.id.tv_plan_start_time, this);
        tvPlanEndTime = findView(R.id.tv_plan_end_time, this);
        tvAddTime = findView(R.id.tv_add_time, this);
        tvAddTime.setText(getCurrentDay());
        tvActualStartTime = findView(R.id.tv_actual_start_time, this);
        tvActualEndTime = findView(R.id.tv_actual_end_time, this);
        tvFinishPlan = findView(R.id.tv_finish_plan);
        edtFinishActual = findView(R.id.edt_finish_actual);
        tvTask = findView(R.id.tv_task);
        edtAll = findView(R.id.edt_all);
        RelativeLayout rlSelectTask = findView(R.id.rl_select_task, this);
        tvAllUnit = findView(R.id.tv_all_unit);
        tvPlanUnit = findView(R.id.tv_plan_unit);
        tvActualUnit = findView(R.id.tv_actual_unit);
        spinner = findView(R.id.spinner_show_type);
        spinnerList.add(new ShowTypeDto(1, "百分比%", "%"));
        spinnerList.add(new ShowTypeDto(2, "里程m", "m"));
        spinnerList.add(new ShowTypeDto(3, "里程km", "km"));
        spinnerList.add(new ShowTypeDto(4, "高程m", "m"));
        // 创建适配器对象并设置数据源
        spinnerAdapter = new SpinnerStringAdapter(spinnerList);
        // 将适配器设置到 Spinner
        spinner.setAdapter(spinnerAdapter);
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                showTypeDto = spinnerList.get(i);
                tvAllUnit.setText(showTypeDto.getUnit());
                tvPlanUnit.setText(showTypeDto.getUnit());
                tvActualUnit.setText(showTypeDto.getUnit());
                // 第一次填报 任务总量可以填写， 实际开始时间 实际完成时间
                if (isFirstAdd) {
                    switch (showTypeDto.getType()) {
                        case 1:
                            linEdtALlMin.setVisibility(View.GONE);
                            tvTaskType.setText("任务总量：");
                            edtAll.setText("100");
                            tvFinishPlan.setText("100");
                            break;
                        case 2:
                        case 3:
                            linEdtALlMin.setVisibility(View.GONE);
                            tvTaskType.setText("任务里程：");
                            edtAll.setText("0");
                            tvFinishPlan.setText("0");
                            break;
                        case 4:
                            linEdtALlMin.setVisibility(View.VISIBLE);
                            edtAllMin.setText("0");
                            tvTaskType.setText("顶部高程：");
                            edtAll.setText("0");
                            tvFinishPlan.setText("0");
                            break;

                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
        edtFinishActual.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    handleState(editable.toString());
                }
            }
        });
        edtAll.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (showTypeDto.getType() != 1) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        tvFinishPlan.setText(editable.toString());
                    }
                }

            }
        });

    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.view_back:
                finish();
                break;
            case R.id.rl_select_task:
                Intent intent = new Intent(this, AllProgressActivity.class);
                intent.putExtra("isTask", true);
                startActivityForResult(intent, code_select_task);
                break;
            case R.id.tv_plan_start_time:
//                openTimePicker(tvPlanStartTime);
                break;
            case R.id.tv_plan_end_time:
//                openTimePicker(tvPlanEndTime);
                break;
            case R.id.tv_add_time:
                openTimePicker(tvAddTime, true);
                break;
            case R.id.tv_actual_start_time:
                openTimePicker(tvActualStartTime, false);
                break;
            case R.id.tv_actual_end_time:
                openTimePicker(tvActualEndTime, false);
                break;
            case R.id.tv_check_staus:
                if (isFirstAdd) {
                    setUnit();
                } else {
                    addTask();
                }
                break;
        }
    }

    /**
     * 获取系统时间
     *
     * @return
     */
    private String getDay(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    /**
     * 获取当前日期
     *
     * @return
     */
    private String getCurrentDay() {
        // 获取当前时间
        Date currentDate = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        addDate = simpleDateFormat.format(currentDate);

        // 创建一个SimpleDateFormat对象，用于指定日期时间格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 使用SimpleDateFormat格式化当前时间为字符串
        String formattedDate = dateFormat.format(currentDate);

        return formattedDate;
    }

    /**
     * 时间选择器
     */
    private void openTimePicker(TextView tv, boolean isAdd) {
        Calendar startDate = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        //正确设置方式 原因：注意事项有说明
        Calendar c = Calendar.getInstance();//
        int mYear = c.get(Calendar.YEAR); // 获取当前年份
        int mMonth = c.get(Calendar.MONTH);// 获取当前月份
        int mDay = c.get(Calendar.DAY_OF_MONTH);// 获取当日期
        startDate.set(mYear, mMonth, mDay);
        endDate.set(2030, 12, 31);
        //确定按钮文字颜色
        pvTime = new TimePickerBuilder(this, new OnTimeSelectListener() {
            @Override
            public void onTimeSelect(Date date, View v) {
                String formatDate = getDay(date);
                tv.setText(formatDate);
                if (isAdd) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    addDate = simpleDateFormat.format(date);
                    judeInDate(addDate, planStartDate, planEndDate);
                }
            }
        }).setCancelText("清空").setCancelColor(getResources().getColor(R.color.sp_18))//取消按钮文字
                .setSubmitColor(getResources().getColor(R.color.text_yellow))//确定按钮文字颜色
                .setType(new boolean[]{true, true, true, false, false, false}).setContentTextSize(16).setLabel("", "", "", "", "", "").isCenterLabel(true).setLineSpacingMultiplier(3.0f).setDividerColor(R.color.divider_color).setDecorView(null)
//                .setRangDate(startDate, endDate)
                .setTextColorCenter(Color.parseColor("#283A4F")).setTextColorOut(Color.parseColor("#A6AEB6"))
                //容器
                //.setDecorView(linContainer)
                .setLayoutRes(R.layout.pickerview_newcheck, new CustomListener() {
                    @Override
                    public void customLayout(View v) {

                        TextView tvDateTittle = v.findViewById(R.id.tv_date_title);
                        tvDateTittle.setText("请选择日期");
                        TextView btnConfirm = v.findViewById(R.id.btn_confirm);
                        btnConfirm.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });

                        TextView tv_clear = (TextView) v.findViewById(R.id.tv_clear);
                        tv_clear.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                pvTime.dismiss();
                            }
                        });

                        ImageView ivClose = (ImageView) v.findViewById(R.id.iv_close);
                        ivClose.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                //pvTime.returnData();
                                pvTime.dismiss();
                            }
                        });
                    }
                }).build();
        pvTime.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case code_select_task:
                    List<SchedualListDto.DataDTO.ChildrenDTO> taskListData = (List<SchedualListDto.DataDTO.ChildrenDTO>) data.getExtras().getSerializable(CustomParam.StartForResult);
                    //任务数据
                    if (!taskListData.isEmpty()) {
                        tasksBean = taskListData.get(0);
                        tvTask.setTextColor(getColor(R.color.black));
                        tvTask.setText(tasksBean.getName());
                        getUint();
                    } else {
                        tvTask.setText("请选择关联任务");
                        tvTask.setTextColor(Color.parseColor("#CCCCCC"));
                    }

                    break;

                default:
                    break;
            }
        }
    }

    /**
     * 获取单位
     */
    private void getUint() {
        schedualController = new SchedualController();
        HashMap<String, String> params = new HashMap<>();
        params.put("uid", tasksBean.getUID());
        params.put("token", Hawk.get(CustomParam.Token));
        schedualController.getUnitText(params, new CallBack<UnitDto>() {

            @Override
            public void onSuccess(UnitDto unitDto) {
                if (unitDto.getRet() == 1) {
                    if (unitDto.getData().getUnitType() != null) {
                        isFirstAdd = false;
                        edtAll.setEnabled(false);
                        edtAll.setText(unitDto.getData().getUnitValue());
                        tvFinishPlan.setText(unitDto.getData().getUnitValue());
                        spinner.setSelection(unitDto.getData().getUnitType() - 1, true);
                        spinner.setEnabled(false);
                        // 不为null时已经填报过
                        // 任务显示方式
                        // 1 为 百分比
                        // 2 为 里程 m
                        // 3 为 里程 km
                        // 4 为 高程 m
                        switch (unitDto.getData().getUnitType()) {
                            case 1:
                                edtAll.setText(unitDto.getData().getUnitValue());
                                tvFinishPlan.setText(unitDto.getData().getUnitValue());
                                tvAllUnit.setText("%");
                                tvPlanUnit.setText("%");
                                tvActualUnit.setText("%");
                                break;
                            case 2:
                                edtAll.setText(unitDto.getData().getUnitValue());
                                tvFinishPlan.setText(unitDto.getData().getUnitValue());
                                tvAllUnit.setText("m");
                                tvPlanUnit.setText("m");
                                tvActualUnit.setText("m");
                                break;
                            case 3:
                                edtAll.setText(unitDto.getData().getUnitValue());
                                tvFinishPlan.setText(unitDto.getData().getUnitValue());
                                tvAllUnit.setText("km");
                                tvPlanUnit.setText("km");
                                tvActualUnit.setText("km");
                                break;
                            case 4:
                                // 0,50
                                tvTaskType.setText("顶部高程：");
                                linEdtALlMin.setVisibility(View.VISIBLE);
                                edtAll.setText(unitDto.getData().getUnitValue().split(",")[1]);
                                edtAllMin.setText(unitDto.getData().getUnitValue().split(",")[0]);
                                edtAllMin.setEnabled(false);
                                tvFinishPlan.setText(unitDto.getData().getUnitValue().split(",")[1]);
                                tvAllUnit.setText("m");
                                tvPlanUnit.setText("m");
                                tvActualUnit.setText("m");
                                break;
                        }
                    } else {
                        // 第一次填报
                        edtAll.setText("100");
                        tvFinishPlan.setText("100");
                        tvAllUnit.setText("%");
                        tvPlanUnit.setText("%");
                        tvActualUnit.setText("%");
                        isFirstAdd = true;

                    }
                    // 获取选中的进度详情
                    getInfo();
                    unitDtoX = unitDto;
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取任务详情
     */
    private void getInfo() {
        schedualController = new SchedualController();
        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("projectid", tasksBean.getProjectUID());
        parmas.put("uid", tasksBean.getUID());
        parmas.put("Token",Hawk.get(CustomParam.Token));
        schedualController.getAllSchedualList(parmas, new CallBack<SchedualPreviewListDTO>() {
            @Override
            public void onSuccess(SchedualPreviewListDTO schedualPreviewListDTO) {
                if (schedualPreviewListDTO.getRet() == 1) {
                    if (!schedualPreviewListDTO.getData().isEmpty()) {
                        isAdded = true;
                        infoDto = schedualPreviewListDTO.getData().get(0);
                        tvPlanStartTime.setText(infoDto.getProgress_planstarttime().split(" ")[0]);
                        tvPlanEndTime.setText(infoDto.getProgress_plannendtime().split(" ")[0]);
                        planStartDate = infoDto.getProgress_planstarttime();
                        planEndDate = infoDto.getProgress_plannendtime();
                        tvAddTime.setText(getCurrentDay());
                        // 计算时间差值
                        judeInDate(addDate, planStartDate, planEndDate);
//                        edtFinishActual.setText(dto.getProgress_actualvalue() + "");

                        // 第一次填报 任务总量可以填写， 实际开始时间 实际完成时间
                        if (isFirstAdd) {
                            showTypeDto = (ShowTypeDto) spinner.getSelectedItem();
                            switch (showTypeDto.getUnit()) {
                                case "%":
                                    edtAll.setText("100");
                                    tvFinishPlan.setText("100");
                                    break;
                                default:
                                    edtAll.setText("0");
                                    tvFinishPlan.setText("0");
                                    break;

                            }

                        } else {
                            // 获取数据
                            if (TextUtils.isEmpty(infoDto.getProgress_actualstarttime())) {
                                tvActualStartTime.setText("选择开始时间");

                            } else {
                                tvActualStartTime.setText(infoDto.getProgress_actualstarttime().split(" ")[0]);
                                tvActualStartTime.setOnClickListener(null);
                            }
                            // 获取数据
                            if (TextUtils.isEmpty(infoDto.getProgress_actualendtime())) {
                                tvActualEndTime.setText("选择结束时间");

                            } else {
                                tvActualEndTime.setText(infoDto.getProgress_actualendtime().split(" ")[0]);
                                tvActualEndTime.setOnClickListener(null);
                            }
//                            tvFinishPlan.setText(infoDto.getProgress_planvalue() + "");
                        }
                    } else {
                        isAdded = false;
                        //
                        planStartDate = tasksBean.getStart();
                        planEndDate = tasksBean.getFinish();
                        judeInDate(addDate, planStartDate, planEndDate);
                        tvPlanStartTime.setText(tasksBean.getStart().split(" ")[0]);
                        tvPlanEndTime.setText(tasksBean.getFinish().split(" ")[0]);
                        tvAddTime.setText(getCurrentDay());
                    }
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 新建填报
     */
    private void addTask() {
        AddTaskDto addTaskDto = new AddTaskDto();
        if (isFirstAdd || !isAdded) {
            addTaskDto.setProgress_Name(tasksBean.getName());
            addTaskDto.setProgress_ProjectID(tasksBean.getProjectUID());
            addTaskDto.setProgress_actualstarttime(tvActualStartTime.getText().toString());
            addTaskDto.setProgress_actualendtime(tvActualEndTime.getText().toString());
            addTaskDto.setProgress_planvalue(Float.parseFloat(edtAll.getText().toString()));
            if (isFirstAdd) {
                // 是否提交单位？unit？
                addTaskDto.setIsSubmitAudit(false);

            } else {
                addTaskDto.setIsSubmitAudit(false);
            }
            if (TextUtils.isEmpty(edtFinishActual.getText().toString())) {
                showMsg("请填写实际完成进度");
                return;
            }

            addTaskDto.setProgress_actualvalue(Double.parseDouble(edtFinishActual.getText().toString()));
            addTaskDto.setProgress_createuser(Hawk.get(CustomParam.RealName));
            addTaskDto.setProgress_createuserid(Hawk.get(CustomParam.UserId));
            addTaskDto.setProgress_parentid(tasksBean.getParentTaskUID());
            addTaskDto.setProgress_planfate(tasksBean.getDuration().intValue());
            addTaskDto.setProgress_plannendtime(tasksBean.getFinish());
            addTaskDto.setProgress_planratio(Double.parseDouble(edtAll.getText().toString()));
            addTaskDto.setProgress_planstarttime(tasksBean.getStart());
            addTaskDto.setProgress_state(tvState.getText().toString());
            addTaskDto.setProgress_treeID(tasksBean.getUID());
            addTaskDto.setProgress_unittime(tvAddTime.getText().toString());
            addTaskDto.setToken(Hawk.get(CustomParam.Token));

        } else {

            addTaskDto.setProgress_Name(tasksBean.getName());
            addTaskDto.setProgress_ProjectID(infoDto.getProgress_ProjectID());
            if (isFirstAdd) {
                // 是否提交单位？unit？
                addTaskDto.setIsSubmitAudit(false);
                addTaskDto.setProgress_actualstarttime(tvActualStartTime.getText().toString());
                addTaskDto.setProgress_actualendtime(tvActualEndTime.getText().toString());
                addTaskDto.setProgress_planvalue(Float.parseFloat(edtAll.getText().toString()));
            } else {
                addTaskDto.setIsSubmitAudit(false);
                addTaskDto.setProgress_actualstarttime(infoDto.getProgress_actualstarttime());
                addTaskDto.setProgress_actualendtime(infoDto.getProgress_actualendtime());
                addTaskDto.setProgress_planvalue(Float.parseFloat(edtAll.getText().toString()));
            }
            if (TextUtils.isEmpty(edtFinishActual.getText().toString())) {
                showMsg("请填写实际完成进度");
                return;
            }
            addTaskDto.setProgress_actualvalue(Double.parseDouble(edtFinishActual.getText().toString()));
            addTaskDto.setProgress_createuser(Hawk.get(CustomParam.RealName));
            addTaskDto.setProgress_createuserid(Hawk.get(CustomParam.UserId));
            addTaskDto.setProgress_parentid(infoDto.getProgress_parentid());
            addTaskDto.setProgress_planfate(infoDto.getProgress_planfate());
            addTaskDto.setProgress_plannendtime(tasksBean.getFinish());
            addTaskDto.setProgress_planratio(infoDto.getProgress_planratio());
            addTaskDto.setProgress_planstarttime(tasksBean.getStart());
            addTaskDto.setProgress_state(infoDto.getProgress_state());
            addTaskDto.setProgress_treeID(infoDto.getProgress_treeID());
            addTaskDto.setProgress_unittime(tvAddTime.getText().toString());
            addTaskDto.setToken(Hawk.get(CustomParam.Token));
        }
        float y = Float.parseFloat(edtFinishActual.getText().toString());
        float x = Float.parseFloat(edtAll.getText().toString());
        float ss = y / x * 100;
        addTaskDto.setProgress_actualratio(ss);
        schedualController.addTask(JsonHelper.toJson(addTaskDto), new CallBack<AddTaskResultDto>() {

            @Override
            public void onSuccess(AddTaskResultDto addTaskResultDto) {
                if (addTaskResultDto.getRet() == 1) {
                    finish();
                } else {
                    showMsg(addTaskResultDto.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }

    /**
     * 计算时间差值
     *
     * @param startDate
     * @param endDate
     */
    private long handleDateDuration(String startDate, String endDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(startDate, formatter);
        LocalDateTime endTime = LocalDateTime.parse(endDate, formatter);
        // 计算时间差值
        Duration duration = Duration.between(startTime, endTime);

        // 获取时间差值的各个部分
        long seconds = duration.getSeconds();
        long minutes = duration.toMinutes();
        long hours = duration.toHours();
        long days = duration.toDays();
        // 打印时间差值
        Logger.t("时间差值（秒）: ").e(seconds + "");
        Logger.t("时间差值（分钟）: ").e(minutes + "");
        Logger.t("时间差值（小时）").e(hours + "");
        Logger.t("时间差值（天）: ").e(days + "");
        return days;

    }

    /**
     * 判断填报时间是否在计划之内
     *
     * @param addDate
     * @param startDate
     * @param endDate
     */
    private void judeInDate(String addDate, String startDate, String endDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(startDate, formatter);
        LocalDateTime endTime = LocalDateTime.parse(endDate, formatter);
        LocalDateTime addTime = LocalDateTime.parse(addDate, formatter);
        // 计算时间差值
        // 首先判断填报时间 是否小于计划开始时间
        Duration durationLeft = Duration.between(startTime, addTime);
        Logger.t("durationLeft ").e(durationLeft.toDays() + "");
        // 其次判断填报时间 是否大雨计划结束时间
        Duration durationRight = Duration.between(endTime, addTime);
        Logger.t("durationRight ").e(durationRight.toDays() + "");
        //long days = durationLeft.toDays();
        int unitType;
        // 分类判断单位类型
        if (isFirstAdd) {
            unitType = showTypeDto.getType();
        } else {
            unitType = unitDtoX.getData().getUnitType();
        }
        if (unitType == 1) {
            // 1 百分比计算方式
            if (durationRight.toDays() >= 0) {
                tvFinishPlan.setText("100");
                return;
            }
            if (durationLeft.toDays() >= 0 && durationRight.toDays() <= 0) {
                long sum = handleDateDuration(startDate, endDate) + 1;
                long average = 100 / sum;
                long plan = average * (durationLeft.toDays() + 1);
                tvFinishPlan.setText(plan + "");
                // 当前填报时间在计划时间之内
                Logger.t("打印当前时间 1").e(addDate);
            } else {
                tvFinishPlan.setText("0");
            }
        } else if (unitType == 4) {
            // 为差值计算方式
            if (durationRight.toDays() >= 0) {
                tvFinishPlan.setText(edtAll.getText());
                return;
            }
            if (durationLeft.toDays() >= 0 && durationRight.toDays() <= 0) {
                long sum = handleDateDuration(startDate, endDate) + 1;
                float average = (Float.parseFloat(edtAll.getText().toString()) - Float.parseFloat(edtAllMin.getText().toString())) / sum;
                float plan = average * (durationLeft.toDays() + 1);
                DecimalFormat decimalFormat= new DecimalFormat( ".00" );
                tvFinishPlan.setText(decimalFormat.format(plan));
                // 当前填报时间在计划时间之内
                Logger.t("打印当前时间 4").e(addDate);
            } else {
                tvFinishPlan.setText("0");
            }

        } else {
            // 为差值计算方式
            if (durationRight.toDays() >= 0) {
                tvFinishPlan.setText(edtAll.getText());
                return;
            }
            if (durationLeft.toDays() >= 0 && durationRight.toDays() <= 0) {
                long sum = handleDateDuration(startDate, endDate) + 1;
                long average = Long.parseLong(edtAll.getText().toString()) / sum;
                long plan = average * (durationLeft.toDays() + 1);
                tvFinishPlan.setText(plan + "");
                // 当前填报时间在计划时间之内
                Logger.t("打印当前时间 2,3 ").e(addDate);
            } else {
                tvFinishPlan.setText("0");
            }

        }


    }

    /**
     * 新建填报
     */
    private void setUnit() {
        SetUnitDto setUnitDto = new SetUnitDto();
        setUnitDto.setUnitType(showTypeDto.getType());
        setUnitDto.setUID(tasksBean.getUID());
        setUnitDto.setToken(Hawk.get(CustomParam.Token));
        if (showTypeDto.getType() == 4) {
            String unit = edtAllMin.getText().toString() + "," + edtAll.getText().toString();
            setUnitDto.setUnitValue(unit);
        } else {
            setUnitDto.setUnitValue(edtAll.getText().toString());
        }


        schedualController.setUnit(JsonHelper.toJson(setUnitDto), new CallBack<AddTaskResultDto>() {

            @Override
            public void onSuccess(AddTaskResultDto addTaskResultDto) {
                if (addTaskResultDto.getRet() == 1) {

                    addTask();
                } else {
                    showMsg(addTaskResultDto.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });


    }

    /**
     * 处理进度计划状态
     */
    private void handleState(String actualStr) {
        // 实际完成进度
        float actualFinish = Float.parseFloat(actualStr);
        float planFinish = Float.parseFloat(tvFinishPlan.getText().toString());
        if (actualFinish > planFinish) {
            tvState.setText("超前");
        } else if (actualFinish == planFinish) {
            tvState.setText("正常");
        } else {
            tvState.setText("滞后");
        }

    }

    /**
     * 对比float是否相等
     *
     * @param a
     * @param b
     * @param epsilon
     * @return
     */
    public boolean areFloatsEqual(float a, float b, float epsilon) {
        return Math.abs(a - b) < epsilon;
    }
}
