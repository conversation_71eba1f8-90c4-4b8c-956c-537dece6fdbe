package com.probim.bimenew.activity.check;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import com.probim.bimenew.R;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/12/22/22:16
 */
public class CheckSLAdapter extends RecyclerView.Adapter<CheckSLAdapter.VHolder> {
  private final List<String> stringList;
  private final LayoutInflater layoutInflater;
  private final Context mContext;
  private setOnItemClickListener onItemClickListener;

  public CheckSLAdapter(List<String> stringList, Context mContext) {
    this.stringList = stringList;
    this.mContext = mContext;
    layoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public VHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
    return new VHolder(layoutInflater.inflate(R.layout.item_rv_s_l, viewGroup, false));
  }

  @Override public void onBindViewHolder(@NonNull VHolder vHolder, int i) {
    String str = stringList.get(i);
    vHolder.textView.setText(str);
    vHolder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        onItemClickListener.onClick(str);
      }
    });
  }

  @Override public int getItemCount() {
    return stringList.size();
  }

  public void setOnItemClickListener(setOnItemClickListener setOnItemClickListener) {
    this.onItemClickListener = setOnItemClickListener;
  }

  interface setOnItemClickListener {
    void onClick(String string);
  }

  public class VHolder extends RecyclerView.ViewHolder {

    public TextView textView;

    public VHolder(@NonNull View itemView) {
      super(itemView);
      textView = itemView.findViewById(R.id.tv_s_l);
    }
  }
}
