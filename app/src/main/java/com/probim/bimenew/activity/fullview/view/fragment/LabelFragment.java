package com.probim.bimenew.activity.fullview.view.fragment;

import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.activity.fullview.adapter.AllLabelAdapter;
import com.probim.bimenew.activity.fullview.controller.FullviewController;
import com.probim.bimenew.activity.fullview.dto.FullviewLableDTO;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.net.CallBack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 标签弹窗
 */
public class LabelFragment extends DialogFragment {
    private List<FullviewLableDTO.DataDTO> mDatas = new ArrayList<>();
    private String schedualOrganizeId;
    private FullviewController fullviewController = new FullviewController();
    private AllLabelAdapter allLabelAdapter;

    public static LabelFragment newInstance() {


        Bundle args = new Bundle();

        LabelFragment fragment = new LabelFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View layoutView = inflater.inflate(R.layout.fragment_fullview_label, container, false);
        initView(layoutView);
        return layoutView;

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        getProjectDao();
        getLable();
        super.onViewCreated(view, savedInstanceState);

    }

    @Override
    public void onResume() {
        super.onResume();
        if (getDialog() == null) {
            return;
        }
        Window window = getDialog().getWindow();
        if (window == null) {
            return;
        }
        window.setBackgroundDrawableResource(android.R.color.transparent);

        WindowManager.LayoutParams params = window.getAttributes();
        params.gravity = Gravity.BOTTOM;
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        window.setAttributes(params);
    }

    private void initView(View view) {
        RecyclerView recyclerView = view.findViewById(R.id.rv_label);
        allLabelAdapter = new AllLabelAdapter(mDatas);
        allLabelAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {

            }

            @Override
            public void OnDelete(int pos, Object o) {

            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalRecyclerView.initialize(recyclerView).setAdapter(allLabelAdapter);
    }

    /**
     * 获取全部标签
     */
    private void getLable() {

        HashMap<String, String> parmas = new HashMap<>();
        parmas.put("OrganizeId", schedualOrganizeId);
        fullviewController.getLabelList(parmas, new CallBack<FullviewLableDTO>() {
            @Override
            public void onSuccess(FullviewLableDTO fullviewLableDTO) {
                if (fullviewLableDTO.getData() != null) {
                    if (!mDatas.isEmpty()) {
                        mDatas.clear();
                    }
                    mDatas.addAll(fullviewLableDTO.getData());
                    allLabelAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 从数据库获取数据
     */
    private void getProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            schedualOrganizeId = bean.getBimProjectId();

        }
    }

    public void showDialog(FragmentManager supportFragmentManager, String tag) {
        //避免重复添加的异常 java.lang.IllegalStateException: Fragment already added
        Fragment fragment = supportFragmentManager.findFragmentByTag(tag);
        if (fragment != null) {
            FragmentTransaction fragmentTransaction = supportFragmentManager.beginTransaction();
            fragmentTransaction.remove(fragment);
            fragmentTransaction.commitAllowingStateLoss();
        }
        //避免状态丢失的异常 java.lang.IllegalStateException: Can not perform this action after onSaveInstanceState
        try {
            super.show(supportFragmentManager, tag);
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

}
