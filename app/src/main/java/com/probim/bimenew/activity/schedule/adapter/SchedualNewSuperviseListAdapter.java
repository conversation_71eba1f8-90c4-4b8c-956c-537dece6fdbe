package com.probim.bimenew.activity.schedule.adapter;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualNewSuperviseDTO;
import com.probim.bimenew.interfaces.IOnItemSchedualSuperviseClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualNewSuperviseListAdapter extends RecyclerView.Adapter<SchedualNewSuperviseListAdapter.ViewHolder> {

    private List<SchedualNewSuperviseDTO> dtoList = new ArrayList<>();
    private IOnItemSchedualSuperviseClickListener onItemClickListener;

    public SchedualNewSuperviseListAdapter(List<SchedualNewSuperviseDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_supervise, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewSuperviseDTO dto = dtoList.get(position);
        int xx = position + 1;
        holder.tvTitle.setText(xx + "");
        holder.edtSupervise.clearFocus();
        holder.edtSupervise.setText(dto.getMobileSupervise_Name());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });

        holder.edtSupervise.clearFocus();
        holder.edtSupervise.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (onItemClickListener != null) {
                    onItemClickListener.showEdtChange(position, editable.toString());
                }

            }
        });
        holder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onDelete(position, dto);
            }
        });

    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualSuperviseClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_supervise_title)
        TextView tvTitle;
        @BindView(R.id.edt_supervise)
        EditText edtSupervise;
        @BindView(R.id.iv_delete_item)
        ImageView ivDelete;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
