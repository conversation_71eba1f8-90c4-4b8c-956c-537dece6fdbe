package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;
import com.umeng.commonsdk.debug.D;

public class AddTaskDto {
    public AddTaskDto() {
    }

    private String Progress_ID;

    public String getProgress_ID() {
        return Progress_ID;
    }

    public void setProgress_ID(String progress_ID) {
        Progress_ID = progress_ID;
    }

    private String token;
    @SerializedName("Progress_Name")
    private String progress_Name;
    @SerializedName("Progress_state")
    private String progress_state;
    @SerializedName("Progress_createuser")
    private String progress_createuser;
    @SerializedName("Progress_createuserid")
    private String progress_createuserid;
    @SerializedName("Progress_planstarttime")
    private String progress_planstarttime;
    @SerializedName("Progress_plannendtime")
    private String progress_plannendtime;
    @SerializedName("Progress_actualstarttime")
    private String progress_actualstarttime;
    @SerializedName("Progress_actualendtime")
    private String progress_actualendtime;
    @SerializedName("Progress_planfate")
    private Integer progress_planfate;
    @SerializedName("Progress_planratio")
    private Double progress_planratio;
    @SerializedName("Progress_actualratio")
    private float progress_actualratio;
    @SerializedName("Progress_treeID")
    private String progress_treeID;
    @SerializedName("Progress_ProjectID")
    private String progress_ProjectID;
    @SerializedName("Progress_parentid")
    private String progress_parentid;
    @SerializedName("Progress_unittime")
    private String progress_unittime;
    @SerializedName("IsSubmitAudit")
    private Boolean isSubmitAudit;
    @SerializedName("Progress_planvalue")
    private Float progress_planvalue;
    @SerializedName("Progress_actualvalue")
    private Double progress_actualvalue;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getProgress_Name() {
        return progress_Name;
    }

    public void setProgress_Name(String progress_Name) {
        this.progress_Name = progress_Name;
    }

    public String getProgress_state() {
        return progress_state;
    }

    public void setProgress_state(String progress_state) {
        this.progress_state = progress_state;
    }

    public String getProgress_createuser() {
        return progress_createuser;
    }

    public void setProgress_createuser(String progress_createuser) {
        this.progress_createuser = progress_createuser;
    }

    public String getProgress_createuserid() {
        return progress_createuserid;
    }

    public void setProgress_createuserid(String progress_createuserid) {
        this.progress_createuserid = progress_createuserid;
    }

    public String getProgress_planstarttime() {
        return progress_planstarttime;
    }

    public void setProgress_planstarttime(String progress_planstarttime) {
        this.progress_planstarttime = progress_planstarttime;
    }

    public String getProgress_plannendtime() {
        return progress_plannendtime;
    }

    public void setProgress_plannendtime(String progress_plannendtime) {
        this.progress_plannendtime = progress_plannendtime;
    }

    public String getProgress_actualstarttime() {
        return progress_actualstarttime;
    }

    public void setProgress_actualstarttime(String progress_actualstarttime) {
        this.progress_actualstarttime = progress_actualstarttime;
    }

    public String getProgress_actualendtime() {
        return progress_actualendtime;
    }

    public void setProgress_actualendtime(String progress_actualendtime) {
        this.progress_actualendtime = progress_actualendtime;
    }

    public Integer getProgress_planfate() {
        return progress_planfate;
    }

    public void setProgress_planfate(Integer progress_planfate) {
        this.progress_planfate = progress_planfate;
    }

    public Double getProgress_planratio() {
        return progress_planratio;
    }

    public void setProgress_planratio(Double progress_planratio) {
        this.progress_planratio = progress_planratio;
    }

    public float getProgress_actualratio() {
        return progress_actualratio;
    }

    public void setProgress_actualratio(float progress_actualratio) {
        this.progress_actualratio = progress_actualratio;
    }

    public String getProgress_treeID() {
        return progress_treeID;
    }

    public void setProgress_treeID(String progress_treeID) {
        this.progress_treeID = progress_treeID;
    }

    public String getProgress_ProjectID() {
        return progress_ProjectID;
    }

    public void setProgress_ProjectID(String progress_ProjectID) {
        this.progress_ProjectID = progress_ProjectID;
    }

    public String getProgress_parentid() {
        return progress_parentid;
    }

    public void setProgress_parentid(String progress_parentid) {
        this.progress_parentid = progress_parentid;
    }

    public String getProgress_unittime() {
        return progress_unittime;
    }

    public void setProgress_unittime(String progress_unittime) {
        this.progress_unittime = progress_unittime;
    }

    public Boolean getIsSubmitAudit() {
        return isSubmitAudit;
    }

    public void setIsSubmitAudit(Boolean isSubmitAudit) {
        this.isSubmitAudit = isSubmitAudit;
    }

    public Float getProgress_planvalue() {
        return progress_planvalue;
    }

    public void setProgress_planvalue(Float progress_planvalue) {
        this.progress_planvalue = progress_planvalue;
    }

    public Double getProgress_actualvalue() {
        return progress_actualvalue;
    }

    public void setProgress_actualvalue(Double progress_actualvalue) {
        this.progress_actualvalue = progress_actualvalue;
    }
}
