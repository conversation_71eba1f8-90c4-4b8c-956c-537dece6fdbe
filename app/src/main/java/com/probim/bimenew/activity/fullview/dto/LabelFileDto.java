package com.probim.bimenew.activity.fullview.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class LabelFileDto {

    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private Object msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("Id")
        private String id;
        @SerializedName("CreateTime")
        private String createTime;
        @SerializedName("ModifyTime")
        private String modifyTime;
        @SerializedName("OrganizeId")
        private String organizeId;
        @SerializedName("LabelId")
        private String labelId;
        @SerializedName("FileName")
        private String fileName;
        @SerializedName("FileExtension")
        private String fileExtension;
        @SerializedName("FilePath")
        private String filePath;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getModifyTime() {
            return modifyTime;
        }

        public void setModifyTime(String modifyTime) {
            this.modifyTime = modifyTime;
        }

        public String getOrganizeId() {
            return organizeId;
        }

        public void setOrganizeId(String organizeId) {
            this.organizeId = organizeId;
        }

        public String getLabelId() {
            return labelId;
        }

        public void setLabelId(String labelId) {
            this.labelId = labelId;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getFileExtension() {
            return fileExtension;
        }

        public void setFileExtension(String fileExtension) {
            this.fileExtension = fileExtension;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }
    }
}
