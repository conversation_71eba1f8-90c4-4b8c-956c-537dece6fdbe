package com.probim.bimenew.activity.fullview.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class AllFullviewDTO2 {

    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("LabelId")
        private String labelId;
        @SerializedName("LabelName")
        private String labelName;
        @SerializedName("BackGroundColor")
        private String backGroundColor;
        @SerializedName("FontColor")
        private String fontColor;
        @SerializedName("Panoramas")
        private List<PanoramasDTO> panoramas;

        public String getLabelId() {
            return labelId;
        }

        public void setLabelId(String labelId) {
            this.labelId = labelId;
        }

        public String getLabelName() {
            return labelName;
        }

        public void setLabelName(String labelName) {
            this.labelName = labelName;
        }

        public String getBackGroundColor() {
            return backGroundColor;
        }

        public void setBackGroundColor(String backGroundColor) {
            this.backGroundColor = backGroundColor;
        }

        public String getFontColor() {
            return fontColor;
        }

        public void setFontColor(String fontColor) {
            this.fontColor = fontColor;
        }

        public List<PanoramasDTO> getPanoramas() {
            return panoramas;
        }

        public void setPanoramas(List<PanoramasDTO> panoramas) {
            this.panoramas = panoramas;
        }

        public static class PanoramasDTO {
            @SerializedName("PqFlag")
            private int pqFlag;
            @SerializedName("PbBIMComposerId")
            private Object pbBIMComposerId;
            @SerializedName("PbOrganizeName")
            private Object pbOrganizeName;
            @SerializedName("LabelId")
            private String labelId;
            @SerializedName("LabelName")
            private String labelName;
            @SerializedName("LabelSort")
            private int labelSort;
            @SerializedName("BackGroundColor")
            private String backGroundColor;
            @SerializedName("FontColor")
            private String fontColor;
            @SerializedName("PbGuid")
            private String pbGuid;
            @SerializedName("PbUrl")
            private String pbUrl;
            @SerializedName("PbName")
            private String pbName;
            @SerializedName("PbUpdatetime")
            private String pbUpdatetime;
            @SerializedName("PbOrganizeId")
            private String pbOrganizeId;
            @SerializedName("PbIsdel")
            private int pbIsdel;
            @SerializedName("PbGisinfo")
            private String pbGisinfo;

            public int getPqFlag() {
                return pqFlag;
            }

            public void setPqFlag(int pqFlag) {
                this.pqFlag = pqFlag;
            }

            public Object getPbBIMComposerId() {
                return pbBIMComposerId;
            }

            public void setPbBIMComposerId(Object pbBIMComposerId) {
                this.pbBIMComposerId = pbBIMComposerId;
            }

            public Object getPbOrganizeName() {
                return pbOrganizeName;
            }

            public void setPbOrganizeName(Object pbOrganizeName) {
                this.pbOrganizeName = pbOrganizeName;
            }

            public String getLabelId() {
                return labelId;
            }

            public void setLabelId(String labelId) {
                this.labelId = labelId;
            }

            public String getLabelName() {
                return labelName;
            }

            public void setLabelName(String labelName) {
                this.labelName = labelName;
            }

            public int getLabelSort() {
                return labelSort;
            }

            public void setLabelSort(int labelSort) {
                this.labelSort = labelSort;
            }

            public String getBackGroundColor() {
                return backGroundColor;
            }

            public void setBackGroundColor(String backGroundColor) {
                this.backGroundColor = backGroundColor;
            }

            public String getFontColor() {
                return fontColor;
            }

            public void setFontColor(String fontColor) {
                this.fontColor = fontColor;
            }

            public String getPbGuid() {
                return pbGuid;
            }

            public void setPbGuid(String pbGuid) {
                this.pbGuid = pbGuid;
            }

            public String getPbUrl() {
                return pbUrl;
            }

            public void setPbUrl(String pbUrl) {
                this.pbUrl = pbUrl;
            }

            public String getPbName() {
                return pbName;
            }

            public void setPbName(String pbName) {
                this.pbName = pbName;
            }

            public String getPbUpdatetime() {
                return pbUpdatetime;
            }

            public void setPbUpdatetime(String pbUpdatetime) {
                this.pbUpdatetime = pbUpdatetime;
            }

            public String getPbOrganizeId() {
                return pbOrganizeId;
            }

            public void setPbOrganizeId(String pbOrganizeId) {
                this.pbOrganizeId = pbOrganizeId;
            }

            public int getPbIsdel() {
                return pbIsdel;
            }

            public void setPbIsdel(int pbIsdel) {
                this.pbIsdel = pbIsdel;
            }

            public String getPbGisinfo() {
                return pbGisinfo;
            }

            public void setPbGisinfo(String pbGisinfo) {
                this.pbGisinfo = pbGisinfo;
            }
        }
    }
}
