package com.probim.bimenew.activity.check;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.activity.NewCheckActivity;
import com.probim.bimenew.adapter.AllTaskAdapter;
import com.probim.bimenew.adapter.TaskItemAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.AllOriginTaskItemDto;
import com.probim.bimenew.result.AllTaskItemDto;
import com.probim.bimenew.result.AllTaskItemResult;
import com.probim.bimenew.result.AllTaskResult;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Description :任务列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/3/31/10:28
 */
public class AllTaskListActivity extends BaseActivity implements View.OnClickListener {

    List<AllTaskItemResult.DataBean.TasksBean> tasks = new ArrayList<>();
    List<AllTaskItemResult.DataBean.TasksBean> itemTasks = new ArrayList<>();
    private List<AllTaskResult.DataBean> allList = new ArrayList<>();
    private List<AllTaskItemDto> allItemList = new ArrayList<>();
    private List<AllOriginTaskItemDto> originList = new ArrayList<>();
    private String organizeId;
    private AllTaskAdapter allAdapter;
    private int index = -1;
    private RecyclerView rvProjectStructure;
    private boolean isFirst = true;
    private boolean isOnItem = false;
    private CheckController checkController;
    private String projectID;
    private boolean isTaskFrom;
    private boolean isDetails;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_project_structure);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
        GetProjectDao();
        checkController = new CheckController();
        getCheckTask();
    }

    @Override
    protected void initView() {
        if (getIntent() != null) {
            isTaskFrom = getIntent().getBooleanExtra("isTask", false);
            isDetails = getIntent().getBooleanExtra("isCheckDetails", false);
        }
        TextView tvTittle = findViewById(R.id.tv_black_title);
        tvTittle.setText("任务列表");
        TextView tvRight = findView(R.id.tv_right, this);
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("完成");
        LinearLayout liniBack = findView(R.id.lin_back, this);

        //初始化全部文件夹适配器
        setAllAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();
            projectID = bean.getProjectID();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.lin_back:
                finish();
                //backStepToList();

                break;
            case R.id.tv_right:

                Intent intent = null;
                if (isDetails) {
                    intent = new Intent(this, CheckDetailsActivity.class);
                } else {
                    intent = new Intent(this, NewCheckActivity.class);
                }
                Bundle bundle = new Bundle();
                bundle.putSerializable(CustomParam.StartForResult, (Serializable) getItemSelectedList());
                //intent.putExtra(CustomParam.StartForResult, getSelectedList());
                intent.putExtras(bundle);
                setResult(RESULT_OK, intent);
                finish();
                break;
        }
    }

    /**
     * 获取选中数据
     */
    private List<AllOriginTaskItemDto> getSelectedList() {

        List<AllOriginTaskItemDto> isSelectedAllList = new ArrayList<>();

        for (int i = 0; i < allItemList.size(); i++) {

            if (allItemList.get(i).isSelected()) {

                isSelectedAllList.add(originList.get(i));
            }
        }
        return isSelectedAllList;
    }

    /**
     * 获取选中数据 传值回新建任务界面
     */
    private List<AllTaskItemResult.DataBean.TasksBean> getItemSelectedList() {

        List<AllTaskItemResult.DataBean.TasksBean> temp = new ArrayList<>();
        for (int i = 0; i < tasks.size(); i++) {
            if (tasks.get(i).isSelected()) {
                temp.add(tasks.get(i));
            }
        }
        return temp;

    }

    /**
     * 设置新的适配器
     */
    private void setItemNewAdapter() {

        TaskItemAdapter itemAdapter = new TaskItemAdapter(this, isTaskFrom, tasks);
        VerticalRecyclerView.initialize(rvProjectStructure).setAdapter(itemAdapter);
        itemAdapter.addRecycleItemListener(new TaskItemAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                if (!tasks.get(pos).getChildren().isEmpty()) {
                    List<AllTaskItemResult.DataBean.TasksBean> tempTasks = new ArrayList<>();
                    tempTasks.addAll(tasks.get(pos).getChildren());
                    tasks.clear();
                    tasks.addAll(tempTasks);
                    itemAdapter.notifyDataSetChanged();
                }

            }
        });
        rvProjectStructure.setAdapter(itemAdapter);
    }

    /**
     * 设置文件夹适配器
     */
    private void setAllAdapter() {

        rvProjectStructure = findViewById(R.id.rv_project_structure);
        allAdapter = new AllTaskAdapter(this, allList);
        allAdapter.addRecycleItemListener(new AllTaskAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                String planId = (String) o;
                getTaskItemData(planId);
            }
        });
        VerticalRecyclerView.initialize(rvProjectStructure).setAdapter(allAdapter);
        rvProjectStructure.setAdapter(allAdapter);
    }

    /**
     * 获取详细item
     */
    private void getTaskItemData(String planId) {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        params.put("planId", planId);
        checkController.getCheckTaskItem(params, new CallBack<AllTaskItemResult>() {
            @Override
            public void onSuccess(AllTaskItemResult allTaskItemResult) {

                List<AllTaskItemResult.DataBean.TasksBean> tasksData = allTaskItemResult.getData().getTasks();

                if (tasksData.size() != 0) {
                    for (int i = 0; i < tasksData.size(); i++) {
                        //PARENTTASKUID_ 为-1时 显示数据
                        if ("-1".equals(tasksData.get(i).getPARENTTASKUID_())) {
                            tasks.add(tasksData.get(i));
                        }
                    }


                    for (int i = 0; i < tasks.size(); i++) {
                        // 递归数据查找每条数据的
                        filterData(tasks.get(i), tasksData);
                    }

                    Logger.t("组合数据----->").e(new Gson().toJson(tasks));
/*
                    Type originCollectionType = new TypeToken<List<AllOriginTaskItemDto>>() {
                    }.getType();
                    List<AllOriginTaskItemDto> originEnums = new Gson().fromJson(JsonHelper.toJson(tasks), originCollectionType);
                    originList.addAll(originEnums);
                    // 循环遍历数据 设置isSelected 值为false
                    for (AllTaskItemResult.DataBean.TasksBean bean : tasks) {
                        bean.setSelected(false);
                    }

                    Type allCollectionType = new TypeToken<List<AllTaskItemDto>>() {
                    }.getType();
                    List<AllTaskItemDto> allEnums = new Gson().fromJson(JsonHelper.toJson(tasks), allCollectionType);
                    allItemList.addAll(allEnums);*/
                    // 替换新的适配器  解决数据不统一问题

                    setItemNewAdapter();
                    mLoading.dismiss();
                } else {
                    mLoading.dismiss();
                    showMsg("暂无数据");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 递归
     *
     * @param item
     * @param tasksData
     */
    private void filterData(AllTaskItemResult.DataBean.TasksBean item, List<AllTaskItemResult.DataBean.TasksBean> tasksData) {
        List<AllTaskItemResult.DataBean.TasksBean> hasChildrenData = new ArrayList<>();
        for (int i = 0; i < tasksData.size(); i++) {
            if (item.getUID_().equals(tasksData.get(i).getPARENTTASKUID_())) {
                //为子集
                hasChildrenData.add(tasksData.get(i));
            }

        }

        if (hasChildrenData.size() > 0) {
            item.setChildren(hasChildrenData);

            if (item.getChildren().size() > 0) {
                for (int i = 0; i < item.getChildren().size(); i++) {
                    filterData(item.getChildren().get(i), tasksData);
                }
            }

        } else {

            item.setChildren(new ArrayList<>());
        }


    }

    /**
     * 获取任务列表
     */
    private void getCheckTask() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", projectID);
        checkController.getCheckTask(params, new CallBack<AllTaskResult>() {
            @Override
            public void onSuccess(AllTaskResult checkTaskResult) {

                allList.addAll(checkTaskResult.getData());
                allAdapter.notifyDataSetChanged();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
}
