package com.probim.bimenew.activity.schedule.modules;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;

import java.util.ArrayList;

public class DemoActivity extends AppCompatActivity {
    private final int TYPE_GROUP = 0xa01;
    private final int TYPE_CHILD = 0xa02;
    private String[] groupNames = {"A", "B", "C", "D", "E", "F", "G"};
    private ArrayList<Item> mItems;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        mItems = new ArrayList<>();
        for (int i = 0; i < groupNames.length; i++) {
            Group group = new Group();
            group.position = i;
            group.title = groupNames[i];
            mItems.add(group);

            int count = (int) (Math.random() * 10) % 4 + 1;
            for (int j = 0; j < count; j++) {
                Child child = new Child();
                child.position = j;
                child.groupPos = i;
                child.groupName = group.title;
                mItems.add(child);
            }
        }

//        RecyclerView mRecyclerView = findViewById(R.id.recycler_view);

        //GridLayoutManager layoutManage = new GridLayoutManager(this, 4);
        LinearLayoutManager layoutManage = new LinearLayoutManager(this);
        layoutManage.setOrientation(LinearLayoutManager.VERTICAL);

//        mRecyclerView.setLayoutManager(layoutManage);

        RecyclerViewAdapter mAdapter = new RecyclerViewAdapter();
//        mRecyclerView.setAdapter(mAdapter);
    }

    public class RecyclerViewAdapter extends RecyclerView.Adapter<ItemVH> {

        @Override
        public ItemVH onCreateViewHolder(ViewGroup parent, int viewType) {
            View view;
            ItemVH itemVH = null;
            switch (viewType) {
                case TYPE_GROUP:
                    view = LayoutInflater.from(parent.getContext()).inflate(android.R.layout.simple_list_item_1, parent, false);
                    itemVH = new GroupVH(view);
                    break;

                case TYPE_CHILD:
                    view = LayoutInflater.from(parent.getContext()).inflate(android.R.layout.simple_list_item_2, parent, false);
                    itemVH = new ChildVH(view);
                    break;
            }

            return itemVH;
        }

        @Override
        public void onBindViewHolder(ItemVH holder, int position) {
            Item item = mItems.get(position);
            switch (getItemViewType(position)) {
                case TYPE_GROUP:
                    Group g = (Group) item;
                    GroupVH groupVH = (GroupVH) holder;
                    groupVH.text1.setText(g.title);
                    break;

                case TYPE_CHILD:
                    Child c = (Child) item;
                    ChildVH childVH = (ChildVH) holder;
                    childVH.text1.setText(c.groupName);
                    childVH.text2.setText(c.position + "");
                    break;
            }
        }

        @Override
        public int getItemCount() {
            return mItems.size();
        }

        @Override
        public int getItemViewType(int position) {
            return mItems.get(position).getType();
        }
    }

    private class Group extends Item {
        public String title;

        @Override
        public int getType() {
            return TYPE_GROUP;
        }
    }

    private class Child extends Item {
        public int groupPos;
        public String groupName;

        @Override
        public int getType() {
            return TYPE_CHILD;
        }
    }

    private abstract class Item {
        public int position;

        public abstract int getType();
    }

    private class GroupVH extends ItemVH {
        public TextView text1;

        public GroupVH(View itemView) {
            super(itemView);
            text1 = itemView.findViewById(android.R.id.text1);
            text1.setBackgroundColor(Color.RED);
        }

        @Override
        public int getType() {
            return TYPE_GROUP;
        }
    }

    private class ChildVH extends ItemVH {
        public TextView text1;
        public TextView text2;

        public ChildVH(View itemView) {
            super(itemView);
            text1 = itemView.findViewById(android.R.id.text1);
            text2 = itemView.findViewById(android.R.id.text2);
            text1.setTextColor(Color.LTGRAY);
            text2.setTextColor(Color.BLUE);
        }

        @Override
        public int getType() {
            return TYPE_CHILD;
        }
    }

    private abstract class ItemVH extends RecyclerView.ViewHolder {
        public ItemVH(View itemView) {
            super(itemView);
        }

        public abstract int getType();
    }

}
