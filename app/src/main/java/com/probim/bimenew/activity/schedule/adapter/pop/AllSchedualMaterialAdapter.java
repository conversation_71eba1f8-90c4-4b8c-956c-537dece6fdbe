package com.probim.bimenew.activity.schedule.adapter.pop;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualMaterialDTO;
import com.probim.bimenew.interfaces.IOnItemClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class AllSchedualMaterialAdapter extends RecyclerView.Adapter<AllSchedualMaterialAdapter.ViewHolder> {

    private List<SchedualMaterialDTO.DataDTO> dtoList = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;

    public AllSchedualMaterialAdapter(List<SchedualMaterialDTO.DataDTO> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_schedual_people, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SchedualMaterialDTO.DataDTO dto = dtoList.get(position);
        holder.tvPeopelName.setText(dto.getMachine_Name());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_people_type)
        TextView tvPeopelName;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
