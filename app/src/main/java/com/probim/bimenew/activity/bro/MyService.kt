package com.probim.bimenew.activity.bro

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.probim.bimenew.R
import com.probim.bimenew.activity.MainActivity

class MyService : Service() {

    override fun onCreate() {
        super.onCreate()
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(this, 0, intent, 0)
        val channelId = "001"
        //适配Android 8.0
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val mChannel = NotificationChannel(channelId, "test", NotificationManager.IMPORTANCE_HIGH)
            val ntfManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            ntfManager.createNotificationChannel(mChannel)
        }
        val notification = NotificationCompat.Builder(this, channelId)
                .setContentTitle("前台服务通知的标题")
                .setContentText("前台服务通知的文字")
                .setContentIntent(pendingIntent)
                .setWhen(System.currentTimeMillis())
                .setSmallIcon(R.mipmap.ic_launcher)
                .setLargeIcon(BitmapFactory.decodeResource(resources, R.mipmap.ic_launcher))
                .build()
        startForeground(1, notification)
    }

    override fun onBind(intent: Intent?): IBinder? {
        TODO("Not yet implemented")
    }
}

