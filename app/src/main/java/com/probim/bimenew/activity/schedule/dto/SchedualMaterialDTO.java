package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SchedualMaterialDTO {

    @SerializedName("Ret")
    private int ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private List<DataDTO> data;

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public static class DataDTO {
        @SerializedName("Machine_ID")
        private int machine_ID;
        @SerializedName("Token")
        private Object token;
        @SerializedName("Machine_Name")
        private String machine_Name;
        @SerializedName("Machine_PlanNum")
        private String machine_PlanNum;
        @SerializedName("Machine_Unit")
        private String machine_Unit;
        private String organizeId;
        @SerializedName("Machine_AddNum")
        private String machine_AddNum;
        @SerializedName("Machine_Size")
        private String machine_Size;
        @SerializedName("Machine_Adress")
        private String machine_Adress;

        public int getMachine_ID() {
            return machine_ID;
        }

        public void setMachine_ID(int machine_ID) {
            this.machine_ID = machine_ID;
        }

        public Object getToken() {
            return token;
        }

        public void setToken(Object token) {
            this.token = token;
        }

        public String getMachine_Name() {
            return machine_Name;
        }

        public void setMachine_Name(String machine_Name) {
            this.machine_Name = machine_Name;
        }

        public String getMachine_PlanNum() {
            return machine_PlanNum;
        }

        public void setMachine_PlanNum(String machine_PlanNum) {
            this.machine_PlanNum = machine_PlanNum;
        }

        public String getMachine_Unit() {
            return machine_Unit;
        }

        public void setMachine_Unit(String machine_Unit) {
            this.machine_Unit = machine_Unit;
        }

        public String getOrganizeId() {
            return organizeId;
        }

        public void setOrganizeId(String organizeId) {
            this.organizeId = organizeId;
        }

        public String getMachine_AddNum() {
            return machine_AddNum;
        }

        public void setMachine_AddNum(String machine_AddNum) {
            this.machine_AddNum = machine_AddNum;
        }

        public String getMachine_Size() {
            return machine_Size;
        }

        public void setMachine_Size(String machine_Size) {
            this.machine_Size = machine_Size;
        }

        public String getMachine_Adress() {
            return machine_Adress;
        }

        public void setMachine_Adress(String machine_Adress) {
            this.machine_Adress = machine_Adress;
        }
    }
}
