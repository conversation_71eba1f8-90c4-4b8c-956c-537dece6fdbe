package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SchedualNewMaterialDTO {


    @SerializedName("MachineDetial_Name")
    private String machineDetial_Name;
    @SerializedName("MachineDetial_Parts")
    private String machineDetial_Parts;
    @SerializedName("MachineDetial_Size")
    private String machineDetial_Size;
    @SerializedName("MachineDetial_Unit")
    private String machineDetial_Unit;
    @SerializedName("MachineDetial_Sum")
    private String machineDetial_Sum;
    @SerializedName("MachineDetial_Approcach")
    private String machineDetial_Approcach;
    @SerializedName("MachineDetial_Add")
    private String machineDetial_Add;
    @SerializedName("MachineDetial_Product")
    private String machineDetial_Product;
    @SerializedName("MachineDetial_Estimated")
    private String machineDetial_Estimated;
    @SerializedName("MachineDetial_Remark")
    private String machineDetial_Remark;
    @SerializedName("MachineDetial_ProjectID")
    private String machineDetial_ProjectID;
    @SerializedName("MachineDetial_Unittime")
    private String machineDetial_Unittime;
    @SerializedName("MachineDetial_Createuser")
    private String machineDetial_Createuser;
    private String organizeId;
    private String machineDetial_PartsName;

    public String getMachineDetial_PartsName() {
        return machineDetial_PartsName;
    }

    public void setMachineDetial_PartsName(String machineDetial_PartsName) {
        this.machineDetial_PartsName = machineDetial_PartsName;
    }

    public String getMachineDetial_Name() {
        return machineDetial_Name;
    }

    public void setMachineDetial_Name(String machineDetial_Name) {
        this.machineDetial_Name = machineDetial_Name;
    }

    public String getMachineDetial_Parts() {
        return machineDetial_Parts;
    }

    public void setMachineDetial_Parts(String machineDetial_Parts) {
        this.machineDetial_Parts = machineDetial_Parts;
    }

    public String getMachineDetial_Size() {
        return machineDetial_Size;
    }

    public void setMachineDetial_Size(String machineDetial_Size) {
        this.machineDetial_Size = machineDetial_Size;
    }

    public String getMachineDetial_Unit() {
        return machineDetial_Unit;
    }

    public void setMachineDetial_Unit(String machineDetial_Unit) {
        this.machineDetial_Unit = machineDetial_Unit;
    }

    public String getMachineDetial_Sum() {
        return machineDetial_Sum;
    }

    public void setMachineDetial_Sum(String machineDetial_Sum) {
        this.machineDetial_Sum = machineDetial_Sum;
    }

    public String getMachineDetial_Approcach() {
        return machineDetial_Approcach;
    }

    public void setMachineDetial_Approcach(String machineDetial_Approcach) {
        this.machineDetial_Approcach = machineDetial_Approcach;
    }

    public String getMachineDetial_Add() {
        return machineDetial_Add;
    }

    public void setMachineDetial_Add(String machineDetial_Add) {
        this.machineDetial_Add = machineDetial_Add;
    }

    public String getMachineDetial_Product() {
        return machineDetial_Product;
    }

    public void setMachineDetial_Product(String machineDetial_Product) {
        this.machineDetial_Product = machineDetial_Product;
    }

    public String getMachineDetial_Estimated() {
        return machineDetial_Estimated;
    }

    public void setMachineDetial_Estimated(String machineDetial_Estimated) {
        this.machineDetial_Estimated = machineDetial_Estimated;
    }

    public String getMachineDetial_Remark() {
        return machineDetial_Remark;
    }

    public void setMachineDetial_Remark(String machineDetial_Remark) {
        this.machineDetial_Remark = machineDetial_Remark;
    }

    public String getMachineDetial_ProjectID() {
        return machineDetial_ProjectID;
    }

    public void setMachineDetial_ProjectID(String machineDetial_ProjectID) {
        this.machineDetial_ProjectID = machineDetial_ProjectID;
    }

    public String getMachineDetial_Unittime() {
        return machineDetial_Unittime;
    }

    public void setMachineDetial_Unittime(String machineDetial_Unittime) {
        this.machineDetial_Unittime = machineDetial_Unittime;
    }

    public String getMachineDetial_Createuser() {
        return machineDetial_Createuser;
    }

    public void setMachineDetial_Createuser(String machineDetial_Createuser) {
        this.machineDetial_Createuser = machineDetial_Createuser;
    }

    public String getOrganizeId() {
        return organizeId;
    }

    public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
    }
}
