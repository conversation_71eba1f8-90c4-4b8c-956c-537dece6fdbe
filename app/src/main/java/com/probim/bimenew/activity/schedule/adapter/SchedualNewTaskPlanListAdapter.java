package com.probim.bimenew.activity.schedule.adapter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualNewTaskPlanDTO;
import com.probim.bimenew.interfaces.IOnItemSchedualTaskPlanClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualNewTaskPlanListAdapter extends RecyclerView.Adapter<SchedualNewTaskPlanListAdapter.ViewHolder> {
    private IOnItemSchedualTaskPlanClickListener onItemClickListener;
    private List<SchedualNewTaskPlanDTO> dtoList = new ArrayList<>();

    public SchedualNewTaskPlanListAdapter(List<SchedualNewTaskPlanDTO> dtoList) {
        this.dtoList = dtoList;
    }


    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_new_task_plan, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewTaskPlanDTO dto = dtoList.get(position);
        int temp = position + 1;
        holder.tvName.setText("任务" + temp);
        holder.tvTaskName.setText(dto.getProgress_Name());
        holder.edtActualratio.setText(dto.getProgress_actualratio());
        holder.edtAddratio.setText(dto.getProgress_MobileAddratio());
        holder.edtPlantratio.setText(dto.getProgress_planratio());
        holder.edtTomorrowratio.setText(dto.getProgress_MobileTomorrowratio());
        holder.edtWhy.setText(dto.getProgress_MobileReason());
        holder.tvStatus.setText(dto.getProgress_state());
        if ("滞后".equals(dto.getProgress_state())) {
            holder.linWhy.setVisibility(View.VISIBLE);
        }
        holder.tvStartDate.setText(dto.getProgress_planstarttime());
        holder.tvEndDate.setText(dto.getProgress_plannendtime());
        holder.rlSelectTask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onTaskClick(position, dto);

            }
        });
        holder.ivDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onDelete(position, dto);
            }
        });
        holder.edtWhy.clearFocus();
        holder.edtWhy.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    onItemClickListener.showEdtWhyChange(position, editable.toString());
                }
            }
        });
        holder.edtTomorrowratio.clearFocus();
        holder.edtTomorrowratio.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    onItemClickListener.showEdtTomorrowratioChange(position, editable.toString());
                }
            }
        });
        holder.edtPlantratio.clearFocus();
        holder.edtPlantratio.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    onItemClickListener.showEdtPlantratioChange(position, editable.toString());
                }
            }
        });

        holder.edtAddratio.clearFocus();
        holder.edtAddratio.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    onItemClickListener.showEdtAddratioChange(position, editable.toString());
                }
            }
        });


        holder.edtActualratio.clearFocus();
        holder.edtActualratio.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    onItemClickListener.showEdtActualratioChange(position, editable.toString());
                }

            }
        });
        holder.edtActualratio.clearFocus();
        holder.edtActualratio.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (!TextUtils.isEmpty(editable.toString())) {
                    onItemClickListener.showEdtAddratioChange(position, editable.toString());
                }


            }
        });


    }


    @Override
    public int getItemCount() {

        return dtoList.size();
    }

    public void setOnItemClickListener(IOnItemSchedualTaskPlanClickListener listener) {
        this.onItemClickListener = listener;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.rl_select_task)
        RelativeLayout rlSelectTask;
        @BindView(R.id.tv_task_name)
        TextView tvTaskName;
        @BindView(R.id.tv_task_start_date)
        TextView tvStartDate;
        @BindView(R.id.tv_task_end_date)
        TextView tvEndDate;
        @BindView(R.id.tv_task_status)
        TextView tvStatus;
        @BindView(R.id.edt_why)
        EditText edtWhy;
        @BindView(R.id.tv_name)
        TextView tvName;
        @BindView(R.id.edt_planratio)
        EditText edtPlantratio;
        @BindView(R.id.edt_actualratio)
        EditText edtActualratio;
        @BindView(R.id.edt_addratio)
        EditText edtAddratio;
        @BindView(R.id.edt_tomorrowratio)
        EditText edtTomorrowratio;
        @BindView(R.id.lin_why)
        LinearLayout linWhy;
        @BindView(R.id.iv_delete_item)
        ImageView ivDelete;


        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
        }
    }
}


