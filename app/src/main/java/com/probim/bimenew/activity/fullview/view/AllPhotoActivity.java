package com.probim.bimenew.activity.fullview.view;

import android.database.Cursor;
import android.os.Bundle;
import android.provider.MediaStore;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;

import java.util.ArrayList;
import java.util.List;

public class AllPhotoActivity extends BaseActivity {

    private List<String> imagePathList;
    private ImageAdapter adapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_all_photo);
        initRecycleview();
        initData();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initData() {
        // 设置查询的条件
        String[] projection = {MediaStore.Images.Media.DATA};
        String sortOrder = MediaStore.Images.Media.DATE_MODIFIED + " DESC";

        // 查询媒体数据库，获取所有图片的路径
        Cursor cursor = getContentResolver().query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, projection, null, null, sortOrder);

        if (cursor != null) {
            while (cursor.moveToNext()) {
                // 获取图片路径
                String imagePath = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Media.DATA));
                // 对图片路径进行处理，如添加到列表中等
                // URI 转文件
                imagePathList.add(imagePath);
            }

            // 关闭Cursor
            cursor.close();
        }
        initRecycleview();
    }

    @Override
    protected void initRecycleview() {
        // 创建图片路径列表
        imagePathList = new ArrayList<>();
        // 将图片路径列表设置到Adapter中
        adapter = new ImageAdapter(imagePathList);
        // 将Adapter绑定到RecyclerView中
        RecyclerView recyclerView = findViewById(R.id.rv_all_photo);
        recyclerView.setAdapter(adapter);
    }

    @Override
    protected void initRefresh() {

    }
}
