package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.TextView.OnEditorActionListener;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.Project2Adapter;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.LoginController;
import com.probim.bimenew.controller.ProjectController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.ExitDto;
import com.probim.bimenew.dto.ProjectsDto;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.ProjectListResult;
import com.probim.bimenew.result.UserInforResult;
import com.probim.bimenew.utils.ClearCacheUtils;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.OperationFileHelper;
import com.probim.bimenew.utils.SetAliasUtils;
import com.probim.bimenew.utils.UIUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cn.jpush.android.api.JPushInterface;

/**
 * Description :app副界面 Author : Gary Email  : <EMAIL> Date   :
 * 2018/7/1/11:00.
 */
public class MainActivity extends BaseActivity {

    private final List<ProjectsDto.DataBean> list = new ArrayList<>();
    private final List<ProjectListResult.DataBean.RowsBean> list2 = new ArrayList<>();
    private final LoginController loginController = new LoginController();
    @BindView(R.id.ic_navi)
    ImageView icNavi;
    @BindView(R.id.rv_project)
    RecyclerView rvProject;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    @BindView(R.id.edt_search_project)
    EditText edtSearch;
    private Project2Adapter project2Adapter;
    private long exitTime = 0;
    private String token;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main2);
        ButterKnife.bind(this);
        initView();
        //EventBus.getDefault().register(this);
    }
    @Override
    protected void loadData() {
        initList("");
        getUserInfo();
    }

    private void initToolBar() {

    }

    @Override
    protected void initView() {
        if (getIntent()!= null){
            token = getIntent().getStringExtra("Token");
        }else {
            token = Hawk.get(CustomParam.Token);
        }
        String name = Hawk.get(CustomParam.RealName, "BIMe");
        setListener();
        initToolBar();
        initRefresh();
        initRecycleview();
        loadData();
    }

    /**
     * 初始化列表数据
     */
    private void initList(String searchStr) {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        params.put("token", token);
        params.put("keyword", searchStr);
        params.put("pageNum", "1");
        params.put("pageSize", "9999");
        ProjectController controller = new ProjectController();
        controller.getProjectList(params, new CallBack<ProjectListResult>() {
            @Override
            public void onSuccess(ProjectListResult projectListResult) {
                if (projectListResult.getRet() == 1){
                    list2.clear();
                    list2.addAll(projectListResult.getData().getRows());
                    project2Adapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
        mLoading.dismiss();
    }

    /**
     * 获取用户信息
     */

    private void getUserInfo() {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", token);
        loginController.getUserInfo(params, new CallBack<UserInforResult>() {
            @Override
            public void onSuccess(UserInforResult userInforResult) {
                if (userInforResult.getRet() == 1) {
                    if (!TextUtils.isEmpty(userInforResult.getData().getRealName())) {
                        Hawk.put(CustomParam.RealName, userInforResult.getData().getRealName());
                        Hawk.put(CustomParam.ACCOUNT, userInforResult.getData().getAccount());
                        Hawk.put(CustomParam.UserId, userInforResult.getData().getUserId());
                        Hawk.put(CustomParam.EMAIL, userInforResult.getData().getEmail());
                        Hawk.put(CustomParam.PHONE, userInforResult.getData().getAccount());

                        SetAliasUtils.setAlias(userInforResult.getData().getUserId().replace("-", "+"),
                                MainActivity.this);
                    }

                    //    LoginActivity.this);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        //为 RecyclerView 设置布局管理器
        rvProject.setLayoutManager(new LinearLayoutManager(this));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvProject.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        //动画
        rvProject.setItemAnimator(new DefaultItemAnimator());

        project2Adapter =
                new Project2Adapter(list2);

        rvProject.setAdapter(project2Adapter);

        project2Adapter.addRecycleItemListener(new Project2Adapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int position) {
                ProjectListResult.DataBean.RowsBean resultDataBean = list2.get(position);
                insertProjectParams(resultDataBean);
                Bundle bundle = new Bundle();
                bundle.putSerializable(CustomParam.PROJECT, resultDataBean);
              /*  bundle.putString(CustomParam.ProjectName, resultDataBean.getFullname());
                bundle.putString(CustomParam.ProjectId, resultDataBean.getBimcomposerid());
                bundle.putString(CustomParam.OrganizeId, resultDataBean.getOrganizeid());
                bundle.putString(CustomParam.IsPublic, String.valueOf(resultDataBean.getIspublic()));*/
                Intent intent = new Intent(MainActivity.this, ProjectActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
            }


        });
    }

    //刷新控件
    @Override
    protected void initRefresh() {
    }

    //按下返回键处理
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            //如果侧边栏打开,先关闭,然后再继续判断返回键

            if ((System.currentTimeMillis() - exitTime) > 2000) {
                showMsg("再按一次退出程序");
                exitTime = System.currentTimeMillis();
            } else {
                exitApp();
            }
            return true;
        }

        return super.onKeyDown(keyCode, event);
    }

    @OnClick({
            R.id.ic_navi, R.id.iv_message
    })
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ic_navi:
                startActivity(SettingActivity.class);
                break;
            case R.id.nav_change_pwd:
                startActivity(ModifyPasswordActivity.class);
                break;
            case R.id.nav_clean_cache:
                ClearCacheUtils.clearAllCache(MainActivity.this);
                UIUtils.postTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        OperationFileHelper.deletePath();
                        showMsg("清除缓存成功");
                    }
                }, 2000);

                break;
            case R.id.btn_exit:
                break;
            case R.id.iv_message:
                startActivity(MessageActivity.class);
                break;
        }
    }

    //添加输入框监听以及键盘搜索按键监听
    private void setListener() {
        edtSearch.setOnEditorActionListener(new OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
                if (i == EditorInfo.IME_ACTION_SEARCH) {
                    initList(edtSearch.getText().toString().trim());
                    return true;
                }
                return false;
            }
        });

    }

    /***
     * 保存项目数据到数据库
     */
    private void insertProjectParams(ProjectListResult.DataBean.RowsBean resultDataBean) {

        ProjectBeanDao dao = BaseApp.getInstance().getDao();

        //Hawk.put(CustomParam.ProjectModelSerializable,resultDataBean);
        dao.deleteAll();
        dao.insert(
                new ProjectBean(resultDataBean.getProjectId(), resultDataBean.getOrganizeId(),
                        resultDataBean.getManager(), resultDataBean.getProjectName()));
    }

 /* @Subscribe(threadMode = ThreadMode.MAIN)
  public void onMessageEvent(MessageEvent<ABC> event) {

    // do something
    Logger.t("event").e(event.getData().getTetsStr() + "helloworld");
  }

  new Thread(new Runnable() {
    @Override public void run() {
      MessageEvent<ABC> event = new MessageEvent<>(0, new ABC("testSuccess"));
      EventBusUtils.sendEvent(event);
    }
  }).start();*/

}
