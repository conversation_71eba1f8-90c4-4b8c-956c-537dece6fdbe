package com.probim.bimenew.activity;

import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.adapter.MessageRvAdapter;
import com.probim.bimenew.adapter.MessageRvAdapter.OnRecycleItemListener;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.MessageController;
import com.probim.bimenew.model.BaseModel;
import com.probim.bimenew.model.Message.DataBean.MsgArrBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.MessageResult;
import com.probim.bimenew.utils.view.AlertDialogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Description :消息界面
 * Author : Gary
 * Email  : <EMAIL>
 * Date   :2018/9/20/11:03.
 */
public class MessageActivity extends BaseActivity {

    private final int position = 1;
    private final int pageSize = 20;
    private final List<MessageResult.DataDTO.ListDTO> list = new ArrayList<>();
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.rv_message)
    RecyclerView rvMessage;
    @BindView(R.id.iv_status)
    ImageView ivStatus;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.tv_black_title)
    TextView tvTitle;
    @BindView(R.id.tv_right)
    TextView tvRight;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    private MessageRvAdapter adapter;
    private MessageController controller;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_message);
        TranslucentUtils.setTRANSLUCENT(this);
        ButterKnife.bind(this);
        initView();
        loadData();
    }

    @Override
    protected void loadData() {
        getMessageList();
    }

    @Override
    protected void initView() {
        controller = new MessageController();
        tvTitle.setText("消息中心");
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText("全部已读");
        initRecycleview();
        initRefresh();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        //为 RecyclerView 设置布局管理器
        rvMessage.setLayoutManager(new LinearLayoutManager(this));
        //为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvMessage.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        //动画
        rvMessage.setItemAnimator(new DefaultItemAnimator());

        adapter = new MessageRvAdapter(this, list);

        adapter.addRecycleItemListener(new OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                MessageResult.DataDTO.ListDTO dataBean = (MessageResult.DataDTO.ListDTO) o;
                String strContent = "我知道了！";
                switch (dataBean.getLogAndMsgType()) {
                    case 47:
                    case 48:
                    case 49:
                        strContent= "您收到一条任务流转提醒，如需处理请到网页端。";
                        break;
                    default:
                        break;
                }
                setItemRead(dataBean, pos, strContent);
            }

            @Override
            public void OnDelete(int pos, Object o) {
                MsgArrBean dataBean = (MsgArrBean) o;

                deleteMessageItem(dataBean, pos);
            }
        });

        rvMessage.setAdapter(adapter);
    }

    @Override
    protected void initRefresh() {
    /*smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
      @Override
      public void onLoadMore(RefreshLayout refreshLayout) {
        position++;
        loadData();
        refreshLayout.finishLoadMore();
      }

      @Override
      public void onRefresh(RefreshLayout refreshLayout) {
        list.clear();
        position = 1;
        loadData();
        refreshLayout.finishRefresh();
      }
    });*/
    }

    @OnClick({R.id.lin_back, R.id.tv_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.lin_back:
                finish();
                break;
            case R.id.tv_right:
                setAllRead();
                break;
            default:
                break;
        }
    }

    /**
     * 获取列表
     */

    private void getMessageList() {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        controller.GetMessageList(params, new CallBack<MessageResult>() {
            @Override
            public void onSuccess(MessageResult messageResult) {
                list.addAll(messageResult.getData().getList());
                adapter.notifyDataSetChanged();
                if (list.size() == 0 || list.isEmpty()) {
                    rvStatusLayout.setVisibility(View.VISIBLE);
                    tvStatus.setText("暂无消息");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 全部已读
     */
    private void setAllRead() {
        mLoading.show();
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", Hawk.get(CustomParam.Token));
        controller.SetMessageAllRead(params, new CallBack<BaseModel>() {
            @Override
            public void onSuccess(BaseModel baseModel) {

                if (baseModel.getRet() == 1) {
                    list.clear();
                    adapter.notifyDataSetChanged();
                    loadData();
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 单条已读
     */
    private void setItemRead(MessageResult.DataDTO.ListDTO listDTO, int dataPos, String str) {
        HashMap<String, String> params = new HashMap<>();
        params.put("mu_guids", listDTO.getMu_guid());
        controller.SetMessageItemRead(params, new CallBack<BaseModel>() {
            @Override
            public void onSuccess(BaseModel baseModel) {
                if (baseModel.getRet() == 1) {

              /*Bundle bundle = new Bundle();
              bundle.putString(CustomParam.IssueId, dataBean.getBI_IssueId());
              Intent intent = new Intent(MessageActivity.this, IssueDetailActivity.class);
              intent.putExtras(bundle);
              startActivity(intent);*/
                    list.clear();
                    adapter.notifyDataSetChanged();
                    loadData();

                    AlertDialogUtil alertDialogUtil = new AlertDialogUtil(MessageActivity.this);
                    alertDialogUtil.builder().setMsg(str).setTitle("消息").setNegativeButton("确定",
                            new OnClickListener() {
                                @Override
                                public void onClick(View view) {

                                }
                            }).show();
                } else {

                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 删除单条消息
     */

    private void deleteMessageItem(MsgArrBean dataBean, int dataPos) {
        HashMap<String, String> params = new HashMap<>();
        params.put("mu_guid", dataBean.getMu_guid());
        controller.DeleteMessageItem(params, new CallBack<BaseModel>() {
            @Override
            public void onSuccess(BaseModel baseModel) {
                if ("OK".equals(baseModel.getMsg())) {
                    list.remove(dataPos);
                    adapter.notifyItemRemoved(dataPos);
                } else {
                    showMsg(baseModel.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
}
