package com.probim.bimenew.activity.check;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.BaseActivity;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.CheckController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.dto.UserDto;
import com.probim.bimenew.event.EventBean;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.CheckResult;
import com.probim.bimenew.utils.BindEventBus;
import com.probim.bimenew.utils.GifSizeFilter;
import com.probim.bimenew.utils.StringUtils;
import com.probim.bimenew.utils.flowlayout.FlowLayout;
import com.probim.bimenew.utils.flowlayout.TagAdapter;
import com.probim.bimenew.utils.flowlayout.TagFlowLayout;
import com.probim.bimenew.utils.view.fontview.FontTextView;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.compress.CompressHelper;
import com.zhihu.matisse.compress.FileUtil;
import com.zhihu.matisse.engine.impl.GlideEngine;
import com.zhihu.matisse.filter.Filter;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * Description : 检查 复检 验收
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/08/16/14:40.
 */

@BindEventBus
public class CheckActivity extends BaseActivity implements View.OnClickListener {

    private final static String videoType = "video";
    private final static String imgType = "img";
    private final int code_photo = 1000;
    private final int code_select_zgr = 2000;
    private final int code_select_ysr = 3000;
    private final List<String> bannerList = new ArrayList<>();
    private final List<File> photoFileList = new ArrayList<>();
    private final List<UserDto> zgrList = new ArrayList<>();
    private final List<UserDto> ysrList = new ArrayList<>();
    List<String> ysrIDList = new ArrayList<>();
    List<String> zgrIdList = new ArrayList<>();
    private int selectedPosition;
    private CheckController checkController;
    private String ResultSelected = "";
    private String LevelSelected = "一般";
    private String AcceptSelected = "";
    private String organizeId;
    private String examineId;
    private int checkType;
    private boolean isSelectedPhoto;
    private TextView tvTitte;
    private FontTextView btnConfirm;
    private FontTextView tvZgrSize;
    private FontTextView tvYSrSize;
    private CheckPhotoAdapter checkPhotoAdapter;
    private LinearLayout linCheckAttention;
    private RecyclerView rvPhoto;
    private RelativeLayout rlPhoto;
    private TextView tvSelectZgrAttention;
    private TextView tvSelectYsrAttention;
    private TagFlowLayout ysrFlowLayout;
    private TagFlowLayout zgrFlowLayout;
    private TagAdapter<List<UserDto>> zgrAdapter;
    private TagAdapter<List<UserDto>> ysrAdapter;
    private EditText edtTittle;
    private LinearLayout linCheck;
    private LinearLayout linAccept;
    private FrameLayout flSelectMoreFile;
    private RelativeLayout rlCheckPhoto;
    private ImageView ivVideo;
    private RelativeLayout rlCheckVideo;
    private ImageView ivDeleteCheckVideo;
    private boolean isZgrClick;
    private boolean isYsrClick;
    private String projectId;
    private LinearLayout linSelectCheckLevel;
    private LinearLayout linSelectCheckPeople;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_check);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
        initRecycleview();
        loadData();
        Logger.t("CheckActivity").e(getTaskId() + "");
    }

    @Override
    protected void loadData() {

        if (getIntent() != null) {
            examineId = getIntent().getStringExtra(CustomParam.Examineid);
            checkType = getIntent().getIntExtra(CustomParam.CheckType, 0);
            //布局切换
            switchLayout(checkType);
            //获取数据库
            GetProjectDao();
            //获取项目所有人
        }
    }

    /**
     * 布局切换
     *
     * @param checkType
     */
    private void switchLayout(int checkType) {

        switch (checkType) {

            case 0:
                //检查
                tvTitte.setText("检查");
                btnConfirm.setText("检查");
                linAccept.setVisibility(View.GONE);
                break;
            case 1:
                //整改
                tvTitte.setText("申请复检");
                btnConfirm.setText("申请复检");
                linCheck.setVisibility(View.GONE);
                linAccept.setVisibility(View.GONE);
                break;
            case 2:
                //验收
                linCheck.setVisibility(View.GONE);
                tvTitte.setText("验收");
                btnConfirm.setText("验收");
                break;
            default:
                linCheck.setVisibility(View.GONE);
                linAccept.setVisibility(View.GONE);
                break;
        }
    }

    @Override
    protected void initView() {
        linSelectCheckLevel = findView(R.id.lin_select_check_level);
        linSelectCheckPeople = findView(R.id.lin_select_check_people);
        ivDeleteCheckVideo = findView(R.id.iv_delete_video, this);
        rlCheckVideo = findView(R.id.rl_check_video);
        ivVideo = findView(R.id.igv_check_video);
        rlCheckPhoto = findView(R.id.rl_check_photo);
        flSelectMoreFile = findView(R.id.fra_select_more_file, this);
        linCheck = findView(R.id.lin_check);
        linAccept = findView(R.id.lin_accept);
        edtTittle = findView(R.id.edt_write_title);
        tvSelectZgrAttention = findView(R.id.tv_select_zgr_attention);
        tvSelectYsrAttention = findView(R.id.tv_select_ysr_attention);
        LinearLayout linZgr = findView(R.id.lin_select_zgr, this);
        LinearLayout linYsr = findView(R.id.lin_select_ysr, this);
        tvZgrSize = findView(R.id.tv_select_zgr_attention);
        tvYSrSize = findView(R.id.tv_select_ysr_attention);
        btnConfirm = findView(R.id.btn_confirm, this);
        tvTitte = findView(R.id.tv_black_title);
        linCheckAttention = findView(R.id.lin_check_photo, this);
        LinearLayout linBack = findView(R.id.lin_back, this);
        rlPhoto = findView(R.id.rl_photo);
        RadioGroup rgSL = findView(R.id.rg_s_l);
        rgSL.check(R.id.rg_yiban);
        rgSL.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                closeKeybord();
                switch (i) {
                    case R.id.rg_yiban:
                        LevelSelected = "一般";
                        break;
                    case R.id.rg_yanzhong:
                        LevelSelected = "严重";
                        break;
                    case R.id.rg_feiyanzhong:
                        LevelSelected = "非常严重";
                        break;
                }
            }
        });
        RadioGroup rgYS = findView(R.id.rg_y_s);
        rgYS.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                closeKeybord();
                switch (i) {
                    case R.id.rg_z_g:
                        AcceptSelected = "需整改";
                        break;
                    case R.id.rg_h_g:
                        AcceptSelected = "已合格";
                        break;

                }
            }
        });

        RadioGroup rgResult = findView(R.id.rg_status);
        rgResult.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                closeKeybord();
                switch (i) {
                    case R.id.rg_hege:
                        // 如果选择了合格 隐藏整改人 验收人 严重等级
                        ResultSelected = "合格";
                        linSelectCheckPeople.setVisibility(View.GONE);
                        linSelectCheckLevel.setVisibility(View.GONE);
                        break;
                    case R.id.rg_buhege:
                        ResultSelected = "不合格";
                        linSelectCheckPeople.setVisibility(View.VISIBLE);
                        linSelectCheckLevel.setVisibility(View.VISIBLE);
                        break;
                }
            }
        });

        checkController = new CheckController();


    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {
        initZgrFlow();
        initYsrFlow();
        rvPhoto = findView(R.id.rv_check_img);
        checkPhotoAdapter = new CheckPhotoAdapter(this, bannerList);
        HorizontalNoItemRecyclerView.initialize(rvPhoto).setAdapter(checkPhotoAdapter);
        checkPhotoAdapter.addItemListener(new CheckPhotoAdapter.addItemListener() {
            @Override
            public void onItemClick(String str) {

            }
        });
    }

    @Override
    protected void initRefresh() {

    }

    /**
     * 去除重复数据
     */
    public List<String> removeStringListDupli(List<String> stringList) {
        Set<String> set = new LinkedHashSet<>();
        set.addAll(stringList);

        stringList.clear();

        stringList.addAll(set);
        return stringList;
    }

    /**
     * 打开文件
     */

    private void openFile() {

        if (!photoFileList.isEmpty()) {
            String videoUrl = photoFileList.get(0).getAbsolutePath();
            Intent intent = new Intent(this, FullScreenActivity.class);
            intent.putExtra(CustomParam.VIDEOURL, videoUrl);
            startActivity(intent);
        }


    }

    /**
     * 选取照片
     */
    private void getPhoto() {

        Set<MimeType> set = new HashSet();
        if (isSelectedPhoto) {
            set.add(MimeType.JPEG);
            set.add(MimeType.PNG);
        } else {
            set.addAll(MimeType.ofAll());
        }
        Matisse.from(this)
                .choose(set)
                .countable(true)
                .capture(true)
                .captureStrategy(
                        new CaptureStrategy(true, getPackageName()))
                .maxSelectable(9)
                .addFilter(new GifSizeFilter(320, 320, 5 * Filter.K * Filter.K))
                .gridExpectedSize(getResources().getDimensionPixelSize(R.dimen.grid_expected_size))
                .thumbnailScale(0.85f)
                .imageEngine(new GlideEngine())
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)

                .forResult(code_photo);
    }

    /**
     * 获取文件类型
     */
    private String getFileType(String filePath) {

        if (filePath.contains(".")) {

            String type = filePath.substring(filePath.lastIndexOf("."));

            if (type.equalsIgnoreCase(".mp4") || type.equalsIgnoreCase(".avi")) {

                //选取视频
                return videoType;
            } else if (type.equalsIgnoreCase(".jpg")
                    || type.equalsIgnoreCase(".jpeg")
                    || type.equalsIgnoreCase(".png")) {

                //选取图片
                return imgType;
            }
        }

        return "";
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != RESULT_OK) {
            return;
        }
        switch (requestCode) {

            case code_photo:

                for (int i = 0; i < Matisse.obtainPathResult(data).size(); i++) {

                    File fileOrigin = FileUtil.getFileByPath(Matisse.obtainPathResult(data).get(i));

                    switch (getFileType(fileOrigin.getName())) {

                        case videoType:
                            rlCheckPhoto.setVisibility(View.GONE);
                            rlCheckVideo.setVisibility(View.VISIBLE);
                            Glide
                                    .with(this)
                                    .load(Uri.fromFile(fileOrigin))
                                    .into(ivVideo);
                            photoFileList.add(fileOrigin);
                            isSelectedPhoto = false;
                            break;
                        case imgType:
                            File fileCompress = CompressHelper.getDefault(getApplicationContext())
                                    .compressToFile(fileOrigin);
                            photoFileList.add(fileCompress);
                            bannerList.add(fileCompress.getAbsolutePath());
                            isSelectedPhoto = true;
                            break;

                        default:
                            break;
                    }
                }

                if (isSelectedPhoto) {
                    rlCheckPhoto.setVisibility(View.VISIBLE);
                    rlCheckVideo.setVisibility(View.GONE);
                    flSelectMoreFile.setVisibility(View.VISIBLE);
                    linCheckAttention.setVisibility(View.GONE);
                    rlPhoto.setVisibility(View.VISIBLE);
                    checkPhotoAdapter.notifyDataSetChanged();
                }
                break;

            /*case code_select_zgr:

                List<UserDto> zgrData =
                        (List<UserDto>) data.getExtras()
                                .getSerializable(CustomParam.StartForResult);
                //人员数据
                if (!zgrData.isEmpty()) {

                    for (UserDto dto : zgrData) {

                        zgrIdList.add(dto.getUserId());

                    }
                    String str = "已选择 " + zgrData.size() + "人";
                    tvSelectZgrAttention.setText(str);
                    tvSelectZgrAttention.setTextColor(getColor(R.color.black));
                    zgrFlowLayout.setVisibility(View.VISIBLE);
                    zgrList.clear();
                    zgrList.addAll(zgrData);
                    zgrAdapter.notifyDataChanged();

                } else {
                    tvSelectZgrAttention.setText("请选择");
                    tvSelectZgrAttention.setTextColor(Color.parseColor("#CCCCCC"));
                    zgrFlowLayout.setVisibility(View.GONE);
                    zgrList.clear();

                }
                break;

            case code_select_ysr:

                List<UserDto> ysrData =
                        (List<UserDto>) data.getExtras()
                                .getSerializable(CustomParam.StartForResult);
                //人员数据
                if (!ysrData.isEmpty()) {

                    for (UserDto dto : ysrData) {

                        ysrIDList.add(dto.getUserId());

                    }
                    String str = "已选择 " + ysrData.size() + "人";
                    tvSelectYsrAttention.setText(str);
                    tvSelectYsrAttention.setTextColor(getColor(R.color.black));
                    ysrFlowLayout.setVisibility(View.VISIBLE);
                    ysrList.clear();
                    ysrList.addAll(ysrData);
                    ysrAdapter.notifyDataChanged();

                } else {
                    tvSelectYsrAttention.setText("请选择");
                    tvSelectYsrAttention.setTextColor(Color.parseColor("#CCCCCC"));
                    ysrFlowLayout.setVisibility(View.GONE);
                    ysrList.clear();

                }
                break;*/
            default:

                break;
        }
    }


    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();
            projectId = bean.getProjectID();
        }
    }


    /**
     * 对PC创建的任务进行检查操作
     * 【ExamineID】:哪个检查任务,
     * 【Token】:口令，
     * 【RectificationRemark】:填写的检查的标题,
     * 【aede_severitylevel】:一般，严重，非常严重,
     * 【IsPassed】:1为合格，否则为不合格，
     * 【RelationMemberID】:指定的整改人，英文逗号分隔userId不可为空,
     * 【PrincipalID】 指定的验收人（英文逗号分隔UserId，为空时，使用Token代表的人）
     * 【OrganizeId】项目ID
     * 【文件】
     */
    private void checkData() {
        mLoading.show();
        HashMap p = new HashMap<String, String>();
        p.put("ExamineID", examineId);
        p.put("Token", Hawk.get(CustomParam.Token));
        p.put("RectificationRemark", edtTittle.getText().toString().trim());
        String IsPassed;
        if (ResultSelected.equals("合格")) {
            IsPassed = "1";
        } else {
            IsPassed = "0";
        }
        if (!ResultSelected.equals("合格")) {
            if (TextUtils.isEmpty(LevelSelected)) {
                mLoading.dismiss();
                showMsg("严重等级为空");
                return;
            }
            p.put("aede_severitylevel", LevelSelected);
            if (zgrIdList.isEmpty()) {
                mLoading.dismiss();
                showMsg("整改人为空");
                return;
            }
            p.put("RelationMemberID", StringUtils.dataToString(zgrIdList));

        }
        p.put("IsPassed", IsPassed);
        p.put("PrincipalID", StringUtils.dataToString(ysrIDList));
        p.put("OrganizeId", projectId);
        checkController.check(photoFileList, p, new CallBack<CheckResult>() {
            @Override
            public void onSuccess(CheckResult checkResult) {
                if (checkResult.getRet() == 1) {
                    mLoading.dismiss();
                    finish();
                }
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });
    }

    /***
     *整改 复检
     */
    private void applyCheckData() {
        mLoading.show();
        HashMap p = new HashMap<String, String>();
        p.put("ExamineID", examineId);
        p.put("Token", Hawk.get(CustomParam.Token));
        p.put("RectificationRemark", edtTittle.getText().toString());
        p.put("OrganizeId", projectId);
        checkController.applyCheck(photoFileList, p, new CallBack<CheckResult>() {
            @Override
            public void onSuccess(CheckResult checkResult) {
                if (checkResult.getRet() == 1) {

                    finish();
                } else {
                    showMsg(checkResult.getMsg());
                }
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });
        mLoading.dismiss();
    }

    /***
     * 验收操作
     */
    private void acceptCheckData() {
        mLoading.show();
        HashMap p = new HashMap<String, String>();
        p.put("ExamineID", examineId);
        p.put("Token", Hawk.get(CustomParam.Token));
        p.put("RectificationRemark", edtTittle.getText().toString());

        String IsPassed;
        if (AcceptSelected.equals("已合格")) {
            IsPassed = "1";
        } else {
            IsPassed = "0";
        }
        p.put("IsPassed", IsPassed);
        p.put("OrganizeId", projectId);
        checkController.acceptCheck(photoFileList, p, new CallBack<CheckResult>() {
            @Override
            public void onSuccess(CheckResult checkResult) {
                if (checkResult.getRet() == 1) {
                    mLoading.dismiss();
                    finish();
                }
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });
    }

    @Override
    public void onClick(View v) {
        Intent intent;
        closeKeybord();
        switch (v.getId()) {
            case R.id.fra_select_more_file:
                // 选择更多文件
                getPhoto();

                break;
            case R.id.iv_delete_video:
                //删除视频文件 恢复初始布局
                photoFileList.clear();
                rlCheckVideo.setVisibility(View.GONE);
                rlCheckPhoto.setVisibility(View.VISIBLE);


                break;

            case R.id.lin_check_photo:
                getPhoto();
                break;
            case R.id.lin_back:
                finish();
                break;
            case R.id.lin_select_zgr:
                isZgrClick = true;
                intent = new Intent(this, AllPeopleMultiSelectListActivity.class);
                startActivityForResult(intent, code_select_zgr);
                break;
            case R.id.lin_select_ysr:
                isYsrClick = true;
                intent = new Intent(this, AllPeopleMultiSelectListActivity.class);
                startActivityForResult(intent, code_select_ysr);
                break;
            case R.id.btn_confirm:

                switch (checkType) {
                    case 0:
                        //检查
                        if (TextUtils.isEmpty(edtTittle.getText().toString().trim())) {

                            showMsg("内容不能为空");
                            return;
                        }  else if (TextUtils.isEmpty(ResultSelected)) {

                            showMsg("状态不能为空");
                            return;
                        }
                        //检查
                        checkData();
                        break;
                    case 1:
                        // 整改 /  复检
                        if (TextUtils.isEmpty(edtTittle.getText().toString().trim())) {

                            showMsg("内容不能为空");
                            return;
                        }
                        applyCheckData();
                        break;
                    case 2:
                        //验收
                        if (TextUtils.isEmpty(edtTittle.getText().toString().trim())) {

                            showMsg("内容不能为空");
                            return;
                        } else if (TextUtils.isEmpty(AcceptSelected)) {

                            showMsg("状态不能为空");
                            return;
                        }
                        acceptCheckData();
                        break;
                    default:
                        break;
                }

                break;

            default:
                break;
        }
    }


    /**
     * 初始化整改人数据
     */
    private void initZgrFlow() {

        zgrFlowLayout = findViewById(R.id.flow_zgr);
        zgrAdapter = new TagAdapter(zgrList) {
            @Override
            public View getView(FlowLayout parent, int position, Object o) {
                TextView tv =
                        (TextView) LayoutInflater.from(CheckActivity.this).inflate(R.layout.item_check_flowlayout,
                                parent, false);
                tv.setText(zgrList.get(position).getUserName());
                return tv;
            }
        };

        zgrFlowLayout.setAdapter(zgrAdapter);

        zgrFlowLayout.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {

                return true;
            }
        });
    }

    /**
     * 初始化验收人数据
     */
    private void initYsrFlow() {

        ysrFlowLayout = findViewById(R.id.flow_ysr);
        ysrAdapter =
                new TagAdapter(ysrList) {
                    @Override
                    public View getView(FlowLayout parent, int position, Object o) {
                        TextView tv =
                                (TextView) LayoutInflater.from(CheckActivity.this).inflate(R.layout.item_check_flowlayout,
                                        parent, false);
                        tv.setText(ysrList.get(position).getUserName());
                        return tv;
                    }
                };

        ysrFlowLayout.setAdapter(ysrAdapter);

        ysrFlowLayout.setOnTagClickListener(new TagFlowLayout.OnTagClickListener() {
            @Override
            public boolean onTagClick(View view, int position, FlowLayout parent) {

                return true;
            }
        });
    }

    /**
     * 获取选择人员
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleEvent(EventBean event) {

        if (event.getCode() == ApiConstant.CODE_TYPE_USER) {
            List<UserDto> userData =
                    (List<UserDto>) event.getData();
            if (userData.isEmpty()) {
                isYsrClick = false;
                isZgrClick = false;
                return;
            }

            // 整改人数据
            if (isZgrClick) {
                isZgrClick = false;

                for (UserDto dto : userData) {

                    zgrIdList.add(dto.getUserId());

                }
                String str = "已选择 " + userData.size() + "人";
                tvSelectZgrAttention.setText(str);
                tvSelectZgrAttention.setTextColor(getColor(R.color.black));
                zgrFlowLayout.setVisibility(View.VISIBLE);
                zgrList.clear();
                zgrList.addAll(userData);
                zgrAdapter.notifyDataChanged();


            }
            //验收人数据
            if (isYsrClick) {
                isYsrClick = false;


                for (UserDto dto : userData) {

                    ysrIDList.add(dto.getUserId());

                }
                String str = "已选择 " + userData.size() + "人";
                tvSelectYsrAttention.setText(str);
                tvSelectYsrAttention.setTextColor(getColor(R.color.black));
                ysrFlowLayout.setVisibility(View.VISIBLE);
                ysrList.clear();
                ysrList.addAll(userData);
                ysrAdapter.notifyDataChanged();

            }


        }
    }

    /**
     * 自动关闭软键盘
     */
    public void closeKeybord() {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.hideSoftInputFromWindow(getWindow().getDecorView().getWindowToken(), 0);
        }
    }


}
