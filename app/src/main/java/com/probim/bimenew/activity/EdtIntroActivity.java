package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.probim.bimenew.R;
import com.probim.bimenew.api.CustomParam;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/14/16:54.
 */
public class EdtIntroActivity extends BaseActivity {

  @BindView(R.id.lin_back)
  LinearLayout linBack;
  @BindView(R.id.tv_title)
  TextView tvTitle;
  @BindView(R.id.edt_text)
  EditText edtText;
  @BindView(R.id.tv_size)
  TextView tvSize;
  @BindView(R.id.tv_right)
  TextView tvRight;
  @BindView(R.id.tv_left)
  TextView tvLeft;
  @BindView(R.id.tv_50)
  TextView tv50;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_edt_text);
    ButterKnife.bind(this);
    initView();
  }

  @Override
  protected void loadData() {

  }

  @Override
  protected void initView() {
    tvTitle.setText("描述");
    tvRight.setVisibility(View.VISIBLE);
    tvRight.setText("完成");
    tvLeft.setText("取消");
    edtText.setHint("( 选填 ）请输入问题描述");
    if (getIntent() != null) {
      String introStr = getIntent().getStringExtra(CustomParam.IntroStr);
      edtText.setText(introStr);
    }
    setListener();
  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  @OnClick({R.id.lin_back, R.id.img_clear, R.id.tv_right})
  public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.img_clear:
        tv50.setText("/50");
        edtText.setText(null);
        break;
      case R.id.tv_right:
        Intent intent = new Intent();
        intent.putExtra(CustomParam.StartForResult, edtText.getText().toString());
        setResult(RESULT_OK, intent);
        finish();
        break;
      default:
        break;
    }
  }


  private void setListener() {

    //用户输入完毕进行size
    edtText.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

      }

      @Override
      public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

      }

      @Override
      public void afterTextChanged(Editable editable) {

        if (editable.toString().length() > 50) {

          tv50.setText("/50");
        } else {

          tvSize.setText(editable.toString().length() + "");
        }
      }
    });


  }

}
