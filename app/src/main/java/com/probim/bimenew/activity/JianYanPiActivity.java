package com.probim.bimenew.activity;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.JianYanPiAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.model.NewCheckConfig;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :分步--焚香--检验批
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/16:42.
 */

public class JianYanPiActivity extends BaseActivity {
  @BindView(R.id.iv_back) ImageView ivBack;
  @BindView(R.id.tv_left) TextView tvLeft;
  @BindView(R.id.lin_back) LinearLayout linBack;
  @BindView(R.id.tv_title) TextView tvTitle;
  @BindView(R.id.tv_right) TextView tvRight;
  @BindView(R.id.img_right) ImageView imgRight;
  @BindView(R.id.rv_step) RecyclerView rvStep;
  @BindView(R.id.iv_status) ImageView ivStatus;
  @BindView(R.id.tv_status) TextView tvStatus;
  @BindView(R.id.rv_status_layout) RelativeLayout rvStatusLayout;
  private List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX.ItemsBeanXX.ChildrenBeanXX.ItemsBeanX.ChildrenBeanX.ItemsBean>
      mList = new ArrayList<>();
  private List<String> titleList = new ArrayList<>();
  private JianYanPiAdapter adapter;
  private int page;

  @Override protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_step_step);
    ButterKnife.bind(this);
    initRecycleview();
    initView();
  }

  @Override protected void loadData() {

  }

  @Override protected void initView() {
    tvTitle.setText("检验批");
    if (getIntent() != null) {
      List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX.ItemsBeanXX.ChildrenBeanXX.ItemsBeanX.ChildrenBeanX>
          list =
          (List<NewCheckConfig.QualitysecurityBean.ChildrenBeanXXXX.ItemsBeanXXX.ChildrenBeanXXX.ItemsBeanXX.ChildrenBeanXX.ItemsBeanX.ChildrenBeanX>) getIntent()
              .getSerializableExtra(CustomParam.FenXiangSelected);

      for (int i = 0; i < list.size(); i++) {
        if (list.get(i).getTitle().equalsIgnoreCase("检验批")) {
          mList.addAll(list.get(i).getItems());
        }
      }
    }

    adapter.notifyDataSetChanged();
  }

  @Override protected void initData() {

  }

  @Override protected void initRecycleview() {

    rvStep.setLayoutManager(new LinearLayoutManager(this));

    DividerItemDecoration itemDecoration =
        new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
    rvStep.addItemDecoration(itemDecoration);

    rvStep.setItemAnimator(new DefaultItemAnimator());

    adapter = new JianYanPiAdapter(this, mList);

    adapter.addRecycleItemListener(new JianYanPiAdapter.OnRecycleItemListener() {
      @Override public void OnRecycleItemClick(int pos, Object o) {
        Hawk.put(CustomParam.JianYanPiSelected,mList.get(pos).getName());
        Hawk.put(CustomParam.JianYanPiIdPiSelected,mList.get(pos).getValue());
        finish();

      }
    });
    rvStep.setAdapter(adapter);
  }

  @Override protected void initRefresh() {

  }

  @OnClick({ R.id.lin_back, R.id.tv_right }) public void onViewClicked(View view) {
    switch (view.getId()) {
      case R.id.lin_back:
        finish();
        break;
      case R.id.tv_right:
        break;
    }
  }
}
