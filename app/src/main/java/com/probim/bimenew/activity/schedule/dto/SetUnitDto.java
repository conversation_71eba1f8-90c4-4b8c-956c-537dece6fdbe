package com.probim.bimenew.activity.schedule.dto;

import com.google.gson.annotations.SerializedName;

public class SetUnitDto {

    @SerializedName("Token")
    private String token;
    @SerializedName("UID")
    private String uID;
    @SerializedName("UnitType")
    private Integer unitType;
    @SerializedName("UnitValue")
    private String unitValue;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUID() {
        return uID;
    }

    public void setUID(String uID) {
        this.uID = uID;
    }

    public Integer getUnitType() {
        return unitType;
    }

    public void setUnitType(Integer unitType) {
        this.unitType = unitType;
    }

    public String getUnitValue() {
        return unitValue;
    }

    public void setUnitValue(String unitValue) {
        this.unitValue = unitValue;
    }
}
