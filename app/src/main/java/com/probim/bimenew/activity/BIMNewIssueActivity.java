package com.probim.bimenew.activity;

import android.annotation.SuppressLint;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.utils.translucentBars.StatusBarUtils;

import java.io.IOException;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Description :视图加载或者视点加载
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/30/17:24.
 */
public class BIMNewIssueActivity extends BaseActivity {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.lin_back)
    LinearLayout linBack;
    @BindView(R.id.tv_black_title)
    TextView tvTitle;
    @BindView(R.id.web_x)
    WebView webX;
    @BindView(R.id.tv_right)
    TextView tvRight;
    private String projectId;
    private String WEB_URL = Hawk.get(CustomParam.Web_URL);
    private String modelId;
    private String imgBase64;
    private String viewPointPosition;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_web);
        ButterKnife.bind(this);
        StatusBarUtils.transparencyBar(this);
        initWebView();
        loadData();
    }

    /**
     * 初始化webview
     */
    private void initWebView() {
        WebSettings webSetting = webX.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setJavaScriptCanOpenWindowsAutomatically(true);
        webSetting.setAllowFileAccess(true);
        webSetting.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        webSetting.setSupportZoom(true);
        webSetting.setBuiltInZoomControls(true);
        webSetting.setUseWideViewPort(true);
        webSetting.setSupportMultipleWindows(true);
        //加载js
        webX.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {

                Logger.t("网页加载完成----->").e(url);
                super.onPageFinished(view, url);
            }
        });

        webX.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                Logger.e(newProgress + "");
                mLoading.show();
                if (newProgress == 100) {

                    mLoading.dismiss();
                }
            }
        });
    }

    @Override
    protected void loadData() {
        if (getIntent() != null) {
            tvRight.setVisibility(View.GONE);
            tvTitle.setText("模型浏览");
            //读取数据
            modelId = getIntent().getStringExtra(CustomParam.ModelId);
            viewPointPosition = getIntent().getStringExtra(CustomParam.ViewPointId);
            ProjectBeanDao dao = BaseApp.getInstance().getDao();
            List<ProjectBean> beanList = dao.loadAll();
            for (ProjectBean bean : beanList) {
                projectId = bean.getProjectID();
            }

            webX.loadUrl(
                    WEB_URL + ApiConstant.LOAD_VIEW + "?projectId=" + projectId + "&model=" + modelId);
            webX.addJavascriptInterface(new getOnPageFinished(), "finishRenderToAndroid");

            //GetSessionId(viewId, modelId, viewPointId, projectId, bim365Id, projectType, projectName);
        }
    }

    @Override
    protected void initView() {

    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @SuppressLint("NewApi")
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @OnClick({R.id.iv_back, R.id.tv_left, R.id.lin_back, R.id.tv_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_left:
                finish();
                break;
            case R.id.lin_back:
                finish();
                break;
            case R.id.tv_right:
                //androigToJsHtml();
                break;
        }
    }

    /**
     * web加载js 设置视点位置
     */
    private void loadViewJs(String ps) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            String positionUrl = ps.replace("\\", "");
            //String positionUrl = "{\"cameraInof\":{\"position\":{\"x\":94.35774975326784,\"y\":154.78142933904576,\"z\":-34.08256841590779},\"rotation\":{\"_order\":\"XYZ\",\"_x\":-1.570796447118667,\"_y\":9.927347029427512e-07,\"_z\":1.6914123425112224},\"target\":{\"x\":-9.927347029425884e-07,\"y\":-0.9999999999995002,\"z\":1.2032377028070587e-07}},\"clipInof\":null,\"controlsPos\":{\"x\":94.35760116577148,\"y\":5.10650015458465,\"z\":-34.082550406455994},\"hidedElement\":[],\"isolateElelement\":[]}";
            //String str = positionUrl.replace("\\", "");
            Logger.e(positionUrl);
            String jsWebUrl =
                    "window.model.BIM365API.Context.setViewPointBasicInfo(" + positionUrl + ")";
            Logger.e(jsWebUrl);

            webX.post(new Runnable() {
                @Override
                public void run() {
                    webX.evaluateJavascript(jsWebUrl, new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String s) {

                        }
                    });
                }
            });


        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        webX.destroy();
        webX = null;
    }

    /**
     * 模型加载完成
     */
    public class getOnPageFinished extends Object {
        // 定义JS需要调用的方法
        // 被JS调用的方法必须加入@JavascriptInterface注解
        @JavascriptInterface
        public void getOnPageFinished(String msg) throws IOException {
            if (!TextUtils.isEmpty(msg)) {
                Logger.t("getOnPageFinished").e(msg);

                loadViewJs(viewPointPosition);


            }

        }


    }
}
