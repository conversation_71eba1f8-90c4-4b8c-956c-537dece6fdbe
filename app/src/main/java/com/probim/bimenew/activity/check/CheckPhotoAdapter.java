package com.probim.bimenew.activity.check;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;

import java.util.List;

public class CheckPhotoAdapter extends RecyclerView.Adapter<CheckPhotoAdapter.VHolder> {
    private final Context mContext;
    private final List<String> photoDtoList;
    private final LayoutInflater layoutInflater;
    private addItemListener mAddItemListener;

    public CheckPhotoAdapter(Context mContext, List<String> list) {
        this.mContext = mContext;
        this.photoDtoList = list;
        layoutInflater = LayoutInflater.from(mContext);

    }


    @NonNull
    @Override
    public VHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new VHolder(layoutInflater.inflate(R.layout.item_check_photo2, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VHolder vHolder, int i) {
        String url = photoDtoList.get(i);
        Glide.with(mContext).load(url).into(vHolder.igv);
        vHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mAddItemListener != null) {
                    mAddItemListener.onItemClick(url);
                }
            }
        });

    }


    @Override
    public int getItemCount() {
        return photoDtoList.size();
    }

    public void addItemListener(addItemListener addItemListener) {

        this.mAddItemListener = addItemListener;


    }

    public interface addItemListener {
        void onItemClick(String str);

    }

    public class VHolder extends RecyclerView.ViewHolder {

        private final ImageView igv;

        public VHolder(@NonNull View itemView) {
            super(itemView);
            igv = itemView.findViewById(R.id.igv_check_photo);
        }
    }
}
