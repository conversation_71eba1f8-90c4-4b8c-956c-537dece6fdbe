package com.probim.bimenew.activity.schedule.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualTypeDto;
import com.probim.bimenew.interfaces.IOnItemClickListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

public class SchedualTypeAdapter extends RecyclerView.Adapter<SchedualTypeAdapter.ViewHolder> {

    private List<SchedualTypeDto> dtoList = new ArrayList<>();
    private IOnItemClickListener onItemClickListener;

    public SchedualTypeAdapter(List<SchedualTypeDto> dtoList) {
        this.dtoList = dtoList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_all_schedual_type, null));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SchedualTypeDto dto = dtoList.get(position);
        holder.tvSchedualName.setText(dto.getName());
        holder.ivType.setImageResource(dto.getImgRes());
        if (dto.getUploadStr().equals("未提交")) {
            holder.ivisUpload.setVisibility(View.GONE);
        } else {
            holder.ivisUpload.setVisibility(View.VISIBLE);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.onClick(position, dto);
                }
            }
        });
    }

    public void setOnItemClickListener(IOnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.schedual_name)
        TextView tvSchedualName;
        @BindView(R.id.type_img)
        ImageView ivType;
        @BindView(R.id.iv_isUpload)
        ImageView ivisUpload;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
