package com.probim.bimenew.activity.schedule.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualNewPhotoDto;
import com.probim.bimenew.api.CustomParam;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :问题详情页面照片展示
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/13/15:42.
 */
public class ExamSchedualPhotoAdapter extends RecyclerView.Adapter<ExamSchedualPhotoAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private List<SchedualNewPhotoDto> mDatas = new ArrayList<>();
    private OnItemListener mItemListener;

    public ExamSchedualPhotoAdapter(List<SchedualNewPhotoDto> list, Context mContext) {
        this.mDatas = list;
        this.mContext = mContext;
        this.mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_exam_schedual_photo, null, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.setIsRecyclable(false);
        SchedualNewPhotoDto dto = mDatas.get(position);
        if (dto.getBf_guid() != null) {
            Glide.with(mContext).load(Hawk.get(CustomParam.Base_URL) + dto.getBf_path()).into(holder.imgPhoto);
            holder.edtPhotoName.setEnabled(false);
        } else {
            Glide.with(mContext).load(dto.getBf_path()).into(holder.imgPhoto);
            holder.edtPhotoName.setEnabled(true);

        }
        holder.edtPhotoName.setText(dto.getBf_filename());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mItemListener != null) {
                    mItemListener.OnItemClick(position, dto);
                }
            }
        });
        holder.imgDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mItemListener != null) {
                    mItemListener.OnItemDelete(position, dto);
                }
            }
        });
        holder.edtPhotoName.setText(dto.getBf_filename());
        holder.edtPhotoName.clearFocus();
        holder.edtPhotoName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (mItemListener != null) {
                    if (!TextUtils.isEmpty(editable.toString())) {
                        mItemListener.onEdtNameChange(position, editable.toString());
                    }

                }
            }
        });

    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addItemListener(OnItemListener onItemListener) {

        this.mItemListener = onItemListener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnItemListener<T> {

        void OnItemDelete(int pos, T o);

        void OnItemClick(int pos, T o);

        void onEdtNameChange(int pos, String s);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.img_photo)
        ImageView imgPhoto;
        @BindView(R.id.img_delete)
        ImageView imgDelete;
        @BindView(R.id.edt_photo_name)
        EditText edtPhotoName;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

 /* PhotoDto dto = mList.get(i);
    Glide.with(mContext).load(dto.getImg_url()).into(holder.imgPhoto);
    holder.imgDelete.setOnClickListener(new OnClickListener() {
    @Override
    public void onClick(View view) {
      if (mItemListener != null) {
        mItemListener.OnDelete(i, dto);
      }
    }
  });*/
}