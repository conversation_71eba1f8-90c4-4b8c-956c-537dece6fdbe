/////////////////////////////////////////////////////////////////////////////// 
// Copyright (C) 2002-2015, Open Design Alliance (the "Alliance"). 
// All rights reserved. 
// 
// This software and its documentation and related materials are owned by 
// the Alliance. The software may only be incorporated into application 
// programs owned by members of the Alliance, subject to a signed 
// Membership Agreement and Supplemental Software License Agreement with the
// Alliance. The structure and organization of this software are the valuable  
// trade secrets of the Alliance and its suppliers. The software is also 
// protected by copyright law and international treaty provisions. Application  
// programs incorporating this software must include the following statement 
// with their copyright notices:
//   
//   This application incorporates Teigha(R) software pursuant to a license 
//   agreement with Open Design Alliance.
//   Teigha(R) Copyright (C) 2002-2015 by Open Design Alliance. 
//   All rights reserved.
//
// By use of this software, its documentation or related materials, you 
// acknowledge and accept the above terms.
///////////////////////////////////////////////////////////////////////////////
package com.opendesign.android;

import android.util.Log;

public class TeighaDWGJni {

    static {
        try {
            System.loadLibrary("teigha_architecture_jni");
        } catch (UnsatisfiedLinkError use) {
            try {
                System.loadLibrary("teigha_jni");
            } catch (UnsatisfiedLinkError use1) {
                Log.e("JNI", "WARNING: Could not load libteigha*_jni.so");
            }
        }
    }

    public static native boolean init();

    public static native boolean finit();

    public static native boolean open(String file);

    public static native boolean createRenderer(int width, int height);

    public static native boolean renderFrame();

    public static native boolean destroyRenderer();

    public static native boolean close();

    public static native boolean viewTranslate(float xAxis, float yAxis);

    public static native boolean viewScale(float sCoef);

    public static native boolean viewCanRotate();

    public static native boolean viewRotate(float rAngle);

    public static native boolean viewOrbit(float xAxis, float yAxis);

    public static native int viewGetRenderMode();

    public static native boolean viewSetRenderMode(int nMode);

    public static native boolean viewRegen();

    //添加标注
    public static native int addShape(float x, float y, String imagePath, String textName, boolean isDeviation);

    //添加标注(颜色可选)
    public static native int addShapeSelect(float x, float y, String imagePath, String textName, boolean isDeviation, int[] selectArray, int colorSet);

    //屏幕坐标转图纸坐标
    public static native float[] screenToWorld(float x, float y);

    //屏幕坐标转图纸坐标
    public static native float[] worldToScreen(float x, float y, float z);

    //图指复位
    public static native boolean zoomExtents();

    //删除标注点
    public static native boolean deleteShape(long id);

    //拾取鼠标处的标注，如果有，返回标注的id
    public static native int pickShapeByCursor(float x, float y);
}
