<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2017 Zhihu Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.zhihu.matisse.internal.ui.widget.PreviewViewPager
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black" />

    <FrameLayout
        android:id="@+id/bottom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/preview_bottom_toolbar_bg"
        android:elevation="4dp"
        tools:targetApi="lollipop">

        <TextView
            android:id="@+id/button_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:foreground="?selectableItemBackground"
            android:padding="16dp"
            android:text="@string/button_back"
            android:textColor="?attr/preview.bottomToolbar.back.textColor"
            android:textSize="16sp" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">


            <LinearLayout
                android:id="@+id/originalLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:foreground="?selectableItemBackground"
                android:orientation="horizontal"
                android:padding="16dp"
                android:visibility="visible"
                tools:showIn="@layout/activity_matisse">


                <com.zhihu.matisse.internal.ui.widget.CheckRadioView
                    android:id="@+id/original"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_preview_radio_off"
                    android:tint="#ffffff" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:enabled="true"
                    android:paddingLeft="4dp"
                    android:paddingStart="4dp"
                    android:text="@string/button_original"
                    android:textColor="?attr/preview.bottomToolbar.back.textColor"
                    android:textSize="14sp" />

            </LinearLayout>


            <TextView
                android:id="@+id/size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/preview_bottom_size"
                android:textSize="16sp"
                android:visibility="gone" />
        </LinearLayout>


        <TextView
            android:id="@+id/button_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:foreground="?selectableItemBackground"
            android:padding="16dp"
            android:textColor="?attr/preview.bottomToolbar.apply.textColor"
            android:textSize="16sp" />

    </FrameLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_margin="8dp"
        android:fitsSystemWindows="true">

        <com.zhihu.matisse.internal.ui.widget.CheckView
            android:id="@+id/check_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp" />
    </FrameLayout>

</RelativeLayout>