<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="#000"
              android:orientation="vertical">

    <com.zhihu.matisse.internal.ui.widget.CropImageView
        android:id="@+id/cv_crop_image"
        android:layout_weight="1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/bottomToolbar.bg"
        android:elevation="4dp" >

        <TextView
            android:id="@+id/button_preview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:foreground="?selectableItemBackground"
            android:padding="16dp"
            android:layout_gravity="start"
            android:text="@string/button_back"
            android:textColor="?attr/bottomToolbar.preview.textColor"
            android:textSize="15sp"/>

        <TextView
            android:id="@+id/button_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:foreground="?selectableItemBackground"
            android:padding="16dp"
            android:textColor="?attr/bottomToolbar.apply.textColor"
            android:textSize="15sp"/>

    </FrameLayout>

</LinearLayout>