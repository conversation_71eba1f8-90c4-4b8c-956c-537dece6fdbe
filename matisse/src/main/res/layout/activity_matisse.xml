<?xml version="1.0" encoding="utf-8"?><!--
  Copyright 2017 Zhihu Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?colorPrimary"
        android:elevation="4dp"
        android:theme="?toolbar"
        tools:targetApi="lollipop">

        <TextView
            android:id="@+id/selected_album"
            android:layout_width="wrap_content"
            android:layout_height="?actionBarSize"
            android:drawableRight="@drawable/ic_arrow_drop_down_white_24dp"
            android:foreground="?selectableItemBackground"
            android:gravity="center"
            android:textColor="?attr/album.element.color"
            android:textSize="20sp" />
    </androidx.appcompat.widget.Toolbar>

    <FrameLayout
        android:id="@+id/bottom_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="?attr/bottomToolbar.bg"
        android:elevation="4dp"
        tools:targetApi="lollipop">

        <TextView
            android:id="@+id/button_preview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:foreground="?selectableItemBackground"
            android:padding="16dp"
            android:text="@string/button_preview"
            android:textColor="?attr/bottomToolbar.preview.textColor"
            android:textSize="16sp" />

        <LinearLayout
            android:id="@+id/originalLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:foreground="?selectableItemBackground"
            android:orientation="horizontal"
            android:padding="16dp"
            android:visibility="visible"
            tools:showIn="@layout/activity_matisse">


            <com.zhihu.matisse.internal.ui.widget.CheckRadioView
                android:id="@+id/original"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_preview_radio_off" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:enabled="true"
                android:paddingLeft="4dp"
                android:paddingStart="4dp"
                android:text="@string/button_original"
                android:textColor="?attr/bottomToolbar.preview.textColor"
                android:textSize="14sp" />

        </LinearLayout>


        <TextView
            android:id="@+id/button_apply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:foreground="?selectableItemBackground"
            android:padding="16dp"
            android:textColor="?attr/bottomToolbar.apply.textColor"
            android:textSize="16sp" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/bottom_toolbar"
        android:layout_below="@id/toolbar"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/empty_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/bottom_toolbar"
        android:layout_below="@id/toolbar"
        android:visibility="gone">

        <TextView
            android:id="@+id/empty_view_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="8dp"
            android:drawableTop="?attr/album.emptyView"
            android:gravity="center"
            android:text="@string/empty_text"
            android:textColor="?attr/album.emptyView.textColor"
            android:textSize="16sp" />

    </FrameLayout>
</RelativeLayout>