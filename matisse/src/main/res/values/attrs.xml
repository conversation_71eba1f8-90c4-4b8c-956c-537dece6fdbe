<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2017 Zhihu Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<resources>
    <attr name="toolbar" format="reference"/>
    <attr name="album.dropdown.title.color" format="reference|color"/>
    <attr name="album.dropdown.count.color" format="reference|color"/>
    <attr name="album.element.color" format="reference|color"/>
    <attr name="album.thumbnail.placeholder" format="reference|color"/>
    <attr name="album.emptyView" format="reference"/>
    <attr name="album.emptyView.textColor" format="reference|color"/>
    <attr name="listPopupWindowStyle" format="reference"/>
    <attr name="page.bg" format="reference|color"/>
    <attr name="bottomToolbar.bg" format="reference|color"/>
    <attr name="bottomToolbar.preview.textColor" format="reference|color"/>
    <attr name="bottomToolbar.apply.textColor" format="reference|color"/>
    <attr name="preview.bottomToolbar.back.textColor" format="reference|color"/>
    <attr name="preview.bottomToolbar.apply.textColor" format="reference|color"/>
    <attr name="item.placeholder" format="reference|color"/>
    <attr name="item.checkCircle.backgroundColor" format="reference|color"/>
    <attr name="item.checkCircle.borderColor" format="reference|color"/>
    <attr name="capture.textColor" format="reference|color"/>

    <!--裁剪框-->
    <declare-styleable name="CropImageView">
        <attr name="cropMaskColor" format="color"/>
        <attr name="cropBorderColor" format="color"/>
        <attr name="cropBorderWidth" format="dimension"/>
        <attr name="cropFocusWidth" format="dimension"/>
        <attr name="cropFocusHeight" format="dimension"/>
        <attr name="cropStyle" format="enum">
            <enum name="rectangle" value="0"/>
            <enum name="circle" value="1"/>
        </attr>
    </declare-styleable>
</resources>