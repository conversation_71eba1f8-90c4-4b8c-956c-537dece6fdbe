<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2017 Zhihu Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zhihu.matisse">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>

    <application>
        <activity android:name="com.zhihu.matisse.ui.MatisseActivity"/>
        <activity android:name="com.zhihu.matisse.internal.ui.AlbumPreviewActivity"/>
        <activity android:name="com.zhihu.matisse.internal.ui.SelectedPreviewActivity"/>
        <activity android:name=".ui.ImageCropActivity" />
    </application>
</manifest>